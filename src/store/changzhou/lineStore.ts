// 定义"LineCoverageMap.vue"组件中会用到的坐标数据
export const lineCoordinateData = {
  count: [
    {
      jztype: '核心DC',
      jzname: '常州钟楼区国际数据中心',
      jzypos: '31.79791977731068',
      showtype: 'big',
      region: '钟楼',
      jzxpos: '119.86985251738412',
      message: `一、核心局站<br>
                1、邮电路有3条连接光缆<br>
                二、边缘局站<br>
                1、长江路有2条连接光缆<br>
                2、常州卜弋有2条连接光缆<br>
                3、常州潞城有2条连接光缆<br>
                4、常州天宁区兰陵有1条连接光缆<br>
                5、竹林有2条连接光缆<br>
                6、常州鸣凰有2条连接光缆`,
    },
    {
      jztype: '核心DC\\汇聚层局点\\综合业务局站',
      jzname: '邮电路',
      jzypos: '31.782018956660448',
      showtype: 'big',
      region: '钟楼',
      jzxpos: '119.95056708440171',
      message: `一、核心局站<br>
                1、常州钟楼区国际数据中心有3条连接光缆<br>
                二、边缘局站<br>
                1、常州天宁区兰陵有4条连接光缆<br>
                2、竹林有3条连接光缆<br>
                3、长江路有4条连接光缆`,
    },
    {
      jztype: '边缘DC\\汇聚层局点\\综合业务局站',
      jzname: '常州鸣凰',
      jzypos: '31.67644013953789',
      showtype: 'medi',
      region: '武进',
      jzxpos: '119.93928017057335',
      message: `一、核心局站<br>
                1、常州钟楼区国际数据中心有2条连接光缆`,
    },
    {
      jztype: '边缘DC\\汇聚层局点\\综合业务局站',
      jzname: '长江路',
      jzypos: '31.830929747049034',
      showtype: 'medi',
      region: '新北',
      jzxpos: '119.97572909763082',
      message: `一、核心局站<br>
                1、邮电路有4条连接光缆<br>
                2、常州钟楼区国际数据中心有2条连接光缆<br>
                二、边缘局站<br>
                1、竹林有2条连接光缆`,
    },
    {
      jztype: '边缘DC\\汇聚层局点\\综合业务局站',
      jzname: '金坛华城',
      jzypos: '31.743190739135557',
      showtype: 'medi',
      region: '金坛',
      jzxpos: '119.59788621764399',
      message: `一、边缘局站<br>
                1、花街有5条连接光缆`,
    },
    {
      jztype: '边缘DC\\汇聚层局点\\综合业务局站',
      jzname: '常州潞城',
      jzypos: '31.760761833340908',
      showtype: 'medi',
      region: '经开',
      jzxpos: '120.04733663682451',
      message: `一、核心局站<br>
                1、常州钟楼区国际数据中心有2条连接光缆<br>
                二、边缘局站<br>
                1、竹林有1条连接光缆`,
    },
    {
      jztype: '边缘DC\\汇聚层局点\\综合业务局站',
      jzname: '竹林',
      jzypos: '31.788444750143427',
      showtype: 'medi',
      region: '天宁',
      jzxpos: '119.98824863118303',
      message: `一、核心局站<br>
                1、邮电路有3条连接光缆<br>
                2、常州钟楼区国际数据中心有2条连接光缆<br>
                二、边缘局站<br>
                1、长江路有2条连接光缆<br>
                2、常州潞城有1条连接光缆`,
    },
    {
      jztype: '边缘DC\\汇聚层局点\\综合业务局站',
      jzname: '常州天宁区兰陵',
      jzypos: '31.766341760230773',
      showtype: 'medi',
      region: '天宁',
      jzxpos: '119.94827770005234',
      message: `一、核心局站<br>
                1、邮电路有4条连接光缆<br>
                2、常州钟楼区国际数据中心有1条连接光缆`,
    },
    {
      jztype: '边缘DC\\汇聚层局点\\综合业务局站',
      jzname: '花街',
      jzypos: '31.741375137880738',
      showtype: 'medi',
      region: '金坛',
      jzxpos: '119.57641157126385',
      message: `一、边缘局站<br>
                1、金坛华城有5条连接光缆`,
    },
    {
      jztype: '边缘DC\\汇聚层局点\\综合业务局站',
      jzname: '常州溧阳昆仑局',
      jzypos: '31.447041448729482',
      showtype: 'medi',
      region: '溧阳',
      jzxpos: '119.48937419505098',
      message: `一、边缘局站<br>
                1、溧阳云计算中心有2条连接光缆`,
    },
    {
      jztype: '边缘DC\\汇聚层局点\\综合业务局站',
      jzname: '常州卜弋',
      jzypos: '31.771114702125047',
      showtype: 'medi',
      region: '钟楼',
      jzxpos: '119.80686511194385',
      message: `一、核心局站<br>
                1、常州钟楼区国际数据中心有2条连接光缆`,
    },
    {
      jztype: '边缘DC',
      jzname: '溧阳云计算中心',
      jzypos: '31.421493539635012',
      showtype: 'medi',
      region: '溧阳',
      jzxpos: '119.48254237975449',
      message: `一、边缘局站<br>
                1、常州溧阳昆仑局有2条连接光缆`,
    },
  ],
};

// 定义"LineCoverageMap.vue"组件中会用到的坐标连线数据(实线)
export const lineLinkSolidData = [
  // 常州钟楼区国际数据中心连线情况
  {
    fromName: '常州钟楼区国际数据中心',
    toName: '邮电路',
    coords: [
      [119.86985251738412, 31.79791977731068],
      [119.95056708440171, 31.782018956660448],
    ],
  },
  {
    fromName: '常州钟楼区国际数据中心',
    toName: '长江路',
    coords: [
      [119.86985251738412, 31.79791977731068],
      [119.97572909763082, 31.830929747049034],
    ],
  },
  {
    fromName: '常州钟楼区国际数据中心',
    toName: '常州卜弋',
    coords: [
      [119.86985251738412, 31.79791977731068],
      [119.80686511194385, 31.771114702125047],
    ],
  },
  {
    fromName: '常州钟楼区国际数据中心',
    toName: '常州潞城',
    coords: [
      [119.86985251738412, 31.79791977731068],
      [120.04733663682451, 31.760761833340908],
    ],
  },
  {
    fromName: '常州钟楼区国际数据中心',
    toName: '常州天宁区兰陵',
    coords: [
      [119.86985251738412, 31.79791977731068],
      [119.94827770005234, 31.766341760230773],
    ],
  },
  {
    fromName: '常州钟楼区国际数据中心',
    toName: '竹林',
    coords: [
      [119.86985251738412, 31.79791977731068],
      [119.98824863118303, 31.788444750143427],
    ],
  },
  {
    fromName: '常州钟楼区国际数据中心',
    toName: '常州鸣凰',
    coords: [
      [119.86985251738412, 31.79791977731068],
      [119.93928017057335, 31.67644013953789],
    ],
  },
  // 邮电路连线情况
  {
    fromName: '邮电路',
    toName: '常州天宁区兰陵',
    coords: [
      [119.95056708440171, 31.782018956660448],
      [119.94827770005234, 31.766341760230773],
    ],
  },
  {
    fromName: '邮电路',
    toName: '竹林',
    coords: [
      [119.95056708440171, 31.782018956660448],
      [119.98824863118303, 31.788444750143427],
    ],
  },
  {
    fromName: '邮电路',
    toName: '长江路',
    coords: [
      [119.95056708440171, 31.782018956660448],
      [119.97572909763082, 31.830929747049034],
    ],
  },
  // 长江路连线情况
  {
    fromName: '长江路',
    toName: '竹林',
    coords: [
      [119.97572909763082, 31.830929747049034],
      [119.98824863118303, 31.788444750143427],
    ],
  },
  // 常州潞城连线情况
  {
    fromName: '常州潞城',
    toName: '竹林',
    coords: [
      [120.04733663682451, 31.760761833340908],
      [119.98824863118303, 31.788444750143427],
    ],
  },
  // 金坛华城连线情况
  {
    fromName: '金坛华城',
    toName: '花街',
    coords: [
      [119.59788621764399, 31.743190739135557],
      [119.57641157126385, 31.741375137880738],
    ],
  },
  // 常州溧阳昆仑局连线情况
  {
    fromName: '常州溧阳昆仑局',
    toName: '溧阳云计算中心',
    coords: [
      [119.48937419505098, 31.447041448729482],
      [119.48254237975449, 31.421493539635012],
    ],
  },
  // 邮电路连线情况
  {
    fromName: '邮电路',
    toName: '金坛华城',
    coords: [
      [119.95056708440171, 31.782018956660448],
      [119.59788621764399, 31.743190739135557],
    ],
  },
  // 常州溧阳昆仑局连线情况
  {
    fromName: '文化宫 - 常州钟楼区国际数据中心',
    toName: '常州溧阳昆仑局',
    coords: [
      [119.86985251738412, 31.79791977731068],
      [119.48937419505098, 31.447041448729482],
    ],
  },
];

// 定义"LineCoverageMap.vue"组件中会用到的坐标连线数据(虚线)
export const lineLinkDashedData = [
  // 金坛华城连线情况
  {
    fromName: '金坛华城',
    toName: '燕山 - 溧阳云计算中心',
    coords: [
      [119.59788621764399, 31.743190739135557],
      [119.48254237975449, 31.421493539635012],
    ],
  },
  // 花街连线情况
  {
    fromName: '花街',
    toName: '燕山 - 常州溧阳昆仑局',
    coords: [
      [119.57641157126385, 31.741375137880738],
      [119.48937419505098, 31.447041448729482],
    ],
  },
];
