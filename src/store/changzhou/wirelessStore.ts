import { ref, reactive } from 'vue';
import { callChangzhouAPI } from './util';

// 定义网络规模数据的接口
interface NetworkCountItem {
  district: string;
  baseStationCount: number;
  fourGRruCount: number;
  fiveGRruCount: number;
}

interface NetworkCountResponse {
  count: NetworkCountItem[];
}

// 定义载波聚合数据的接口
interface CarrierAggregationItem {
  quantity: number;
  carrierAggregationType: string;
  region: string;
}

interface CarrierAggregationResponse {
  count: CarrierAggregationItem[];
}

// 定义无人机资源数据的接口
interface DroneResourceItem {
  airportCount: number;
  district: string;
  baseStationCount: number;
}

interface DroneResourceResponse {
  data: DroneResourceItem[];
}

// 定义物理资源数据的接口
interface PhysicalResourceItem {
  bbu5g: number;
  bbu4g: number;
  rru5g: number;
  rru4g: number;
  jizhan: number;
  zhifzhJinduan: number;
  zhifzhYuanduan: number;
  district: string;
}

interface PhysicalResourceResponse {
  count: PhysicalResourceItem[];
}

// 定义区域数据的接口
interface DistrictNetworkData {
  基站: number;
  '4GRRU': number;
  '5GRRU': number;
}

interface DistrictScenarioData {
  载波聚合2CC: number;
  载波聚合3CC: number;
}

// 定义无人机资源数据的接口
interface DistrictDroneData {
  机场: number;
  基站: number;
}

// 定义物理资源数据的接口
interface DistrictPhysicalResourceData {
  bbu5g: number;
  bbu4g: number;
  rru5g: number;
  rru4g: number;
  jizhan: number;
  zhifzhJinduan: number;
  zhifzhYuanduan: number;
}

// 定义网络数据的响应式对象类型
interface NetworkDataState {
  isLoaded: boolean;
  districtData: Record<string, DistrictNetworkData>;
  totalData: DistrictNetworkData;
}

// 定义场景数据的响应式对象类型
interface ScenarioNetworkDataState {
  isLoaded: boolean;
  districtData: Record<string, DistrictScenarioData>;
  totalData: DistrictScenarioData;
}

// 定义无人机数据的响应式对象类型
interface DroneDataState {
  isLoaded: boolean;
  districtData: Record<string, DistrictDroneData>;
  totalData: DistrictDroneData;
}

// 定义物理资源数据的响应式对象类型
interface PhysicalResourceDataState {
  isLoaded: boolean;
  districtData: Record<string, DistrictPhysicalResourceData>;
  totalData: DistrictPhysicalResourceData;
}

/**
 * 网络规模无线数量数据
 */
export const networkData = reactive<NetworkDataState>({
  isLoaded: false,
  districtData: {},
  totalData: { 基站: 0, '4GRRU': 0, '5GRRU': 0 },
});

// 加载网络规模数据
export const loadNetworkData = async (): Promise<void> => {
  try {
    const response = await callChangzhouAPI('network_scale_wuxian_count', 'V20250408162029219');
    const typedResponse = response as NetworkCountResponse;

    if (typedResponse && typedResponse.count && typedResponse.count.length > 0) {
      // 处理总数据（常州市）
      const totalItem = typedResponse.count.find((item) => item.district === '常州');
      if (totalItem) {
        networkData.totalData = {
          基站: totalItem.baseStationCount,
          '4GRRU': totalItem.fourGRruCount,
          '5GRRU': totalItem.fiveGRruCount,
        };
      }

      // 处理各区域数据
      const districtMap: Record<string, DistrictNetworkData> = {};
      typedResponse.count.forEach((item) => {
        if (item.district !== '常州') {
          districtMap[item.district] = {
            基站: item.baseStationCount,
            '4GRRU': item.fourGRruCount,
            '5GRRU': item.fiveGRruCount,
          };
        }
      });

      networkData.districtData = districtMap;
      networkData.isLoaded = true;
    }
  } catch (error) {
    console.error('加载网络规模数据失败:', error);
  }
};

// 初始加载数据
loadNetworkData();

/**
 * 载波聚合数据
 */
export const scenarioNetworkData = reactive<ScenarioNetworkDataState>({
  isLoaded: false,
  districtData: {},
  totalData: { 载波聚合2CC: 0, 载波聚合3CC: 0 },
});

// 加载载波聚合数据
export const loadScenarioData = async (): Promise<void> => {
  try {
    const response = await callChangzhouAPI('czcarrieraggregationdata', 'V20250415090944212');
    const typedResponse = response as CarrierAggregationResponse;

    if (typedResponse && typedResponse.count && typedResponse.count.length > 0) {
      // 处理总数据
      const totalData: DistrictScenarioData = { 载波聚合2CC: 0, 载波聚合3CC: 0 };
      const districtMap: Record<string, DistrictScenarioData> = {};

      typedResponse.count.forEach((item) => {
        // 处理总计数据
        if (item.region === '总计') {
          totalData[item.carrierAggregationType as keyof DistrictScenarioData] = item.quantity;
        }
        // 处理各区域数据
        else {
          if (!districtMap[item.region]) {
            districtMap[item.region] = { 载波聚合2CC: 0, 载波聚合3CC: 0 };
          }
          districtMap[item.region][item.carrierAggregationType as keyof DistrictScenarioData] =
            item.quantity;
        }
      });

      scenarioNetworkData.totalData = totalData;
      scenarioNetworkData.districtData = districtMap;
      scenarioNetworkData.isLoaded = true;
    }
  } catch (error) {
    console.error('加载载波聚合数据失败:', error);
  }
};

/**
 * 无人机资源数据
 */
export const droneData = reactive<DroneDataState>({
  isLoaded: false,
  districtData: {},
  totalData: { 机场: 0, 基站: 0 },
});

// 加载无人机资源数据
export const loadDroneData = async (): Promise<void> => {
  try {
    const response = await callChangzhouAPI('nrm.graph_cz_5g_resource_v2', 'V20250427093511976');
    const typedResponse = response as DroneResourceResponse;

    if (typedResponse && typedResponse.data && typedResponse.data.length > 0) {
      // 处理总数据（常州市）
      const totalItem = typedResponse.data.find((item) => item.district === '常州');
      if (totalItem) {
        droneData.totalData = {
          机场: totalItem.airportCount,
          基站: totalItem.baseStationCount,
        };
      }

      // 处理各区域数据
      const districtMap: Record<string, DistrictDroneData> = {};
      typedResponse.data.forEach((item) => {
        if (item.district !== '常州') {
          districtMap[item.district] = {
            机场: item.airportCount,
            基站: item.baseStationCount,
          };
        }
      });

      droneData.districtData = districtMap;
      droneData.isLoaded = true;
    }
  } catch (error) {
    console.error('加载无人机资源数据失败:', error);
  }
};

// 初始加载无人机资源数据
loadDroneData();

/**
 * 物理资源数据
 */
export const physicalResourceData = reactive<PhysicalResourceDataState>({
  isLoaded: false,
  districtData: {},
  totalData: {
    bbu5g: 0,
    bbu4g: 0,
    rru5g: 0,
    rru4g: 0,
    jizhan: 0,
    zhifzhJinduan: 0,
    zhifzhYuanduan: 0,
  },
});

// 加载物理资源数据
export const loadPhysicalResourceData = async (): Promise<void> => {
  try {
    const response = await callChangzhouAPI('wuxianphysicresourcecount', 'V20250506191348356');
    const typedResponse = response as PhysicalResourceResponse;

    if (typedResponse && typedResponse.count && typedResponse.count.length > 0) {
      // 处理总数据（常州市）
      const totalItem = typedResponse.count.find((item) => item.district === '常州市');
      if (totalItem) {
        physicalResourceData.totalData = {
          bbu5g: totalItem.bbu5g,
          bbu4g: totalItem.bbu4g,
          rru5g: totalItem.rru5g,
          rru4g: totalItem.rru4g,
          jizhan: totalItem.jizhan,
          zhifzhJinduan: totalItem.zhifzhJinduan,
          zhifzhYuanduan: totalItem.zhifzhYuanduan,
        };
      }

      // 处理各区域数据
      const districtMap: Record<string, DistrictPhysicalResourceData> = {};
      typedResponse.count.forEach((item) => {
        if (item.district !== '常州市') {
          districtMap[item.district] = {
            bbu5g: item.bbu5g,
            bbu4g: item.bbu4g,
            rru5g: item.rru5g,
            rru4g: item.rru4g,
            jizhan: item.jizhan,
            zhifzhJinduan: item.zhifzhJinduan,
            zhifzhYuanduan: item.zhifzhYuanduan,
          };
        }
      });

      physicalResourceData.districtData = districtMap;
      physicalResourceData.isLoaded = true;
    }
  } catch (error) {
    console.error('加载物理资源数据失败:', error);
  }
};

// 初始加载物理资源数据
loadPhysicalResourceData();

// 初始加载载波聚合数据
loadScenarioData();

// 当前选中的区域
export const selectedDistrict = ref<string | null>(null);

// 根据选中区域获取数据
export function getChartData(): number[] {
  if (!selectedDistrict.value) {
    return [
      networkData.totalData.基站,
      networkData.totalData['4GRRU'],
      networkData.totalData['5GRRU'],
    ];
  }

  const district = networkData.districtData[selectedDistrict.value];
  return district ? [district.基站, district['4GRRU'], district['5GRRU']] : [0, 0, 0];
}

// 根据选中区域获取场景数据
export function getScenarioData(): number[] {
  if (!selectedDistrict.value) {
    return [
      scenarioNetworkData.totalData['载波聚合2CC'],
      scenarioNetworkData.totalData['载波聚合3CC'],
    ];
  }

  const district = scenarioNetworkData.districtData[selectedDistrict.value];
  return district ? [district['载波聚合2CC'], district['载波聚合3CC']] : [0, 0];
}

// 根据选中区域获取无人机资源数据
export function getDroneData(): number[] {
  if (!selectedDistrict.value) {
    return [droneData.totalData.机场, droneData.totalData.基站];
  }

  const district = droneData.districtData[selectedDistrict.value];
  return district ? [district.机场, district.基站] : [0, 0];
}

// 根据选中区域获取物理资源数据
export function getPhysicalResourceData(): number[] {
  if (!selectedDistrict.value) {
    return [
      physicalResourceData.totalData.bbu5g,
      physicalResourceData.totalData.bbu4g,
      physicalResourceData.totalData.rru5g,
      physicalResourceData.totalData.rru4g,
      physicalResourceData.totalData.jizhan,
      physicalResourceData.totalData.zhifzhJinduan,
      physicalResourceData.totalData.zhifzhYuanduan,
    ];
  }

  const district = physicalResourceData.districtData[selectedDistrict.value];
  return district
    ? [
        district.bbu5g,
        district.bbu4g,
        district.rru5g,
        district.rru4g,
        district.jizhan,
        district.zhifzhJinduan,
        district.zhifzhYuanduan,
      ]
    : [0, 0, 0, 0, 0, 0, 0];
}

// 设置选中的区域
export function setSelectedDistrict(district: string | null): void {
  selectedDistrict.value = district;
}
