import { ref, reactive } from 'vue';
import { callChangzhouAPI } from './util';

// 定义网络规模数据的接口
interface NetworkCountItem {
  district: string;
  baseStationCount: number;
  fourGRruCount: number;
  fiveGRruCount: number;
}

interface NetworkCountResponse {
  count: NetworkCountItem[];
}

// 定义载波聚合数据的接口
interface CarrierAggregationItem {
  quantity: number;
  carrierAggregationType: string;
  region: string;
}

interface CarrierAggregationResponse {
  count: CarrierAggregationItem[];
}

// 定义无人机资源数据的接口
interface DroneResourceItem {
  airportCount: number;
  district: string;
  baseStationCount: number;
}

interface DroneResourceResponse {
  data: DroneResourceItem[];
}

// 定义物理资源数据的接口
interface PhysicalResourceItem {
  bbu5g: number;
  bbu4g: number;
  rru5g: number;
  rru4g: number;
  jizhan: number;
  zhifzhJinduan: number;
  zhifzhYuanduan: number;
  district: string;
}

interface PhysicalResourceResponse {
  count: PhysicalResourceItem[];
}

// 定义区域数据的接口
interface DistrictNetworkData {
  基站: number;
  '4GRRU': number;
  '5GRRU': number;
}

interface DistrictScenarioData {
  载波聚合2CC: number;
  载波聚合3CC: number;
}

// 定义无人机资源数据的接口
interface DistrictDroneData {
  机场: number;
  基站: number;
}

// 定义物理资源数据的接口
interface DistrictPhysicalResourceData {
  bbu5g: number;
  bbu4g: number;
  rru5g: number;
  rru4g: number;
  jizhan: number;
  zhifzhJinduan: number;
  zhifzhYuanduan: number;
}

// 定义网络数据的响应式对象类型
interface NetworkDataState {
  isLoaded: boolean;
  districtData: Record<string, DistrictNetworkData>;
  totalData: DistrictNetworkData;
}

// 定义场景数据的响应式对象类型
interface ScenarioNetworkDataState {
  isLoaded: boolean;
  districtData: Record<string, DistrictScenarioData>;
  totalData: DistrictScenarioData;
}

// 定义无人机数据的响应式对象类型
interface DroneDataState {
  isLoaded: boolean;
  districtData: Record<string, DistrictDroneData>;
  totalData: DistrictDroneData;
}

// 定义物理资源数据的响应式对象类型
interface PhysicalResourceDataState {
  isLoaded: boolean;
  districtData: Record<string, DistrictPhysicalResourceData>;
  totalData: DistrictPhysicalResourceData;
}

// 定义电信联通共享4G数据的接口
interface TelecomUnicomSharingItem {
  dxtoltCells: number; // 电信共享给联通4G小区数量
  dxsharingRatio: number; // 电信4G共享比例（共享小区数/总小区数）
  dxliuliangratiodx: number; // 电信共享小区电信流量占比
  dxliuliangratiolt: number; // 电信共享小区联通流量占比
  lttodxCells: number; // 联通共享给电信4G小区数量
  ltsharingRatio: number; // 联通4G共享比例（共享小区数/总小区数）
  ltliuliangratiodx: number; // 联通共享小区电信流量占比
  ltliuliangratiolt: number; // 联通共享小区联通流量占比
  district: string; // 区域名称
}

interface TelecomUnicomSharingResponse {
  count: TelecomUnicomSharingItem[];
}

// 定义区域共享数据接口
interface DistrictTelecomUnicomData {
  dxtoltCells: number; // 电信共享给联通4G小区数量
  dxsharingRatio: number; // 电信4G共享比例
  dxliuliangratiodx: number; // 电信共享小区电信流量占比
  dxliuliangratiolt: number; // 电信共享小区联通流量占比
  lttodxCells: number; // 联通共享给电信4G小区数量
  ltsharingRatio: number; // 联通4G共享比例
  ltliuliangratiodx: number; // 联通共享小区电信流量占比
  ltliuliangratiolt: number; // 联通共享小区联通流量占比
}

// 定义共享数据的响应式对象类型
interface TelecomUnicomDataState {
  isLoaded: boolean;
  districtData: Record<string, DistrictTelecomUnicomData>;
  totalData: DistrictTelecomUnicomData;
}

// 定义业务负荷数据的接口
interface BusinessLoadItem {
  busyCells5g: number; // 5G超忙小区数
  busyCells4g: number; // 4G超忙小区数
  busyCellsRatio5g: number; // 5G超忙小区占比
  busyCellsRatio4g: number; // 4G超忙小区占比
  prbUtil5g: number; // 5G忙时PRB利用率
  prbUtil4g: number; // 4G忙时PRB利用率
  district: string; // 区域名称
}

interface BusinessLoadResponse {
  count: BusinessLoadItem[];
}

// 定义区域共享数据接口
interface DistrictBusinessLoadData {
  busyCells5g: number; // 5G超忙小区数
  busyCells4g: number; // 4G超忙小区数
  busyCellsRatio5g: number; // 5G超忙小区占比
  busyCellsRatio4g: number; // 4G超忙小区占比
  prbUtil5g: number; // 5G忙时PRB利用率
  prbUtil4g: number; // 4G忙时PRB利用率
}

// 定义共享数据的响应式对象类型
interface BusinessLoadDataState {
  isLoaded: boolean;
  districtData: Record<string, DistrictBusinessLoadData>;
  totalData: DistrictBusinessLoadData;
}

/**
 * 网络规模无线数量数据
 */
export const networkData = reactive<NetworkDataState>({
  isLoaded: false,
  districtData: {},
  totalData: { 基站: 0, '4GRRU': 0, '5GRRU': 0 },
});

// 加载网络规模数据
export const loadNetworkData = async (): Promise<void> => {
  try {
    const response = await callChangzhouAPI('network_scale_wuxian_count', 'V20250408162029219');
    const typedResponse = response as NetworkCountResponse;

    if (typedResponse && typedResponse.count && typedResponse.count.length > 0) {
      // 处理总数据（常州市）
      const totalItem = typedResponse.count.find((item) => item.district === '常州');
      if (totalItem) {
        networkData.totalData = {
          基站: totalItem.baseStationCount,
          '4GRRU': totalItem.fourGRruCount,
          '5GRRU': totalItem.fiveGRruCount,
        };
      }

      // 处理各区域数据
      const districtMap: Record<string, DistrictNetworkData> = {};
      typedResponse.count.forEach((item) => {
        if (item.district !== '常州') {
          districtMap[item.district] = {
            基站: item.baseStationCount,
            '4GRRU': item.fourGRruCount,
            '5GRRU': item.fiveGRruCount,
          };
        }
      });

      networkData.districtData = districtMap;
      networkData.isLoaded = true;
    }
  } catch (error) {
    console.error('加载网络规模数据失败:', error);
  }
};

// 初始加载数据
loadNetworkData();

/**
 * 载波聚合数据
 */
export const scenarioNetworkData = reactive<ScenarioNetworkDataState>({
  isLoaded: false,
  districtData: {},
  totalData: { 载波聚合2CC: 0, 载波聚合3CC: 0 },
});

// 加载载波聚合数据
export const loadScenarioData = async (): Promise<void> => {
  try {
    const response = await callChangzhouAPI('czcarrieraggregationdata', 'V20250415090944212');
    const typedResponse = response as CarrierAggregationResponse;

    if (typedResponse && typedResponse.count && typedResponse.count.length > 0) {
      // 处理总数据
      const totalData: DistrictScenarioData = { 载波聚合2CC: 0, 载波聚合3CC: 0 };
      const districtMap: Record<string, DistrictScenarioData> = {};

      typedResponse.count.forEach((item) => {
        // 处理总计数据
        if (item.region === '总计') {
          totalData[item.carrierAggregationType as keyof DistrictScenarioData] = item.quantity;
        }
        // 处理各区域数据
        else {
          if (!districtMap[item.region]) {
            districtMap[item.region] = { 载波聚合2CC: 0, 载波聚合3CC: 0 };
          }
          districtMap[item.region][item.carrierAggregationType as keyof DistrictScenarioData] =
            item.quantity;
        }
      });

      scenarioNetworkData.totalData = totalData;
      scenarioNetworkData.districtData = districtMap;
      scenarioNetworkData.isLoaded = true;
    }
  } catch (error) {
    console.error('加载载波聚合数据失败:', error);
  }
};

/**
 * 无人机资源数据
 */
export const droneData = reactive<DroneDataState>({
  isLoaded: false,
  districtData: {},
  totalData: { 机场: 0, 基站: 0 },
});

// 加载无人机资源数据
export const loadDroneData = async (): Promise<void> => {
  try {
    const response = await callChangzhouAPI('nrm.graph_cz_5g_resource_v2', 'V20250427093511976');
    const typedResponse = response as DroneResourceResponse;

    if (typedResponse && typedResponse.data && typedResponse.data.length > 0) {
      // 处理总数据（常州市）
      const totalItem = typedResponse.data.find((item) => item.district === '常州');
      if (totalItem) {
        droneData.totalData = {
          机场: totalItem.airportCount,
          基站: totalItem.baseStationCount,
        };
      }

      // 处理各区域数据
      const districtMap: Record<string, DistrictDroneData> = {};
      typedResponse.data.forEach((item) => {
        if (item.district !== '常州') {
          districtMap[item.district] = {
            机场: item.airportCount,
            基站: item.baseStationCount,
          };
        }
      });

      droneData.districtData = districtMap;
      droneData.isLoaded = true;
    }
  } catch (error) {
    console.error('加载无人机资源数据失败:', error);
  }
};

// 初始加载无人机资源数据
loadDroneData();

/**
 * 物理资源数据
 */
export const physicalResourceData = reactive<PhysicalResourceDataState>({
  isLoaded: false,
  districtData: {},
  totalData: {
    bbu5g: 0,
    bbu4g: 0,
    rru5g: 0,
    rru4g: 0,
    jizhan: 0,
    zhifzhJinduan: 0,
    zhifzhYuanduan: 0,
  },
});

// 加载物理资源数据
export const loadPhysicalResourceData = async (): Promise<void> => {
  try {
    const response = await callChangzhouAPI('wuxianphysicresourcecount', 'V20250506191348356');
    const typedResponse = response as PhysicalResourceResponse;

    if (typedResponse && typedResponse.count && typedResponse.count.length > 0) {
      // 处理总数据（常州市）
      const totalItem = typedResponse.count.find((item) => item.district === '常州市');
      if (totalItem) {
        physicalResourceData.totalData = {
          bbu5g: totalItem.bbu5g,
          bbu4g: totalItem.bbu4g,
          rru5g: totalItem.rru5g,
          rru4g: totalItem.rru4g,
          jizhan: totalItem.jizhan,
          zhifzhJinduan: totalItem.zhifzhJinduan,
          zhifzhYuanduan: totalItem.zhifzhYuanduan,
        };
      }

      // 处理各区域数据
      const districtMap: Record<string, DistrictPhysicalResourceData> = {};
      typedResponse.count.forEach((item) => {
        if (item.district !== '常州市') {
          districtMap[item.district] = {
            bbu5g: item.bbu5g,
            bbu4g: item.bbu4g,
            rru5g: item.rru5g,
            rru4g: item.rru4g,
            jizhan: item.jizhan,
            zhifzhJinduan: item.zhifzhJinduan,
            zhifzhYuanduan: item.zhifzhYuanduan,
          };
        }
      });

      physicalResourceData.districtData = districtMap;
      physicalResourceData.isLoaded = true;
    }
  } catch (error) {
    console.error('加载物理资源数据失败:', error);
  }
};

// 初始加载物理资源数据
loadPhysicalResourceData();

// 初始加载载波聚合数据
loadScenarioData();

// 当前选中的区域
export const selectedDistrict = ref<string | null>(null);

// 根据选中区域获取数据
export function getChartData(): number[] {
  if (!selectedDistrict.value) {
    return [
      networkData.totalData.基站,
      networkData.totalData['4GRRU'],
      networkData.totalData['5GRRU'],
    ];
  }

  const district = networkData.districtData[selectedDistrict.value];
  return district ? [district.基站, district['4GRRU'], district['5GRRU']] : [0, 0, 0];
}

// 根据选中区域获取场景数据
export function getScenarioData(): number[] {
  if (!selectedDistrict.value) {
    return [
      scenarioNetworkData.totalData['载波聚合2CC'],
      scenarioNetworkData.totalData['载波聚合3CC'],
    ];
  }

  const district = scenarioNetworkData.districtData[selectedDistrict.value];
  return district ? [district['载波聚合2CC'], district['载波聚合3CC']] : [0, 0];
}

// 根据选中区域获取无人机资源数据
export function getDroneData(): number[] {
  if (!selectedDistrict.value) {
    return [droneData.totalData.机场, droneData.totalData.基站];
  }

  const district = droneData.districtData[selectedDistrict.value];
  return district ? [district.机场, district.基站] : [0, 0];
}

// 根据选中区域获取物理资源数据
export function getPhysicalResourceData(): number[] {
  if (!selectedDistrict.value) {
    return [
      physicalResourceData.totalData.bbu5g,
      physicalResourceData.totalData.bbu4g,
      physicalResourceData.totalData.rru5g,
      physicalResourceData.totalData.rru4g,
      physicalResourceData.totalData.jizhan,
      physicalResourceData.totalData.zhifzhJinduan,
      physicalResourceData.totalData.zhifzhYuanduan,
    ];
  }

  const district = physicalResourceData.districtData[selectedDistrict.value];
  return district
    ? [
        district.bbu5g,
        district.bbu4g,
        district.rru5g,
        district.rru4g,
        district.jizhan,
        district.zhifzhJinduan,
        district.zhifzhYuanduan,
      ]
    : [0, 0, 0, 0, 0, 0, 0];
}

// 设置选中的区域
export function setSelectedDistrict(district: string | null): void {
  selectedDistrict.value = district;
}

/**
 * 电信联通共享4G数据
 */
export const telecomUnicomData = reactive<TelecomUnicomDataState>({
  isLoaded: false,
  districtData: {},
  totalData: {
    dxtoltCells: 0,
    dxsharingRatio: 0,
    dxliuliangratiodx: 0,
    dxliuliangratiolt: 0,
    lttodxCells: 0,
    ltsharingRatio: 0,
    ltliuliangratiodx: 0,
    ltliuliangratiolt: 0,
  },
});

// 加载电信联通共享4G数据
export const loadTelecomUnicomData = async (): Promise<void> => {
  try {
    const response = await callChangzhouAPI('dianliangongxiang4Gcount', 'V20250506192307026');
    const typedResponse = response as TelecomUnicomSharingResponse;

    if (typedResponse && typedResponse.count && typedResponse.count.length > 0) {
      // 处理总数据（常州市）
      const totalItem = typedResponse.count.find((item) => item.district === '常州市');
      if (totalItem) {
        telecomUnicomData.totalData = {
          dxtoltCells: totalItem.dxtoltCells,
          dxsharingRatio: totalItem.dxsharingRatio,
          dxliuliangratiodx: totalItem.dxliuliangratiodx,
          dxliuliangratiolt: totalItem.dxliuliangratiolt,
          lttodxCells: totalItem.lttodxCells,
          ltsharingRatio: totalItem.ltsharingRatio,
          ltliuliangratiodx: totalItem.ltliuliangratiodx,
          ltliuliangratiolt: totalItem.ltliuliangratiolt,
        };
      }

      // 处理各区域数据
      const districtMap: Record<string, DistrictTelecomUnicomData> = {};
      typedResponse.count.forEach((item) => {
        if (item.district !== '常州市') {
          districtMap[item.district] = {
            dxtoltCells: item.dxtoltCells,
            dxsharingRatio: item.dxsharingRatio,
            dxliuliangratiodx: item.dxliuliangratiodx,
            dxliuliangratiolt: item.dxliuliangratiolt,
            lttodxCells: item.lttodxCells,
            ltsharingRatio: item.ltsharingRatio,
            ltliuliangratiodx: item.ltliuliangratiodx,
            ltliuliangratiolt: item.ltliuliangratiolt,
          };
        }
      });

      telecomUnicomData.districtData = districtMap;
      telecomUnicomData.isLoaded = true;
    }
  } catch (error) {
    console.error('加载电信联通共享4G数据失败:', error);
  }
};

// 初始加载电信联通共享4G数据
loadTelecomUnicomData();

// 根据选中区域获取电信联通共享4G数据
export function getTelecomUnicomData(): DistrictTelecomUnicomData {
  if (!selectedDistrict.value) {
    return telecomUnicomData.totalData;
  }

  const district = telecomUnicomData.districtData[selectedDistrict.value];
  return district || telecomUnicomData.totalData;
}

/**
 * 业务负荷数据
 */
export const businessLoadData = reactive<BusinessLoadDataState>({
  isLoaded: false,
  districtData: {},
  totalData: {
    busyCells5g: 0,
    busyCells4g: 0,
    busyCellsRatio5g: 0,
    busyCellsRatio4g: 0,
    prbUtil5g: 0,
    prbUtil4g: 0,
  },
});

// 加载业务负荷数据
export const loadBusinessLoadData = async (): Promise<void> => {
  try {
    const response = await callChangzhouAPI('wxyewufuhecount', 'V20250506191648615');
    const typedResponse = response as BusinessLoadResponse;

    if (typedResponse && typedResponse.count && typedResponse.count.length > 0) {
      // 处理总数据（常州市）
      const totalItem = typedResponse.count.find((item) => item.district === '常州市');
      if (totalItem) {
        businessLoadData.totalData = {
          busyCells5g: totalItem.busyCells5g,
          busyCells4g: totalItem.busyCells4g,
          busyCellsRatio5g: totalItem.busyCellsRatio5g,
          busyCellsRatio4g: totalItem.busyCellsRatio4g,
          prbUtil5g: totalItem.prbUtil5g,
          prbUtil4g: totalItem.prbUtil4g,
        };
      }

      // 处理各区域数据
      const districtMap: Record<string, DistrictBusinessLoadData> = {};
      typedResponse.count.forEach((item) => {
        if (item.district !== '常州市') {
          districtMap[item.district] = {
            busyCells5g: item.busyCells5g,
            busyCells4g: item.busyCells4g,
            busyCellsRatio5g: item.busyCellsRatio5g,
            busyCellsRatio4g: item.busyCellsRatio4g,
            prbUtil5g: item.prbUtil5g,
            prbUtil4g: item.prbUtil4g,
          };
        }
      });

      businessLoadData.districtData = districtMap;
      businessLoadData.isLoaded = true;
    }
  } catch (error) {
    console.error('加载业务负荷数据失败:', error);
  }
};

// 初始加载业务负荷数据
loadBusinessLoadData();

// 根据选中区域获取业务负荷数据
export function getBusinessLoadData(): DistrictBusinessLoadData {
  if (!selectedDistrict.value) {
    return businessLoadData.totalData;
  }

  const district = businessLoadData.districtData[selectedDistrict.value];
  return district || businessLoadData.totalData;
}
