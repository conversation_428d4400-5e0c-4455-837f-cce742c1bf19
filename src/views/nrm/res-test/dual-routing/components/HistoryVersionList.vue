<template>
  <div class="history-version-list-container p-6 bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-800">历史版本清单</h1>
          <p class="text-gray-600 mt-2">查看和管理双路由保护组的历史版本记录</p>
        </div>

        <!-- - 只有省级用户可见 -->
        <div  class="flex space-x-4">
          <a-button
            type="primary"
            size="large"
            @click="handleGoToStatistics"
            class="bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700"
          >
            <template #icon>
              <BarChartOutlined />
            </template>
            省级统计分析
          </a-button>
        </div>
      </div>
    </div>

    <!-- 筛选条件区域 -->
    <a-card class="filter-card mb-6">
      <template #title>
        <div class="flex items-center justify-between">
          <span>筛选条件</span>
          <a-button
            type="link"
            size="small"
            @click="toggleFilterExpanded"
            class="text-blue-500"
          >
            {{ filterExpanded ? '收起' : '展开' }}
            <template #icon>
              <UpOutlined v-if="filterExpanded" />
              <DownOutlined v-else />
            </template>
          </a-button>
        </div>
      </template>

      <a-form
        :model="filterForm"
        layout="inline"
        @finish="handleSearch"
        class="filter-form"
      >
        <!-- 常用筛选条件 - 始终显示 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-4">
          <!-- 保护组ID -->
          <a-form-item label="保护组ID" name="protectionGroupId">
            <a-input
              v-model:value="filterForm.protectionGroupId"
              placeholder="请输入保护组ID"
              allow-clear
            />
          </a-form-item>

          <!-- 场景类型 - 美观的卡片式选择器 -->
          <a-form-item label="场景类型" name="scenarioType">
            <a-select
              v-model:value="filterForm.scenarioType"
              placeholder="请选择场景类型"
              allow-clear
              size="default"
            >
              <a-select-option value="singleDeviceDualUplink">
                <div class="flex items-center">
                  <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  单设备双上联
                </div>
              </a-select-option>
              <a-select-option value="dualDeviceDualRoute">
                <div class="flex items-center">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  设备对双路由
                </div>
              </a-select-option>
              <a-select-option value="dualDeviceTripleRoute">
                <div class="flex items-center">
                  <span class="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                  设备对三路由
                </div>
              </a-select-option>
              <a-select-option value="ringNetwork">
                <div class="flex items-center">
                  <span class="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                  环形组网
                </div>
              </a-select-option>
            </a-select>
          </a-form-item>

          <!-- 城市代码 -->
          <a-form-item label="城市代码" name="cityCode">
            <a-select
              v-model:value="filterForm.cityCode"
              placeholder="请选择城市"
              allow-clear
              @change="handleCityChange"
            >
              <a-select-option value="NJ">南京</a-select-option>
              <a-select-option value="WX">无锡</a-select-option>
              <a-select-option value="XZ">徐州</a-select-option>
              <a-select-option value="CZ">常州</a-select-option>
              <a-select-option value="SZ">苏州</a-select-option>
              <a-select-option value="NT">南通</a-select-option>
              <a-select-option value="LYG">连云港</a-select-option>
              <a-select-option value="HA">淮安</a-select-option>
              <a-select-option value="YC">盐城</a-select-option>
              <a-select-option value="YZ">扬州</a-select-option>
              <a-select-option value="ZJ">镇江</a-select-option>
              <a-select-option value="TZ">泰州</a-select-option>
              <a-select-option value="SQ">宿迁</a-select-option>
            </a-select>
          </a-form-item>

          <!-- 版本备注（支持模糊查询） -->
          <a-form-item label="版本备注" name="description">
            <a-input
              v-model:value="filterForm.description"
              placeholder="请输入版本备注（支持模糊查询）"
              allow-clear
            />
          </a-form-item>
        </div>

        <!-- 高级筛选条件 - 可展开/收起 -->
        <a-collapse-transition>
          <div v-show="filterExpanded">
            <a-divider class="my-4" />

            <!-- 设备相关 -->
            <div class="mb-4">
              <h3 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                <span class="w-1 h-4 bg-blue-500 mr-2"></span>
                设备相关
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                <!-- 设备编码 -->
                <a-form-item label="设备编码" name="deviceCode">
                  <a-input
                    v-model:value="filterForm.deviceCode"
                    placeholder="请输入设备编码"
                    allow-clear
                  />
                </a-form-item>

                <!-- 设备对ID -->
                <a-form-item label="设备对ID" name="devicePairId">
                  <a-input
                    v-model:value="filterForm.devicePairId"
                    placeholder="请输入设备对ID"
                    allow-clear
                  />
                </a-form-item>

                <!-- B1设备编码 -->
                <a-form-item label="B1设备编码" name="b1Code">
                  <a-input
                    v-model:value="filterForm.b1Code"
                    placeholder="请输入B1设备编码"
                    allow-clear
                  />
                </a-form-item>

                <!-- B2设备编码 -->
                <a-form-item label="B2设备编码" name="b2Code">
                  <a-input
                    v-model:value="filterForm.b2Code"
                    placeholder="请输入B2设备编码"
                    allow-clear
                  />
                </a-form-item>
              </div>
            </div>

            <!-- 环网相关 -->
            <div class="mb-4">
              <h3 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                <span class="w-1 h-4 bg-green-500 mr-2"></span>
                环网相关
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                <!-- 环号 -->
                <a-form-item label="环号" name="ringNumber">
                  <a-input
                    v-model:value="filterForm.ringNumber"
                    placeholder="请输入环号"
                    allow-clear
                  />
                </a-form-item>
              </div>
            </div>

            <!-- 地理位置 -->
            <div class="mb-4">
              <h3 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                <span class="w-1 h-4 bg-orange-500 mr-2"></span>
                地理位置
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                <!-- 区域代码（支持模糊查询） -->
                <a-form-item label="区域代码" name="areaCode">
                  <a-input
                    v-model:value="filterForm.areaCode"
                    placeholder="请输入区域代码（支持模糊查询）"
                    allow-clear
                  />
                </a-form-item>
              </div>
            </div>

            <!-- 时间范围 -->
            <div class="mb-4">
              <h3 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                <span class="w-1 h-4 bg-purple-500 mr-2"></span>
                时间范围
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                <!-- 开始时间 -->
                <a-form-item label="开始时间" name="startTime">
                  <a-date-picker
                    v-model:value="filterForm.startTime"
                    placeholder="请选择开始时间"
                    show-time
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 100%"
                  />
                </a-form-item>

                <!-- 结束时间 -->
                <a-form-item label="结束时间" name="endTime">
                  <a-date-picker
                    v-model:value="filterForm.endTime"
                    placeholder="请选择结束时间"
                    show-time
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 100%"
                  />
                </a-form-item>
              </div>
            </div>
          </div>
        </a-collapse-transition>

        <!-- 操作按钮 -->
        <div class="flex justify-end mt-4 space-x-4">
          <a-button @click="handleReset">
            重置
          </a-button>
          <a-button type="primary" html-type="submit" :loading="loading">
            查询
          </a-button>
        </div>
      </a-form>
    </a-card>

    <!-- 数据表格区域 -->
    <a-card class="table-card" title="历史版本列表">
      <template #extra>
        <div class="flex items-center space-x-4">
          <!-- <span class="text-sm text-gray-600">
            共 {{ pagination.total }} 条记录
          </span> -->
          <a-button @click="handleRefresh" :loading="loading">
            刷新
          </a-button>
        </div>
      </template>

      <a-table
        :dataSource="dataSource"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: 1600 }"
        size="middle"
        :rowKey="record => record.id"
        @change="handleTableChange"
        class="history-version-table"
      >
        <!-- 自定义列渲染 -->
        <template #bodyCell="{ column, record }">
          <!-- 场景类型列 -->
          <template v-if="column.key === 'scenario_type'">
            <div class="flex items-center">
              <span
                class="w-2 h-2 rounded-full mr-2"
                :class="{
                  'bg-blue-500': record.scenario_type === 'singleDeviceDualUplink',
                  'bg-green-500': record.scenario_type === 'dualDeviceDualRoute',
                  'bg-orange-500': record.scenario_type === 'dualDeviceTripleRoute',
                  'bg-purple-500': record.scenario_type === 'ringNetwork',
                  'bg-gray-400': !record.scenario_type
                }"
              ></span>
              <span>{{ getScenarioTypeLabel(record.scenario_type) }}</span>
            </div>
          </template>

          <!-- 版本备注列 -->
          <template v-else-if="column.key === 'description'">
            <div class="max-w-xs">
              <a-tooltip :title="record.description" placement="topLeft">
                <span class="truncate block">{{ record.description || '无备注' }}</span>
              </a-tooltip>
            </div>
          </template>

          <!-- 创建时间列 -->
          <template v-else-if="column.key === 'create_time'">
            <span>{{ formatDateTime(record.create_time) }}</span>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'action'">
            <div class="flex space-x-2">
              <a-button
                type="primary"
                size="small"
                @click="handleViewDetail(record)"
              >
                查看详情
              </a-button>

              <a-button
                type="default"
                size="small"
                @click="handleEditDescription(record)"
              >
                修改备注
              </a-button>

              <!-- 只有创建人才能删除 -->
              <a-button
                v-if="canDelete(record)"
                type="primary"
                danger
                size="small"
                @click="handleDelete(record)"
              >
                删除
              </a-button>
            </div>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 修改备注模态框 -->
    <a-modal
      v-model:open="editDescriptionVisible"
      title="修改版本备注"
      @ok="handleEditDescriptionOk"
      @cancel="handleEditDescriptionCancel"
      :confirm-loading="editDescriptionLoading"
      width="500px"
    >
      <a-form
        :model="editDescriptionForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="版本ID">
          <a-input :value="editDescriptionForm.id" disabled />
        </a-form-item>

        <a-form-item
          label="版本备注"
          name="description"
          :rules="[{ required: true, message: '请输入版本备注' }]"
        >
          <a-textarea
            v-model:value="editDescriptionForm.description"
            placeholder="请输入版本备注"
            :rows="4"
            :maxlength="500"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { UpOutlined, DownOutlined, BarChartOutlined } from '@ant-design/icons-vue';
import { useUnifiedApi } from '@/hooks/web/useUnifiedApi';
import { useUserStoreWithOut } from '@/store/modules/user';
import dayjs from 'dayjs';

defineOptions({
  name: 'HistoryVersionList'
});

const router = useRouter();

// 获取用户存储
const userStore = useUserStoreWithOut();
const areaCode = userStore.getAreaCode;

// 如果是江苏，默认查询无锡的数据，否则使用用户所在地市
const defaultCityCode = areaCode === 'js' ? 'WX' : areaCode;

// 检测是否为省级用户（江苏省级用户）
const isProvinceUser = computed(() => {
  return areaCode === 'js';
});

// 初始化统一API调用钩子
const unifiedApi = useUnifiedApi({
  rootPath: '/graph-rest-api',
  defaultCityCode: defaultCityCode
});

// 加载状态
const loading = ref(false);

// 筛选条件展开状态
const filterExpanded = ref(false);

// 修改备注相关状态
const editDescriptionVisible = ref(false);
const editDescriptionLoading = ref(false);
const editDescriptionForm = reactive({
  id: '',
  description: ''
});

// 筛选表单
const filterForm = reactive({
  protectionGroupId: '',
  scenarioType: '',
  deviceCode: '',
  devicePairId: '',
  ringNumber: '',
  cityCode: defaultCityCode,
  areaCode: '',
  b1Code: '',
  b2Code: '',
  description: '',
  startTime: '',
  endTime: ''
});

// 数据源
const dataSource = ref<any[]>([]);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => {
    const totalPages = Math.ceil(total / pagination.pageSize);
    return `共 ${total} 条记录`;
  },
  pageSizeOptions: ['10', '20', '50', '100']
});

// 表格列配置
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
    fixed: 'left'
  },
  {
    title: '保护组ID',
    dataIndex: 'protection_group_id',
    key: 'protection_group_id',
    width: 150,
    ellipsis: true
  },
  {
    title: '场景类型',
    dataIndex: 'scenario_type',
    key: 'scenario_type',
    width: 120
  },
  {
    title: '设备编码',
    dataIndex: 'device_code',
    key: 'device_code',
    width: 150,
    ellipsis: true
  },
  {
    title: '设备对ID',
    dataIndex: 'device_pair_id',
    key: 'device_pair_id',
    width: 120,
    ellipsis: true
  },
  {
    title: '环号',
    dataIndex: 'ring_number',
    key: 'ring_number',
    width: 100
  },
  {
    title: '城市代码',
    dataIndex: 'city_code',
    key: 'city_code',
    width: 100
  },
  {
    title: '区域代码',
    dataIndex: 'area_code',
    key: 'area_code',
    width: 100
  },
  {
    title: 'B1设备编码',
    dataIndex: 'b1_code',
    key: 'b1_code',
    width: 150,
    ellipsis: true
  },
  {
    title: 'B2设备编码',
    dataIndex: 'b2_code',
    key: 'b2_code',
    width: 150,
    ellipsis: true
  },
  {
    title: '版本备注',
    dataIndex: 'description',
    key: 'description',
    width: 200,
    ellipsis: true
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    key: 'create_time',
    width: 180,
    sorter: true
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    key: 'creator',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
];

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss');
};

// 获取场景类型标签
const getScenarioTypeLabel = (scenarioType: string) => {
  switch (scenarioType) {
    case 'singleDeviceDualUplink':
      return '单设备双上联';
    case 'dualDeviceDualRoute':
      return '设备对双路由';
    case 'dualDeviceTripleRoute':
      return '设备对三路由';
    case 'ringNetwork':
      return '环形组网';
    default:
      return scenarioType || '未知';
  }
};

// 处理城市变更
const handleCityChange = (value: string) => {
  console.log('切换城市:', value);
  // 重置分页到第一页
  pagination.current = 1;
};

// 切换筛选条件展开状态
const toggleFilterExpanded = () => {
  filterExpanded.value = !filterExpanded.value;
};

// 跳转到省级统计分析页面
const handleGoToStatistics = () => {
  router.push({
    name: 'ProvinceStatistics'
  });
};

// 查询数据
const fetchData = async () => {
  loading.value = true;

  try {
    // 构建查询参数
    const params: any = {
      pageSize: pagination.pageSize,
      currentPage: pagination.current
    };

    console.log('发送的分页参数:', {
      pageSize: pagination.pageSize,
      currentPage: pagination.current
    });

    // 添加筛选条件（只添加非空值，但排除cityCode）
    Object.keys(filterForm).forEach(key => {
      const value = filterForm[key as keyof typeof filterForm];
      // cityCode只用于构建shardingCode，不传递给API的param参数
      if (key !== 'cityCode' && value !== '' && value !== null && value !== undefined) {
        params[key] = value;
      }
    });

    console.log('查询历史版本参数:', params);
    console.log('使用的城市代码:', filterForm.cityCode || defaultCityCode);

    // 调用API
    const result = await unifiedApi.queryHistoryVersions(
      filterForm.cityCode || defaultCityCode,
      params
    );

    console.log('API返回结果:', result);

    if (result && result.data) {
      // 处理数据列表
      if (Array.isArray(result.data)) {
        dataSource.value = result.data;
      } else if (result.data.records) {
        dataSource.value = result.data.records;
      } else {
        dataSource.value = [];
      }

      // 处理分页信息 - 根据实际API返回结构
      if (result.pageInfo) {
        // 使用API返回的分页信息
        pagination.total = result.pageInfo.totalCount || 0;
        pagination.current = result.pageInfo.currentPage || 1;
        pagination.pageSize = result.pageInfo.pageSize || 10;

        console.log('API返回的分页信息:', {
          totalCount: result.pageInfo.totalCount,
          totalPage: result.pageInfo.totalPage,
          currentPage: result.pageInfo.currentPage,
          pageSize: result.pageInfo.pageSize,
          recordPosition: result.pageInfo.recordPosition,
          enable: result.pageInfo.enable
        });
      } else {
        // 兜底处理
        pagination.total = dataSource.value.length;
      }
    } else {
      dataSource.value = [];
      pagination.total = 0;
    }

    console.log('处理后的分页数据:', {
      dataCount: dataSource.value.length,
      total: pagination.total,
      current: pagination.current,
      pageSize: pagination.pageSize
    });

  } catch (error) {
    console.error('查询历史版本失败:', error);
    message.error('查询历史版本失败，请稍后重试');
    dataSource.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};

// 处理重置
const handleReset = () => {
  Object.keys(filterForm).forEach(key => {
    if (key === 'cityCode') {
      filterForm[key] = defaultCityCode;
    } else {
      filterForm[key] = '';
    }
  });
  pagination.current = 1;
  fetchData();
};

// 处理刷新
const handleRefresh = () => {
  fetchData();
};

// 处理表格变化（分页、排序等）
const handleTableChange = (pag: any) => {
  console.log('分页变化:', pag);
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchData();
};

// 检查是否可以删除（只有创建人可以删除）
const canDelete = (record: any) => {
  const currentUser = userStore.getUserInfo;
  console.log('当前用户信息:', currentUser);
  const currentUsername = currentUser?.realName || '';
  return record.creator === currentUsername;
};

// 处理修改备注
const handleEditDescription = (record: any) => {
  editDescriptionForm.id = record.id;
  editDescriptionForm.description = record.description || '';
  editDescriptionVisible.value = true;
};

// 处理修改备注确认
const handleEditDescriptionOk = async () => {
  if (!editDescriptionForm.description.trim()) {
    message.error('请输入版本备注');
    return;
  }

  editDescriptionLoading.value = true;

  try {
    const params = {
      id: editDescriptionForm.id,
      description: editDescriptionForm.description.trim()
    };

    console.log('更新版本备注参数:', params);

    const result = await unifiedApi.updateHistoryVersionDescription(
      filterForm.cityCode || defaultCityCode,
      params
    );

    console.log('更新版本备注结果:', result);

    if (result && result.code === '0') {
      message.success('版本备注修改成功');
      editDescriptionVisible.value = false;
      // 刷新表格数据
      fetchData();
    } else {
      message.error(result?.message || '版本备注修改失败');
    }
  } catch (error) {
    console.error('修改版本备注失败:', error);
    message.error('修改版本备注失败，请稍后重试');
  } finally {
    editDescriptionLoading.value = false;
  }
};

// 处理修改备注取消
const handleEditDescriptionCancel = () => {
  editDescriptionVisible.value = false;
  editDescriptionForm.id = '';
  editDescriptionForm.description = '';
};

// 处理删除
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除此历史版本吗？删除后无法恢复',
    okText: '确认删除',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        const params = {
          id: record.id
        };

        console.log('删除版本参数:', params);

        const result = await unifiedApi.deleteHistoryVersion(
          filterForm.cityCode || defaultCityCode,
          params
        );

        console.log('删除版本结果:', result);

        if (result && result.code === '0') {
          message.success('历史版本删除成功');
          // 刷新表格数据
          fetchData();
        } else {
          message.error(result?.message || '历史版本删除失败');
        }
      } catch (error) {
        console.error('删除历史版本失败:', error);
        message.error('删除历史版本失败，请稍后重试');
      }
    }
  });
};

// 处理查看详情
const handleViewDetail = (record: any) => {
  console.log('查看历史版本详情:', record);

  // 构建基础查询参数
  const baseQuery: any = {
    versionId: record.id,
    protectionGroupId: record.protection_group_id,
    protectionGroupType: record.scenario_type,
    scenarioType: record.scenario_type,
    cityCode: filterForm.cityCode || defaultCityCode
  };

  // 根据不同场景类型添加特定参数
  switch (record.scenario_type) {
    case 'singleDeviceDualUplink':
      // 单设备双上联场景
      baseQuery.protectionGroupName = `单设备 ${record.device_code || '未知设备'}`;
      if (record.device_code) {
        baseQuery.deviceCode = record.device_code;
      }
      break;

    case 'dualDeviceDualRoute':
    case 'dualDeviceTripleRoute':
      // 设备对双路由/三路由场景
      baseQuery.protectionGroupName = `设备对 ${record.device_pair_id || '未知设备对'}`;
      if (record.device_pair_id) {
        baseQuery.devicePairId = record.device_pair_id;
      }
      if (record.device_code) {
        // 如果有设备编码，尝试解析设备名称
        const deviceCodes = record.device_code.split(',');
        if (deviceCodes.length >= 2) {
          baseQuery.deviceAName = deviceCodes[0].trim();
          baseQuery.deviceBName = deviceCodes[1].trim();
        }
      }
      break;

    case 'ringNetwork':
      // A环场景
      baseQuery.protectionGroupName = `A环设备 ${record.b1_code || '未知设备'}`;
      if (record.ring_number) {
        baseQuery.ringNum = record.ring_number;
      }
      if (record.b1_code) {
        baseQuery.b1Code = record.b1_code;
      }
      if (record.b2_code) {
        baseQuery.b2Code = record.b2_code;
      }
      break;

    default:
      baseQuery.protectionGroupName = `历史版本 ${record.id}`;
  }

  // 添加区域信息
  if (record.area_code) {
    baseQuery.areaName = record.area_code;
  }
  if (record.city_code) {
    baseQuery.cityName = record.city_code;
  }

  console.log('跳转参数:', baseQuery);

  // 跳转到历史版本查看页面
  router.push({
    name: 'HistoryVersionView',
    query: baseQuery
  });
};

// 组件挂载时获取数据
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.history-version-list-container {
  background-color: #f5f5f5;
}

.filter-card :deep(.ant-card-body) {
  padding: 24px;
}

.filter-form :deep(.ant-form-item) {
  margin-bottom: 16px;
}

.table-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.history-version-table :deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
}

.history-version-table :deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f0f9ff;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
