<template>
  <div class="optical-path-map-container">
    <div class="map-header" v-if="title">
      <h3>{{ title }}</h3>
      <div class="map-actions" v-if="$slots.actions">
        <slot name="actions"></slot>
      </div>
    </div>
    <div class="map-content">
      <a-spin :spinning="loading">
        <div :id="mapId" class="map-element"></div>

        <!-- 光路图例 -->
        <div class="map-legend" v-if="showLegend && props.opticalPaths && props.opticalPaths.length > 0">
          <div class="legend-title">光路图例</div>
          <div class="legend-items">
            <div
              v-for="path in normalizedOpticalPaths"
              :key="path.code"
              class="legend-item"
              @click="highlightOpticalPath(path.code)"
            >
              <div class="color-box" :style="{ 'background-color': path.color }"></div>
              <div class="path-name">{{ path.name || path.code }}</div>
            </div>
            <div
              v-if="hasOverlappingPaths"
              class="legend-item"
              @click="highlightOverlappingSegments"
            >
              <div class="color-box" style="background-color: #800080"></div>
              <div class="path-name">重叠部分</div>
            </div>
          </div>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, defineProps, defineEmits } from 'vue';
import { message } from 'ant-design-vue';
import { useInfo } from '@/hooks/web/useRestAPI';
import useGis from '@/hooks/gis/useGis';

// 定义光路对象类型
interface OpticalPath {
  code: string;
  color?: string;
  name?: string;
}

const props = defineProps({
  // 光路列表，可以是字符串数组或对象数组
  opticalPaths: {
    type: Array as () => (string | OpticalPath)[],
    default: () => []
  },
  // 城市代码
  cityCode: {
    type: String,
    default: 'js'
  },
  // 地图容器ID
  mapId: {
    type: String,
    default: 'opticalPathMap'
  },
  // 地图标题
  title: {
    type: String,
    default: ''
  },
  // 是否自动加载数据
  autoLoad: {
    type: Boolean,
    default: true
  },
  // 是否显示设备
  showDevices: {
    type: Boolean,
    default: true
  },
  // 是否显示管道段
  showPipeSegments: {
    type: Boolean,
    default: true
  },
  // 是否显示图例
  showLegend: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['map-initialized', 'data-loaded', 'error']);

// 状态变量
const loading = ref(false);
const mapContainer = ref<any>(null);
const mapInitialized = ref(false);
const opticalPathsData = ref<any[]>([]);
const pipeSegments = ref<any[]>([]);
const devices = ref<any[]>([]);
const hasOverlappingPaths = ref(false);

// 标准化光路对象，确保每个光路都有code和color属性
const normalizedOpticalPaths = computed(() => {
  const colors = ['#1890ff', '#f5222d', '#52c41a', '#faad14', '#722ed1', '#eb2f96', '#fa541c', '#13c2c2'];

  return props.opticalPaths.map((path, index) => {
    if (typeof path === 'string') {
      return {
        code: path,
        name: `光路 ${path}`,
        color: colors[index % colors.length]
      };
    } else {
      return {
        code: path.code,
        name: path.name || `光路 ${path.code}`,
        color: path.color || colors[index % colors.length]
      };
    }
  });
});

// 存储管线ID和线条图层对象的映射
const pipeSegmentLayerMap = ref(new Map());
// 存储光路编码和相关管线ID的映射
const opticalPathSegmentMap = ref(new Map());

// GIS工具
const { getAk, pantoCity } = useGis();

// 初始化地图
const initializeMap = async () => {
  try {
    console.log('初始化地图:', props.mapId);

    // 获取地图API密钥
    const ak = await getAk();
    console.log('获取到地图API密钥:', ak);

    // 确保DOM已经渲染
    await nextTick();

    // 获取地图API
    const Ai = (window as any).Ai;
    if (!Ai) {
      console.error('地图API未加载');
      emit('error', '地图API未加载');
      return null;
    }

    // 创建地图实例
    const map = new Ai.Map(props.mapId, {
      ak: ak,
      crs: '',
      mapType: '',
      center: [118.78, 32.07],
      zoom: 12,
      minZoom: 5,
      maxZoom: 18,
      doubleClickZoom: true
    });

    // 添加底图
    const maplayer = Ai.TileLayer(
      '/gis-platform-new/elec/js_map/server/wmts',
    );
    map.addLayer(maplayer);

    // 创建图层组
    const overLayer = new Ai.FeatureGroup();
    map.addLayer(overLayer);

    // 保存地图容器
    mapContainer.value = { map, overLayer, ak };
    mapInitialized.value = true;

    // 默认定位到指定城市
    pantoCity(mapContainer.value, props.cityCode.toLowerCase());

    // 触发地图初始化完成事件
    emit('map-initialized', mapContainer.value);

    return mapContainer.value;
  } catch (error) {
    console.error('初始化地图失败:', error);
    emit('error', '初始化地图失败');
    return null;
  }
};

// 加载光路数据
const loadOpticalPathsData = async (opticalPaths: (string | OpticalPath)[]) => {
  try {
    if (!opticalPaths || opticalPaths.length === 0) {
      console.warn('没有光路可加载');
      return false;
    }

    console.log('开始加载光路数据:', opticalPaths);
    loading.value = true;

    // 创建数组存储管道段和设备数据
    const allPipeSegments: any[] = [];
    const allDevices: any[] = [];
    const deviceIds = new Set();

    // 为每个光路分配颜色，优先使用传入的颜色
    const colorMap = new Map();
    const colors = ['#1890ff', '#f5222d', '#52c41a', '#faad14', '#722ed1', '#eb2f96', '#fa541c', '#13c2c2'];

    // 处理光路数据，提取编码和颜色
    const glCodes: string[] = [];

    // 首先从normalizedOpticalPaths中获取颜色映射
    const normalizedColorMap = new Map();
    normalizedOpticalPaths.value.forEach(path => {
      normalizedColorMap.set(path.code, path.color);
    });

    // 为每个光路预先分配颜色
    opticalPaths.forEach((path, index) => {
      let code: string;
      let color: string | undefined;

      if (typeof path === 'string') {
        // 如果是字符串，直接作为光路编码
        code = path;
        // 首先尝试从normalizedOpticalPaths中获取颜色
        color = normalizedColorMap.get(code) || colors[index % colors.length];
      } else {
        // 如果是对象，提取编码和颜色
        code = path.code;
        // 优先使用提供的颜色，其次尝试从normalizedOpticalPaths中获取，最后使用默认颜色
        color = path.color || normalizedColorMap.get(code) || colors[index % colors.length];
      }

      // 保存光路编码和颜色
      glCodes.push(code);
      colorMap.set(code, color);

      // 打印调试信息
      console.log(`光路 ${code} 分配颜色: ${color}`);
    });

    console.log('处理后的光路编码:', glCodes);
    console.log('光路颜色映射:', Object.fromEntries(colorMap));

    // 创建查询函数
    const fetchSingleOptRoadData = async (glCode: string) => {
      try {
        console.log('获取光路数据:', glCode);

        // 创建临时infoService
        const optRoadInfoService = useInfo({
          rootPath: '/graph-rest-api'
        });

        // 设置info值
        optRoadInfoService.info.value = {
          code: glCode,
          areaCode: props.cityCode.toLowerCase()
        };

        // 调用API
        console.log(`调用API: /api/customer-topology/opt_road_gis (光路: ${glCode})`);
        const result = await optRoadInfoService.doCreateNew('/api/customer-topology/opt_road_gis');

        if (!result) {
          console.warn(`光路 ${glCode} 没有返回数据`);
          return null;
        }

        console.log(`光路 ${glCode} 返回数据成功`);
        return { glCode, data: result };
      } catch (error) {
        console.error(`获取光路 ${glCode} 数据出错:`, error);
        return null;
      }
    };

    // 并行执行查询
    const optRoadDataPromises = glCodes.map(glCode => fetchSingleOptRoadData(glCode));
    const optRoadDataResults = await Promise.all(optRoadDataPromises);

    // 处理查询结果
    for (const result of optRoadDataResults) {
      if (!result) continue;

      const { glCode, data } = result;

      // 保存原始数据
      opticalPathsData.value.push({ glCode, data });

      // 处理设备数据
      if (data.road_both_point_devices_xy) {
        const tempObj = data.road_both_point_devices_xy;

        // 处理A端设备
        if (tempObj.a_id && !deviceIds.has(tempObj.a_id)) {
          deviceIds.add(tempObj.a_id);
          allDevices.push({
            "id": tempObj.a_id,
            "device_code": tempObj.a_id,
            "device_name": tempObj.a_name,
            "device_type": tempObj.a_spec_name,
            "pos_x": tempObj.a_pos_x,
            "pos_y": tempObj.a_pos_y,
            "gl_code": glCode
          });
        }

        // 处理Z端设备
        if (tempObj.z_id && !deviceIds.has(tempObj.z_id)) {
          deviceIds.add(tempObj.z_id);
          allDevices.push({
            "id": tempObj.z_id,
            "device_code": tempObj.z_id,
            "device_name": tempObj.z_name,
            "device_type": tempObj.z_spec_name,
            "pos_x": tempObj.z_pos_x,
            "pos_y": tempObj.z_pos_y,
            "gl_code": glCode
          });
        }
      }

      // 处理管道段数据
      if (data.pipe_segments && data.pipe_segments.length > 0) {
        data.pipe_segments.forEach((segment: any) => {
          // 确保opt_road_list存在
          if (!segment.opt_road_list) {
            segment.opt_road_list = [];
          }

          // 添加当前光路信息
          const hasCurrentOptRoad = segment.opt_road_list.some((road: any) => road.code === glCode);
          if (!hasCurrentOptRoad) {
            segment.opt_road_list.push({
              code: glCode,
              name: `光路 ${glCode}`,
              color: colorMap.get(glCode)
            });
          }

          // 检查是否已经添加过该管道段
          const existingSegment = allPipeSegments.find(s => s.code === segment.code);
          if (existingSegment) {
            // 如果已经添加过，合并光路信息
            segment.opt_road_list.forEach((road: any) => {
              const hasRoad = existingSegment.opt_road_list.some((r: any) => r.code === road.code);
              if (!hasRoad) {
                existingSegment.opt_road_list.push(road);
              }
            });
          } else {
            // 如果没有添加过，直接添加
            allPipeSegments.push(segment);
          }
        });
      }
    }

    // 保存数据
    devices.value = allDevices;
    const fixedPipeSegments = fixPipeSegmentsCoordinates(allPipeSegments);
    pipeSegments.value = fixedPipeSegments;

    console.log(`处理后的管道段数据: ${fixedPipeSegments.length} 个`);

    // 确保有管道段数据
    if (fixedPipeSegments.length === 0) {
      console.warn('没有有效的管道段数据可绘制');
      message.warning('没有找到有效的管道段数据');
    }

    // 清空映射
    pipeSegmentLayerMap.value.clear();
    opticalPathSegmentMap.value.clear();

    // 绘制地图
    drawMapData();

    // 延迟再次绘制，确保地图正确显示
    setTimeout(() => {
      console.log('延迟重绘地图，确保管线正确显示');
      drawMapData();
    }, 1000);

    // 触发数据加载完成事件
    emit('data-loaded', {
      devices: devices.value,
      pipeSegments: pipeSegments.value,
      opticalPaths: opticalPathsData.value
    });

    loading.value = false;
    return true;
  } catch (error) {
    console.error('加载光路数据失败:', error);
    emit('error', '加载光路数据失败');
    loading.value = false;
    return false;
  }
};

// 检查和修复管线段坐标
const fixPipeSegmentsCoordinates = (pipeSegments: any[]) => {
  if (!pipeSegments || pipeSegments.length === 0) {
    return [];
  }

  const fixedSegments: any[] = [];

  for (const segment of pipeSegments) {
    const fixedSegment = { ...segment };

    // 检查坐标是否有效
    if (!fixedSegment.a_pos_x || !fixedSegment.a_pos_y || !fixedSegment.z_pos_x || !fixedSegment.z_pos_y) {
      // 尝试从其他字段获取坐标
      if (fixedSegment.start_pos_x && fixedSegment.start_pos_y && fixedSegment.end_pos_x && fixedSegment.end_pos_y) {
        fixedSegment.a_pos_x = fixedSegment.start_pos_x;
        fixedSegment.a_pos_y = fixedSegment.start_pos_y;
        fixedSegment.z_pos_x = fixedSegment.end_pos_x;
        fixedSegment.z_pos_y = fixedSegment.end_pos_y;
      } else if (fixedSegment.pos_x1 && fixedSegment.pos_y1 && fixedSegment.pos_x2 && fixedSegment.pos_y2) {
        fixedSegment.a_pos_x = fixedSegment.pos_x1;
        fixedSegment.a_pos_y = fixedSegment.pos_y1;
        fixedSegment.z_pos_x = fixedSegment.pos_x2;
        fixedSegment.z_pos_y = fixedSegment.pos_y2;
      } else if (fixedSegment.a_xy && fixedSegment.z_xy) {
        // 尝试从a_xy和z_xy字段获取坐标
        const aXyParts = fixedSegment.a_xy.split(',');
        const zXyParts = fixedSegment.z_xy.split(',');

        if (aXyParts.length === 2 && zXyParts.length === 2) {
          fixedSegment.a_pos_x = parseFloat(aXyParts[0]);
          fixedSegment.a_pos_y = parseFloat(aXyParts[1]);
          fixedSegment.z_pos_x = parseFloat(zXyParts[0]);
          fixedSegment.z_pos_y = parseFloat(zXyParts[1]);
        } else {
          continue;
        }
      } else if (fixedSegment.xy) {
        // 尝试从xy字段获取坐标
        try {
          const xyPoints = JSON.parse(fixedSegment.xy);
          if (xyPoints && xyPoints.length >= 2) {
            fixedSegment.a_pos_x = xyPoints[0][0];
            fixedSegment.a_pos_y = xyPoints[0][1];
            fixedSegment.z_pos_x = xyPoints[xyPoints.length - 1][0];
            fixedSegment.z_pos_y = xyPoints[xyPoints.length - 1][1];
          } else {
            continue;
          }
        } catch (error) {
          continue;
        }
      } else {
        continue;
      }
    }

    // 检查坐标是否为数字
    if (
      isNaN(parseFloat(fixedSegment.a_pos_x)) ||
      isNaN(parseFloat(fixedSegment.a_pos_y)) ||
      isNaN(parseFloat(fixedSegment.z_pos_x)) ||
      isNaN(parseFloat(fixedSegment.z_pos_y))
    ) {
      continue;
    }

    // 确保坐标是数字类型
    fixedSegment.a_pos_x = parseFloat(fixedSegment.a_pos_x);
    fixedSegment.a_pos_y = parseFloat(fixedSegment.a_pos_y);
    fixedSegment.z_pos_x = parseFloat(fixedSegment.z_pos_x);
    fixedSegment.z_pos_y = parseFloat(fixedSegment.z_pos_y);

    // 添加到修复后的管线段数组
    fixedSegments.push(fixedSegment);
  }

  return fixedSegments;
};

// 绘制地图数据
const drawMapData = () => {
  try {
    console.log('开始绘制地图数据');

    if (!mapContainer.value || !mapContainer.value.map || !mapContainer.value.overLayer) {
      console.error('地图容器未初始化');
      return;
    }

    const Ai = (window as any).Ai;
    if (!Ai) {
      console.error('地图API未加载');
      return;
    }

    // 清空图层
    console.log('清空现有图层');
    mapContainer.value.overLayer.clearLayers();

    // 添加调试信息
    console.log('管道段数据:', pipeSegments.value);
    console.log('设备数据:', devices.value);

    // 重置重叠路径标志
    hasOverlappingPaths.value = false;

    // 绘制管道段
    if (props.showPipeSegments && pipeSegments.value && pipeSegments.value.length > 0) {
      // console.log(`开始绘制 ${pipeSegments.value.length} 个管道段`);

      for (const segment of pipeSegments.value) {
        try {
          // 检查坐标是否有效
          if (!segment.a_pos_x || !segment.a_pos_y || !segment.z_pos_x || !segment.z_pos_y) {
            continue;
          }

          // 检查是否有多条光路共用这个管道段
          if (segment.opt_road_list && segment.opt_road_list.length > 1) {
            hasOverlappingPaths.value = true;
          }

          // 创建线条
          const lineWktStr = `LINESTRING (${segment.a_pos_x} ${segment.a_pos_y}, ${segment.z_pos_x} ${segment.z_pos_y})`;

          // 设置线条样式
          const lineStyle = {
            color: '#1890ff', // 默认蓝色
            weight: 5,  // 增加线宽，使管线更明显
            opacity: 0.5  // 设置透明度为50%，使重叠部分可见
          };

          // 如果有光路，使用第一个光路的颜色
          if (segment.opt_road_list && segment.opt_road_list.length > 0) {
            // 确保光路有颜色
            if (segment.opt_road_list[0].color) {
              lineStyle.color = segment.opt_road_list[0].color;
            } else {
              // 如果光路没有颜色，尝试从normalizedOpticalPaths中查找
              const optPath = normalizedOpticalPaths.value.find(p => p.code === segment.opt_road_list[0].code);
              if (optPath && optPath.color) {
                lineStyle.color = optPath.color;
                // 更新光路颜色，以便后续使用
                segment.opt_road_list[0].color = optPath.color;
              }
            }
          }

          // 如果有多条光路共用这个管道段，使用紫色标记
          if (segment.opt_road_list && segment.opt_road_list.length > 1) {
            lineStyle.color = '#800080'; // 紫色
            lineStyle.opacity = 0.7; // 重叠部分透明度稍高
            lineStyle.weight = 6; // 重叠部分线宽稍大
          }

          // 确保颜色有值
          if (!lineStyle.color) {
            lineStyle.color = '#1890ff'; // 如果颜色未定义，使用默认蓝色
          }

          // console.log(`绘制管线段，样式:`, lineStyle);

          // 创建线条图层
          const lineLayer = new Ai.Polyline(lineWktStr, lineStyle);

          // 获取管线ID
          const segmentId = segment.zc_section_code || segment.code || `segment_${Math.random().toString(36).substring(2, 11)}`;

          // 保存光路信息到图层对象，方便高亮显示时查找
          if (segment.opt_road_list && segment.opt_road_list.length > 0) {
            // 保存光路列表，确保每个光路都有正确的颜色
            lineLayer.opticalPaths = segment.opt_road_list.map((road: any) => {
              // 尝试从normalizedOpticalPaths中获取颜色
              const normalizedPath = normalizedOpticalPaths.value.find(p => p.code === road.code);
              const color = road.color || (normalizedPath ? normalizedPath.color : undefined);

              return {
                code: road.code,
                name: road.name,
                color: color
              };
            });

            // 保存第一个光路编码到选项中
            lineLayer.options.opticalPath = segment.opt_road_list[0].code;

            // 建立光路和管线的关联
            segment.opt_road_list.forEach((road: any) => {
              const glCode = road.code;

              // 如果映射中还没有这个光路，创建一个新的集合
              if (!opticalPathSegmentMap.value.has(glCode)) {
                opticalPathSegmentMap.value.set(glCode, new Set());
              }

              // 将管线ID添加到光路的管线集合中
              opticalPathSegmentMap.value.get(glCode).add(segmentId);
            });
          }

          // 将线条图层对象存储到映射中
          pipeSegmentLayerMap.value.set(segmentId, lineLayer);

          // 添加到图层
          mapContainer.value.overLayer.addLayer(lineLayer);

          // console.log(`管线 ${segmentId} 已绘制并存储到映射中`);

          // 添加弹窗
          let popupContent = `<div>
            <p><strong>管道段：</strong>${segment.zc_section_code || segment.code || '-'}</p>`;

          // 添加光路信息
          if (segment.opt_road_list && segment.opt_road_list.length > 0) {
            popupContent += `<p><strong>光路：</strong>`;
            segment.opt_road_list.forEach((road: any, index: number) => {
              popupContent += `<span style="color: ${road.color}; font-weight: bold;">${road.code}</span>`;
              if (index < segment.opt_road_list.length - 1) {
                popupContent += ', ';
              }
            });
            popupContent += `</p>`;
          }

          popupContent += `</div>`;

          lineLayer.bindPopup(popupContent);
        } catch (error) {
          console.error('绘制管道段出错:', error, segment);
        }
      }
    }

    // 绘制设备
    if (props.showDevices && devices.value && devices.value.length > 0) {
      console.log(`开始绘制 ${devices.value.length} 个设备`);

      for (const device of devices.value) {
        try {
          // 检查坐标是否有效
          if (!device.pos_x || !device.pos_y) {
            continue;
          }

          // 获取设备类型的简称
          let deviceText = '设备';
          if (device.device_type) {
            deviceText = device.device_type.length > 2 ? device.device_type.substring(0, 2) : device.device_type;
          } else if (device.device_name) {
            deviceText = device.device_name.charAt(0);
          }

          // 创建自定义图标
          const divIconHTML = `
            <div style="
              width: 30px;
              height: 30px;
              border-radius: 50%;
              background-color: #1890ff;
              color: white;
              display: flex;
              justify-content: center;
              align-items: center;
              font-weight: bold;
              font-size: 14px;
              box-shadow: 0 2px 6px rgba(0,0,0,0.3);
            ">${deviceText}</div>
          `;

          const icon = new Ai.DivIcon({
            html: divIconHTML,
            iconSize: [30, 30],
            iconAnchor: [15, 15],
            className: 'custom-device-icon'
          });

          // 创建点标记
          const pointWktStr = `POINT (${device.pos_x} ${device.pos_y})`;
          const markerLayer = new Ai.Point(pointWktStr, { icon });

          // 添加到图层
          mapContainer.value.overLayer.addLayer(markerLayer);

          // 添加弹窗
          const popupContent = `<div>
            <p><strong>设备名称：</strong>${device.device_name || '-'}</p>
            <p><strong>设备编码：</strong>${device.device_code || '-'}</p>
            <p><strong>设备类型：</strong>${device.device_type || '-'}</p>
            ${device.gl_code ? `<p><strong>关联光路：</strong>${device.gl_code}</p>` : ''}
          </div>`;

          markerLayer.bindPopup(popupContent);
        } catch (error) {
          console.error('绘制设备出错:', error, device);
        }
      }
    }

    // 计算所有设备的中心点坐标
    if (devices.value && devices.value.length > 0) {
      console.log('计算所有设备的中心点坐标');

      // 收集所有有效的设备坐标
      const validDevices = devices.value.filter(device => device.pos_x && device.pos_y);

      if (validDevices.length > 0) {
        // 计算平均坐标
        let sumLat = 0;
        let sumLng = 0;

        validDevices.forEach(device => {
          sumLat += parseFloat(device.pos_y);
          sumLng += parseFloat(device.pos_x);
        });

        const avgLat = sumLat / validDevices.length;
        const avgLng = sumLng / validDevices.length;

        console.log(`设备中心点坐标: [${avgLat}, ${avgLng}]`);

        // 设置地图视图中心
        mapContainer.value.map.setView([avgLat, avgLng], 12);
        return;
      }
    }

    // 如果没有设备数据，使用默认坐标
    console.log('没有设备数据，使用默认坐标');
    mapContainer.value.map.setView([33.140749, 119.483485], 12);
  } catch (error) {
    console.error('绘制地图数据失败:', error);
    emit('error', '绘制地图数据失败');
  }
};

// 高亮显示光路
const highlightOpticalPath = (glCode: string) => {
  try {
    console.log(`尝试高亮显示光路: ${glCode}`);

    if (!mapContainer.value || !mapContainer.value.overLayer) {
      console.error('地图容器未初始化');
      return false;
    }

    // 先清除所有高亮
    clearHighlights();

    // 将光路编码转为小写，用于不区分大小写的比较
    const glCodeLower = glCode.toLowerCase();

    // 使用映射查找与光路相关的管线
    let found = false;
    let highlightedCount = 0;

    // 检查映射中是否有这个光路
    const segmentIds = opticalPathSegmentMap.value.get(glCode);

    if (segmentIds && segmentIds.size > 0) {
      console.log(`在映射中找到光路 ${glCode} 相关的管线，数量: ${segmentIds.size}`);

      // 遍历管线ID，高亮显示相关管线
      segmentIds.forEach((segmentId: string) => {
        const lineLayer = pipeSegmentLayerMap.value.get(segmentId);

        if (lineLayer) {
          // 普通光路高亮不再执行地图跳转，提高响应速度
          // console.log('普通光路高亮不执行地图跳转');

          // 保存原始样式以便恢复
          if (!lineLayer.options.originalStyle) {
            lineLayer.options.originalStyle = {
              color: lineLayer.options.color || '#1890ff',
              weight: lineLayer.options.weight || 5,
              opacity: lineLayer.options.opacity || 1.0
            };
          }

          // 设置高亮样式
          lineLayer.setStyle({
            color: '#ff0000',
            weight: 8,
            opacity: 1
          });

          console.log(`高亮显示管线 ${segmentId}`);

          found = true;
          highlightedCount++;

          // 将高亮的图层移到最上层
          lineLayer.bringToFront();
        } else {
          console.warn(`未找到管线 ${segmentId} 对应的图层对象`);
        }
      });

      console.log(`高亮显示了 ${highlightedCount} 个与光路 ${glCode} 相关的管道段`);
    } else {
      console.log(`在映射中未找到光路 ${glCode} 相关的管线，尝试使用其他方法查找`);

      // 尝试使用不区分大小写的方式查找
      for (const [key, value] of opticalPathSegmentMap.value.entries()) {
        if (key.toLowerCase() === glCodeLower) {
          console.log(`找到不区分大小写匹配的光路: ${key}`);

          // 遍历管线ID，高亮显示相关管线
          value.forEach((segmentId: string) => {
            const lineLayer = pipeSegmentLayerMap.value.get(segmentId);

            if (lineLayer) {
              // 普通光路高亮不再执行地图跳转，提高响应速度
              // console.log('普通光路高亮不执行地图跳转');

              // 保存原始样式以便恢复
              if (!lineLayer.options.originalStyle) {
                lineLayer.options.originalStyle = {
                  color: lineLayer.options.color || '#1890ff',
                  weight: lineLayer.options.weight || 5,
                  opacity: lineLayer.options.opacity || 1.0
                };
              }

              // 设置高亮样式
              lineLayer.setStyle({
                color: '#ff0000',
                weight: 8,
                opacity: 1
              });

              console.log(`高亮显示管线 ${segmentId}`);

              found = true;
              highlightedCount++;

              // 将高亮的图层移到最上层
              lineLayer.bringToFront();
            }
          });
        }
      }

      // 如果仍然没有找到，尝试使用图层属性查找
      if (!found) {
        console.log(`使用不区分大小写的方式仍未找到光路 ${glCode} 相关的管线，尝试使用图层属性查找`);

        // 获取所有图层
        const layers = mapContainer.value.overLayer.getLayers();
        console.log(`当前地图有 ${layers.length} 个图层`);

        // 检查每个图层
        layers.forEach((layer: any) => {
          // 检查是否是线条图层（管道段）
          if (layer instanceof (window as any).Ai.Polyline) {
            let shouldHighlight = false;

            // 方法1: 检查弹窗内容
            if (layer._popup && layer._popup._content) {
              const popupContent = layer._popup._content.toLowerCase();
              if (popupContent.includes(glCodeLower)) {
                console.log('通过弹窗内容找到匹配的光路');
                shouldHighlight = true;
              }
            }

            // 方法2: 检查图层选项中的光路信息
            if (!shouldHighlight && layer.options && layer.options.opticalPath) {
              const opticalPathCode = layer.options.opticalPath.toLowerCase();
              if (opticalPathCode === glCodeLower) {
                console.log('通过图层选项找到匹配的光路');
                shouldHighlight = true;
              }
            }

            // 方法3: 检查自定义属性
            if (!shouldHighlight && layer.opticalPaths) {
              const paths = Array.isArray(layer.opticalPaths) ? layer.opticalPaths : [layer.opticalPaths];
              for (const path of paths) {
                const pathCode = typeof path === 'string' ? path : (path.code || '');
                if (pathCode.toLowerCase() === glCodeLower) {
                  console.log('通过自定义属性找到匹配的光路');
                  shouldHighlight = true;
                  break;
                }
              }
            }

            // 如果应该高亮显示
            if (shouldHighlight) {
              // 普通光路高亮不再执行地图跳转，提高响应速度
              // console.log('普通光路高亮不执行地图跳转');

              // 保存原始样式以便恢复
              if (!layer.options.originalStyle) {
                layer.options.originalStyle = {
                  color: layer.options.color || '#1890ff',
                  weight: layer.options.weight || 5,
                  opacity: layer.options.opacity || 1.0
                };
              }

              // 设置高亮样式
              layer.setStyle({
                color: '#ff0000',
                weight: 8,
                opacity: 1
              });

              console.log('高亮显示管线:', layer.options);

              found = true;
              highlightedCount++;

              // 将高亮的图层移到最上层
              layer.bringToFront();
            }
          }
        });

        console.log(`通过图层属性查找，高亮显示了 ${highlightedCount} 个与光路 ${glCode} 相关的管道段`);
      }
    }

    // 如果仍然没有找到，打印调试信息
    if (!found) {
      console.log(`未找到与光路 ${glCode} 相关的管道段，打印调试信息`);

      console.log('光路-管线映射:', Object.fromEntries([...opticalPathSegmentMap.value.entries()].map(([k, v]) => [k, [...v]])));
      console.log('管线-图层映射大小:', pipeSegmentLayerMap.value.size);

      // 打印所有管线ID
      console.log('所有管线ID:');
      for (const segmentId of pipeSegmentLayerMap.value.keys()) {
        console.log(`- ${segmentId}`);
      }
    }

    return found;
  } catch (error) {
    console.error('高亮显示光路失败:', error);
    return false;
  }
};

// 清除所有高亮
const clearHighlights = () => {
  try {
    console.log('清除所有高亮');

    if (!mapContainer.value || !mapContainer.value.overLayer) {
      console.error('地图容器未初始化');
      return;
    }

    // 获取所有图层
    const layers = mapContainer.value.overLayer.getLayers();
    console.log(`当前地图有 ${layers.length} 个图层`);

    // 恢复所有图层的默认样式
    let restoredCount = 0;
    layers.forEach((layer: any) => {
      if (layer.options && layer.options.originalStyle) {
        console.log('恢复图层原始样式:', layer.options.originalStyle);
        layer.setStyle(layer.options.originalStyle);
        // 清除原始样式记录，以便下次高亮时重新保存
        delete layer.options.originalStyle;
        restoredCount++;
      }
    });

    console.log(`已恢复 ${restoredCount} 个图层的原始样式`);
  } catch (error) {
    console.error('清除所有高亮失败:', error);
  }
};

// 重新加载数据
const reloadData = async () => {
  // 清空数据
  opticalPathsData.value = [];
  pipeSegments.value = [];
  devices.value = [];

  // 清空图层
  if (mapContainer.value && mapContainer.value.overLayer) {
    mapContainer.value.overLayer.clearLayers();
  }

  // 加载数据
  if (props.opticalPaths && props.opticalPaths.length > 0) {
    await loadOpticalPathsData(props.opticalPaths);
  }
};

// 高亮显示所有重叠的管道段
const highlightOverlappingSegments = () => {
  try {
    console.log('高亮显示所有重叠的管道段');

    if (!mapContainer.value || !mapContainer.value.overLayer) {
      console.error('地图容器未初始化');
      return false;
    }

    // 先清除所有高亮
    clearHighlights();

    // 高亮所有重叠的管道段
    let highlightedCount = 0;

    // 使用映射查找所有重叠的管道段
    if (pipeSegmentLayerMap.value.size > 0) {
      console.log(`检查 ${pipeSegmentLayerMap.value.size} 个管道段是否有重叠`);

      // 遍历所有管线图层，查找包含多条光路的管线
      for (const [segmentId, lineLayer] of pipeSegmentLayerMap.value.entries()) {
        if (lineLayer.opticalPaths && lineLayer.opticalPaths.length > 1) {
          console.log(`找到重叠的管道段 ${segmentId}，包含 ${lineLayer.opticalPaths.length} 条光路`);

          // 设置地图视图到高亮管线位置 - 先跳转再高亮
          try {
            mapContainer.value.map.setView((lineLayer as any).getCenter(), 15, {});
            console.log('将视图设置到重叠管线中心点，缩放级别为15');
          } catch (error) {
            console.error('设置视图位置失败:', error);
          }

          // 保存原始样式以便恢复
          if (!lineLayer.options.originalStyle) {
            lineLayer.options.originalStyle = {
              color: lineLayer.options.color || '#1890ff',
              weight: lineLayer.options.weight || 5,
              opacity: lineLayer.options.opacity || 1.0
            };
          }

          // 设置高亮样式 - 使用紫色
          lineLayer.setStyle({
            color: '#800080', // 紫色
            weight: 8,
            opacity: 0.9
          });

          highlightedCount++;
          lineLayer.bringToFront();
        }
      }

      console.log(`高亮显示了 ${highlightedCount} 个重叠的管道段`);
    }

    return highlightedCount > 0;
  } catch (error) {
    console.error('高亮显示重叠部分失败:', error);
    return false;
  }
};

// 高亮所有管线
const highlightAllPaths = () => {
  try {
    console.log('高亮所有管线');

    if (!mapContainer.value || !mapContainer.value.overLayer) {
      console.error('地图容器未初始化');
      return false;
    }

    // 先清除所有高亮
    clearHighlights();

    // 高亮所有管线
    let highlightedCount = 0;

    // 使用映射高亮所有管线
    if (pipeSegmentLayerMap.value.size > 0) {
      console.log(`使用映射高亮所有管线，数量: ${pipeSegmentLayerMap.value.size}`);

      // 遍历所有管线图层
      for (const [, lineLayer] of pipeSegmentLayerMap.value.entries()) {
        // 普通光路高亮不再执行地图跳转，提高响应速度
        // console.log('普通光路高亮不执行地图跳转');

        // 保存原始样式以便恢复
        if (!lineLayer.options.originalStyle) {
          lineLayer.options.originalStyle = {
            color: lineLayer.options.color || '#1890ff',
            weight: lineLayer.options.weight || 5,
            opacity: lineLayer.options.opacity || 1.0
          };
        }

        // 设置高亮样式
        lineLayer.setStyle({
          color: '#ff0000',
          weight: 8,
          opacity: 1
        });

        highlightedCount++;
        lineLayer.bringToFront();
      }

      console.log(`使用映射高亮显示了 ${highlightedCount} 个管线`);
    } else {
      console.log('映射为空，使用图层方式高亮所有管线');

      // 获取所有图层
      const layers = mapContainer.value.overLayer.getLayers();
      console.log(`当前地图有 ${layers.length} 个图层`);

      // 高亮所有线条图层
      layers.forEach((layer: any) => {
        if (layer instanceof (window as any).Ai.Polyline) {
          // 普通光路高亮不再执行地图跳转，提高响应速度
          // console.log('普通光路高亮不执行地图跳转');

          // 保存原始样式以便恢复
          if (!layer.options.originalStyle) {
            layer.options.originalStyle = {
              color: layer.options.color || '#1890ff',
              weight: layer.options.weight || 5,
              opacity: layer.options.opacity || 1.0
            };
          }

          // 设置高亮样式
          layer.setStyle({
            color: '#ff0000',
            weight: 8,
            opacity: 1
          });

          highlightedCount++;
          layer.bringToFront();
        }
      });

      console.log(`使用图层方式高亮显示了 ${highlightedCount} 个管线`);
    }

    return highlightedCount > 0;
  } catch (error) {
    console.error('高亮所有管线失败:', error);
    return false;
  }
};

// 暴露方法给父组件
defineExpose({
  initializeMap,
  loadOpticalPathsData,
  drawMapData,
  highlightOpticalPath,
  highlightAllPaths,
  highlightOverlappingSegments,
  clearHighlights,
  reloadData,
  mapContainer
});

// 监听光路编码变化
watch(() => props.opticalPaths, async (newPaths) => {
  if (newPaths && newPaths.length > 0 && mapInitialized.value) {
    await reloadData();
  }
}, { deep: true });

// 组件挂载时初始化地图
onMounted(async () => {
  // 初始化地图
  await initializeMap();

  // 如果设置了自动加载并且有光路编码，则加载数据
  if (props.autoLoad && props.opticalPaths && props.opticalPaths.length > 0) {
    await loadOpticalPathsData(props.opticalPaths);
  }
});
</script>

<style scoped lang="less">
.optical-path-map-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .map-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: bold;
    }

    .map-actions {
      display: flex;
      gap: 8px;
    }
  }

  .map-content {
    flex: 1;
    position: relative;
    min-height: 500px;

    .map-element {
      width: 100%;
      height: 100%;
      min-height: 500px;
    }

    .map-legend {
      position: absolute;
      top: 10px;
      right: 10px;
      background-color: rgba(255, 255, 255, 0.9);
      border-radius: 4px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
      padding: 8px;
      z-index: 1000;
      width: 200px;
      max-width: 200px;
      max-height: 70%;
      overflow-y: auto;

      .legend-title {
        font-weight: bold;
        margin-bottom: 8px;
        font-size: 14px;
        border-bottom: 1px solid #eee;
        padding-bottom: 4px;
      }

      .legend-items {
        display: flex;
        flex-direction: column;
        gap: 6px;
        max-height: calc(100% - 30px); // 减去标题高度

        .legend-item {
          display: flex;
          align-items: center;
          cursor: pointer;
          padding: 2px 4px;
          border-radius: 2px;

          &:hover {
            background-color: #f0f0f0;
          }

          .color-box {
            width: 16px;
            height: 16px;
            border-radius: 3px;
            margin-right: 8px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
          }

          .path-name {
            font-size: 12px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 150px; // 确保文本有足够的显示空间
          }
        }
      }
    }
  }
}
</style>
