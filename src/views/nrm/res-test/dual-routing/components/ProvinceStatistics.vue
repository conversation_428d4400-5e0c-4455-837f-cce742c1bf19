<template>
  <div class="province-statistics-container p-6 bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-800">江苏省双路由历史版本统计分析</h1>
          <p class="text-gray-600 mt-2">全省13个地市的历史版本数据统计与分析</p>
        </div>

        <div class="flex space-x-4">
          <a-button @click="handleRefreshAll" :loading="refreshLoading">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新数据
          </a-button>

          <a-button @click="handleExportReport">
            <template #icon>
              <DownloadOutlined />
            </template>
            导出报表
          </a-button>

          <a-button @click="handleGoBack">
            <template #icon>
              <ArrowLeftOutlined />
            </template>
            返回清单
          </a-button>
        </div>
      </div>
    </div>

    <!-- 时间筛选区域 -->
    <a-card class="filter-card mb-6" title="数据筛选">
      <div class="flex items-center space-x-4 flex-wrap">
        <div class="flex items-center space-x-2">
          <span class="text-gray-700">时间范围：</span>
          <a-radio-group v-model:value="timeRange" @change="handleTimeRangeChange">
            <a-radio-button value="7">最近7天</a-radio-button>
            <a-radio-button value="30">最近30天</a-radio-button>
            <a-radio-button value="90">最近90天</a-radio-button>
            <a-radio-button value="custom">自定义</a-radio-button>
          </a-radio-group>
        </div>

        <div v-if="timeRange === 'custom'" class="flex items-center space-x-2">
          <a-date-picker
            v-model:value="customStartTime"
            placeholder="开始时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleCustomTimeChange"
          />
          <span>至</span>
          <a-date-picker
            v-model:value="customEndTime"
            placeholder="结束时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleCustomTimeChange"
          />
        </div>

        <div class="flex items-center space-x-2">
          <span class="text-gray-700">场景类型：</span>
          <a-select
            v-model:value="scenarioTypeFilter"
            placeholder="全部场景"
            style="width: 150px"
            allow-clear
            @change="handleScenarioFilterChange"
          >
            <a-select-option value="">全部场景</a-select-option>
            <a-select-option value="singleDeviceDualUplink">单设备双上联</a-select-option>
            <a-select-option value="dualDeviceDualRoute">设备对双路由</a-select-option>
            <a-select-option value="dualDeviceTripleRoute">设备对三路由</a-select-option>
            <a-select-option value="ringNetwork">环形组网</a-select-option>
          </a-select>
        </div>
      </div>
    </a-card>

    <!-- 13个地市卡片区域 -->
    <a-card class="cities-card mb-6" title="江苏省13个地市概览">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
        <div
          v-for="city in cities"
          :key="city.code"
          class="city-card p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 hover:shadow-lg"
          :class="{
            'border-blue-500 bg-blue-50': selectedCity === city.code,
            'border-gray-200 bg-white hover:border-blue-300': selectedCity !== city.code
          }"
          @click="handleCitySelect(city.code)"
        >
          <div class="flex items-center justify-between mb-2">
            <h3 class="text-lg font-semibold text-gray-800">{{ city.name }}</h3>
            <div
              class="w-3 h-3 rounded-full"
              :style="{ backgroundColor: city.color }"
            ></div>
          </div>

          <div class="space-y-1">
            <div class="flex justify-between items-center">
              <span class="text-gray-600">版本总数：</span>
              <span class="font-bold text-blue-600">
                {{ cityStats[city.code]?.total || 0 }}
              </span>
            </div>

            <div class="flex justify-between items-center">
              <span class="text-gray-600">最近更新：</span>
              <span class="text-sm text-gray-500">
                {{ formatLastUpdate(cityStats[city.code]?.lastUpdate) }}
              </span>
            </div>
          </div>

          <div v-if="cityStats[city.code]?.loading" class="mt-2">
            <a-spin size="small" />
            <span class="ml-2 text-sm text-gray-500">加载中...</span>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 统计报表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- 版本新增趋势图 -->
      <a-card title="版本新增趋势" class="chart-card">
        <div ref="trendChartRef" class="chart-container" style="height: 300px;"></div>
      </a-card>

      <!-- 设备版本排行榜 -->
      <a-card title="设备版本排行榜 (Top 10)" class="chart-card">
        <div ref="deviceRankChartRef" class="chart-container" style="height: 300px;"></div>
      </a-card>
    </div>

    <!-- 创建人活跃度排行 -->
    <a-card title="创建人活跃度排行 (Top 10)" class="chart-card mb-6">
      <div ref="creatorRankChartRef" class="chart-container" style="height: 300px;"></div>
    </a-card>

    <!-- 详细数据表格 -->
    <a-card class="table-card" :title="`${getSelectedCityName()}详细历史版本列表`">
      <template #extra>
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-600">
            共 {{ detailPagination.total }} 条记录
          </span>
          <a-button @click="handleRefreshDetail" :loading="detailLoading">
            刷新
          </a-button>
        </div>
      </template>

      <a-table
        :dataSource="detailDataSource"
        :columns="detailColumns"
        :loading="detailLoading"
        :pagination="detailPagination"
        :scroll="{ x: 1200 }"
        size="middle"
        :rowKey="(record: any) => record.id"
        @change="handleDetailTableChange"
        class="detail-table"
      >
        <!-- 场景类型列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'scenario_type'">
            <div class="flex items-center">
              <span
                class="w-2 h-2 rounded-full mr-2"
                :class="{
                  'bg-blue-500': record.scenario_type === 'singleDeviceDualUplink',
                  'bg-green-500': record.scenario_type === 'dualDeviceDualRoute',
                  'bg-orange-500': record.scenario_type === 'dualDeviceTripleRoute',
                  'bg-purple-500': record.scenario_type === 'ringNetwork',
                  'bg-gray-400': !record.scenario_type
                }"
              ></span>
              <span>{{ getScenarioTypeLabel(record.scenario_type) }}</span>
            </div>
          </template>

          <!-- 版本备注列 -->
          <template v-else-if="column.key === 'description'">
            <div class="max-w-xs">
              <a-tooltip :title="record.description" placement="topLeft">
                <span class="truncate block">{{ record.description || '无备注' }}</span>
              </a-tooltip>
            </div>
          </template>

          <!-- 创建时间列 -->
          <template v-else-if="column.key === 'create_time'">
            <span>{{ formatDateTime(record.create_time) }}</span>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  ReloadOutlined,
  DownloadOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons-vue';
import { useUnifiedApi } from '@/hooks/web/useUnifiedApi';
import { useUserStoreWithOut } from '@/store/modules/user';
import dayjs from 'dayjs';
import * as echarts from 'echarts';

defineOptions({
  name: 'ProvinceStatistics'
});

const router = useRouter();

// 获取用户存储
const userStore = useUserStoreWithOut();

// 初始化统一API调用钩子
const unifiedApi = useUnifiedApi({
  rootPath: '/graph-rest-api',
  defaultCityCode: 'js'
});

// 江苏省13个地市配置
const cities = [
  { code: 'NJ', name: '南京', color: '#1890ff' },
  { code: 'WX', name: '无锡', color: '#52c41a' },
  { code: 'XZ', name: '徐州', color: '#fa8c16' },
  { code: 'CZ', name: '常州', color: '#eb2f96' },
  { code: 'SZ', name: '苏州', color: '#722ed1' },
  { code: 'NT', name: '南通', color: '#13c2c2' },
  { code: 'LYG', name: '连云港', color: '#faad14' },
  { code: 'HA', name: '淮安', color: '#f5222d' },
  { code: 'YC', name: '盐城', color: '#a0d911' },
  { code: 'YZ', name: '扬州', color: '#2f54eb' },
  { code: 'ZJ', name: '镇江', color: '#fa541c' },
  { code: 'TZ', name: '泰州', color: '#531dab' },
  { code: 'SQ', name: '宿迁', color: '#096dd9' }
];

// 状态变量
const refreshLoading = ref(false);
const timeRange = ref('30');
const customStartTime = ref('');
const customEndTime = ref('');
const scenarioTypeFilter = ref('');
const selectedCity = ref('NJ'); // 默认选择南京
const cityStats = reactive<Record<string, any>>({});

// 图表引用
const trendChartRef = ref<HTMLElement>();
const deviceRankChartRef = ref<HTMLElement>();
const creatorRankChartRef = ref<HTMLElement>();

// 详细数据表格
const detailLoading = ref(false);
const detailDataSource = ref<any[]>([]);
const detailPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
  pageSizeOptions: ['10', '20', '50', '100']
});

// 详细数据表格列配置
const detailColumns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80
  },
  {
    title: '保护组ID',
    dataIndex: 'protection_group_id',
    key: 'protection_group_id',
    width: 150,
    ellipsis: true
  },
  {
    title: '场景类型',
    dataIndex: 'scenario_type',
    key: 'scenario_type',
    width: 120
  },
  {
    title: '版本备注',
    dataIndex: 'description',
    key: 'description',
    width: 200,
    ellipsis: true
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    key: 'create_time',
    width: 180
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    key: 'creator',
    width: 120
  }
];

// 计算属性和方法
const getSelectedCityName = () => {
  const city = cities.find(c => c.code === selectedCity.value);
  return city ? city.name : '全省';
};

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss');
};

// 格式化最后更新时间
const formatLastUpdate = (dateTime: string) => {
  if (!dateTime) return '暂无数据';
  const now = dayjs();
  const updateTime = dayjs(dateTime);
  const diffDays = now.diff(updateTime, 'day');

  if (diffDays === 0) {
    return '今天';
  } else if (diffDays === 1) {
    return '昨天';
  } else if (diffDays <= 7) {
    return `${diffDays}天前`;
  } else {
    return updateTime.format('MM-DD');
  }
};

// 获取场景类型标签
const getScenarioTypeLabel = (scenarioType: string) => {
  switch (scenarioType) {
    case 'singleDeviceDualUplink':
      return '单设备双上联';
    case 'dualDeviceDualRoute':
      return '设备对双路由';
    case 'dualDeviceTripleRoute':
      return '设备对三路由';
    case 'ringNetwork':
      return '环形组网';
    default:
      return scenarioType || '未知';
  }
};

// 获取时间范围
const getTimeRange = () => {
  const now = dayjs();
  let startTime = '';
  let endTime = now.format('YYYY-MM-DD HH:mm:ss');

  if (timeRange.value === 'custom') {
    if (customStartTime.value && customEndTime.value) {
      startTime = dayjs(customStartTime.value).format('YYYY-MM-DD 00:00:00');
      endTime = dayjs(customEndTime.value).format('YYYY-MM-DD 23:59:59');
    }
  } else {
    const days = parseInt(timeRange.value);
    startTime = now.subtract(days, 'day').format('YYYY-MM-DD 00:00:00');
  }

  return { startTime, endTime };
};

// 查询单个地市的统计数据
const fetchCityStats = async (cityCode: string) => {
  try {
    cityStats[cityCode] = { ...cityStats[cityCode], loading: true };

    const { startTime, endTime } = getTimeRange();
    const params: any = {
      pageSize: 1000,
      currentPage: 1
    };

    if (startTime) params.startTime = startTime;
    if (endTime) params.endTime = endTime;
    if (scenarioTypeFilter.value) params.scenarioType = scenarioTypeFilter.value;

    console.log(`查询${cityCode}统计数据:`, params);

    const result = await unifiedApi.queryHistoryVersions(cityCode.toLowerCase(), params);

    if (result && result.data) {
      const data = Array.isArray(result.data) ? result.data : [];
      const total = result.pageInfo?.totalCount || data.length;

      // 找到最近更新时间
      let lastUpdate = '';
      if (data.length > 0) {
        const sortedData = data.sort((a, b) =>
          dayjs(b.create_time).valueOf() - dayjs(a.create_time).valueOf()
        );
        lastUpdate = sortedData[0].create_time;
      }

      cityStats[cityCode] = {
        total,
        lastUpdate,
        data,
        loading: false
      };
    } else {
      cityStats[cityCode] = {
        total: 0,
        lastUpdate: '',
        data: [],
        loading: false
      };
    }
  } catch (error) {
    console.error(`查询${cityCode}统计数据失败:`, error);
    cityStats[cityCode] = {
      total: 0,
      lastUpdate: '',
      data: [],
      loading: false,
      error: true
    };
  }
};

// 查询所有地市统计数据
const fetchAllCitiesStats = async () => {
  refreshLoading.value = true;

  try {
    // 并行查询所有地市数据
    const promises = cities.map(city => fetchCityStats(city.code));
    await Promise.all(promises);

    // 更新图表
    await nextTick();
    updateCharts();

  } catch (error) {
    console.error('查询全省统计数据失败:', error);
    message.error('查询统计数据失败，请稍后重试');
  } finally {
    refreshLoading.value = false;
  }
};

// 查询选中地市的详细数据
const fetchDetailData = async () => {
  detailLoading.value = true;

  try {
    const { startTime, endTime } = getTimeRange();
    const params: any = {
      pageSize: detailPagination.pageSize,
      currentPage: detailPagination.current
    };

    if (startTime) params.startTime = startTime;
    if (endTime) params.endTime = endTime;
    if (scenarioTypeFilter.value) params.scenarioType = scenarioTypeFilter.value;

    console.log(`查询${selectedCity.value}详细数据:`, params);

    const result = await unifiedApi.queryHistoryVersions(
      selectedCity.value.toLowerCase(),
      params
    );

    if (result && result.data) {
      detailDataSource.value = Array.isArray(result.data) ? result.data : [];
      detailPagination.total = result.pageInfo?.totalCount || 0;
      detailPagination.current = result.pageInfo?.currentPage || 1;
      detailPagination.pageSize = result.pageInfo?.pageSize || 10;
    } else {
      detailDataSource.value = [];
      detailPagination.total = 0;
    }
  } catch (error) {
    console.error('查询详细数据失败:', error);
    message.error('查询详细数据失败，请稍后重试');
    detailDataSource.value = [];
    detailPagination.total = 0;
  } finally {
    detailLoading.value = false;
  }
};

// 事件处理方法
const handleRefreshAll = () => {
  fetchAllCitiesStats();
  fetchDetailData();
};

const handleExportReport = () => {
  message.info('导出功能开发中...');
};

const handleGoBack = () => {
  router.push({ name: 'HistoryVersionList' });
};

const handleTimeRangeChange = () => {
  fetchAllCitiesStats();
  fetchDetailData();
};

const handleCustomTimeChange = () => {
  if (timeRange.value === 'custom' && customStartTime.value && customEndTime.value) {
    fetchAllCitiesStats();
    fetchDetailData();
  }
};

const handleScenarioFilterChange = () => {
  fetchAllCitiesStats();
  fetchDetailData();
};

const handleCitySelect = (cityCode: string) => {
  selectedCity.value = cityCode;
  fetchDetailData();
};

const handleRefreshDetail = () => {
  fetchDetailData();
};

const handleDetailTableChange = (pag: any) => {
  detailPagination.current = pag.current;
  detailPagination.pageSize = pag.pageSize;
  fetchDetailData();
};

// 更新图表
const updateCharts = () => {
  updateTrendChart();
  updateDeviceRankChart();
  updateCreatorRankChart();
};

// 更新趋势图
const updateTrendChart = () => {
  if (!trendChartRef.value) return;

  const chart = echarts.init(trendChartRef.value);

  // 准备数据
  const dates: string[] = [];
  const seriesData: any[] = [];

  // 获取最近7天的日期
  for (let i = 6; i >= 0; i--) {
    dates.push(dayjs().subtract(i, 'day').format('MM-DD'));
  }

  // 为每个地市准备数据
  cities.forEach(city => {
    const cityData = cityStats[city.code]?.data || [];
    const dailyCounts = dates.map(date => {
      const fullDate = dayjs().year() + '-' + date;
      return cityData.filter((item: any) =>
        dayjs(item.create_time).format('YYYY-MM-DD') === dayjs(fullDate).format('YYYY-MM-DD')
      ).length;
    });

    seriesData.push({
      name: city.name,
      type: 'line',
      data: dailyCounts,
      smooth: true
    });
  });

  const option = {
    title: {
      text: '各地市版本新增趋势',
      left: 'center',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      top: 30,
      type: 'scroll'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: 80,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: {
      type: 'value'
    },
    series: seriesData
  };

  chart.setOption(option);
};

// 更新设备排行图
const updateDeviceRankChart = () => {
  if (!deviceRankChartRef.value) return;

  const chart = echarts.init(deviceRankChartRef.value);

  // 统计所有设备的版本数量
  const deviceStats: Record<string, number> = {};

  Object.values(cityStats).forEach((cityData: any) => {
    if (cityData.data) {
      cityData.data.forEach((item: any) => {
        if (item.device_code) {
          const devices = item.device_code.split(',');
          devices.forEach((device: string) => {
            const deviceCode = device.trim();
            if (deviceCode) {
              deviceStats[deviceCode] = (deviceStats[deviceCode] || 0) + 1;
            }
          });
        }
      });
    }
  });

  // 排序并取前10
  const sortedDevices = Object.entries(deviceStats)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10);

  const option = {
    title: {
      text: '设备版本数量排行',
      left: 'center',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: 50,
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: sortedDevices.map(([device]) => device),
      axisLabel: {
        interval: 0,
        formatter: (value: string) => {
          return value.length > 10 ? value.substring(0, 10) + '...' : value;
        }
      }
    },
    series: [{
      type: 'bar',
      data: sortedDevices.map(([, count]) => count),
      itemStyle: {
        color: '#1890ff'
      }
    }]
  };

  chart.setOption(option);
};

// 更新创建人排行图
const updateCreatorRankChart = () => {
  if (!creatorRankChartRef.value) return;

  const chart = echarts.init(creatorRankChartRef.value);

  // 统计所有创建人的版本数量
  const creatorStats: Record<string, number> = {};

  Object.values(cityStats).forEach((cityData: any) => {
    if (cityData.data) {
      cityData.data.forEach((item: any) => {
        if (item.creator) {
          creatorStats[item.creator] = (creatorStats[item.creator] || 0) + 1;
        }
      });
    }
  });

  // 排序并取前10
  const sortedCreators = Object.entries(creatorStats)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10);

  const option = {
    title: {
      text: '创建人活跃度排行',
      left: 'center',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'item'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: 50,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: sortedCreators.map(([creator]) => creator),
      axisLabel: {
        interval: 0,
        rotate: 45
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      type: 'bar',
      data: sortedCreators.map(([, count]) => count),
      itemStyle: {
        color: '#52c41a'
      }
    }]
  };

  chart.setOption(option);
};

// 组件挂载时初始化
onMounted(() => {
  fetchAllCitiesStats();
  fetchDetailData();
});
</script>

<style scoped>
.province-statistics-container {
  background-color: #f5f5f5;
}

.city-card {
  transition: all 0.3s ease;
}

.city-card:hover {
  transform: translateY(-2px);
}

.chart-card :deep(.ant-card-body) {
  padding: 16px;
}

.chart-container {
  width: 100%;
}

.detail-table :deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
}

.detail-table :deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f0f9ff;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-card :deep(.ant-card-body) {
  padding: 20px;
}

.cities-card :deep(.ant-card-body) {
  padding: 20px;
}

.table-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
