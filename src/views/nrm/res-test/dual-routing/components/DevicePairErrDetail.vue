<template>
  <div class="device-detail-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h1>设备对问题详情</h1>
        <span class="subtitle">{{ devicePair.b_pair }}</span>
      </div>
      <div class="page-actions">
        <a-button type="primary" @click="viewHistoryVersions" style="margin-right: 10px;">
          <template #icon>
            <history-outlined />
          </template>
          历史版本
        </a-button>
        <a-button @click="goBack">
          <template #icon>
            <left-outlined />
          </template>
          返回
        </a-button>
      </div>
    </div>

    <!-- 设备对基本信息 -->
    <a-card class="info-card" title="设备对基本信息" :loading="loading">
      <a-descriptions bordered :column="4">
        <a-descriptions-item label="设备对标识" :span="4">
          {{ devicePair.b_pair || '未知' }}
        </a-descriptions-item>

        <!-- 设备A信息 -->
        <a-descriptions-item label="设备A名称">
          {{ devicePair.deviceA?.device_name || '未知' }}
        </a-descriptions-item>
        <a-descriptions-item label="设备A编码">
          {{ devicePair.deviceA?.device_code || '未知' }}
        </a-descriptions-item>
        <a-descriptions-item label="设备A IP">
          {{ devicePair.deviceA?.device_ip || '未知' }}
        </a-descriptions-item>
        <a-descriptions-item label="设备A类型">
          {{ devicePair.deviceA?.device_type || '未知' }}
        </a-descriptions-item>

        <!-- 设备B信息 -->
        <a-descriptions-item label="设备B名称">
          {{ devicePair.deviceB?.device_name || '未知' }}
        </a-descriptions-item>
        <a-descriptions-item label="设备B编码">
          {{ devicePair.deviceB?.device_code || '未知' }}
        </a-descriptions-item>
        <a-descriptions-item label="设备B IP">
          {{ devicePair.deviceB?.device_ip || '未知' }}
        </a-descriptions-item>
        <a-descriptions-item label="设备B类型">
          {{ devicePair.deviceB?.device_type || '未知' }}
        </a-descriptions-item>

        <!-- 区域和创建时间 -->
        <a-descriptions-item label="区域" :span="2">
          {{ devicePair.region || '未知' }}
        </a-descriptions-item>
        <a-descriptions-item label="设备创建时间" :span="2">
          {{ devicePair.discoveryTime || '未知' }}
        </a-descriptions-item>
      </a-descriptions>
    </a-card>



    <!-- 问题详情卡片 -->
    <a-card class="info-card" title="问题详情" :loading="loading" :style="cardStyle">
      <a-tabs v-model:activeKey="activeTabKey">
        <a-tab-pane key="problem" tab="问题描述">
          <div class="problem-description">
            <div class="problem-tables">
              <!-- 1. 端口 & 光路异常数据 -->

              <div class="problem-table-section">


                <h3 class="table-title">
                  <a-tag color="red">端口/光路异常</a-tag>
                  <span class="table-title-count" v-if="portErrors.length > 0">{{ portErrors.length }}</span>
                </h3>
                <a-table
                  v-if="portErrors.length > 0"
                  :columns="portErrorColumns"
                  :data-source="portErrors"
                  :loading="loading"
                  :pagination="{ showSizeChanger: true, pageSizeOptions: ['5', '10', '20', '50'], showTotal: (total: number) => `共 ${total} 条记录` }"
                  :scroll="{ x: 1000 ,y:  500 }"
                  size="small"
                  :rowKey="() => 'id_' + Math.random().toString(36).substring(2, 10)"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'error'">
                      <a-tag :color="getProblemTypeColor(record.error)">{{ record.error }}</a-tag>
                    </template>
                    <template v-else-if="column.dataIndex === 'device_code'">
                      <span>{{ record.device_code || '无' }}</span>
                    </template>
                    <template v-else-if="column.dataIndex === 'device_name'">
                      <span>{{ record.device_name || '无' }}</span>
                    </template>
                    <template v-else-if="column.dataIndex === 'port_id'">
                      <span>{{ record.port_id || '无' }}</span>
                    </template>
                    <template v-else-if="column.dataIndex === 'port_name'">
                      <span>{{ record.port_name || '无' }}</span>
                    </template>
                    <template v-else-if="column.dataIndex === 'gl_code'">
                      <span>{{ record.gl_code || '无' }}</span>
                    </template>
                  </template>
                </a-table>
                <a-empty v-else description="暂无端口异常或光路异常数据" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
              </div>

              <!-- 2. 板卡异常清单 -->
              <div class="problem-table-section">
                <h3 class="table-title">
                  <a-tag color="orange">板卡异常</a-tag>
                  <span class="table-title-count" v-if="boardErrors.length > 0">{{ boardErrors.length }}</span>
                </h3>
                <a-table
                  v-if="boardErrors.length > 0"
                  :columns="boardErrorColumns"
                  :data-source="boardErrors"
                  :loading="loading"
                  :pagination="{ showSizeChanger: true, pageSizeOptions: ['5', '10', '20', '50'], showTotal: (total: number) => `共 ${total} 条记录` }"
                  :scroll="{ x: 1000 }"
                  size="small"
                  :rowKey="() => 'id_' + Math.random().toString(36).substring(2, 10)"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'error'">
                      <a-tag :color="getProblemTypeColor(record.error)">{{ record.error }}</a-tag>
                    </template>
                    <template v-else-if="column.dataIndex === 'device_code'">
                      <span>{{ record.device_code || '无' }}</span>
                    </template>
                    <template v-else-if="column.dataIndex === 'device_name'">
                      <span>{{ record.device_name || '无' }}</span>
                    </template>
                    <template v-else-if="column.dataIndex === 'board_id'">
                      <span>{{ record.board_id || '无' }}</span>
                    </template>
                    <template v-else-if="column.dataIndex === 'board_name'">
                      <span>{{ record.board_name || '无' }}</span>
                    </template>
                  </template>
                </a-table>
                <a-empty v-else description="暂无板卡异常数据" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
              </div>

              <!-- 3. 同光缆 & 无穿管 & 同管道异常清单 -->
              <div class="problem-table-section">
                <h3 class="table-title">
                  <a-tag color="blue">同光缆/无穿管/同管道异常</a-tag>
                  <span class="table-title-count" v-if="routeErrors.length > 0">{{ routeErrors.length }}</span>
                </h3>



                <a-table
                  v-if="routeErrors.length > 0"
                  :columns="routeErrorColumns"
                  :data-source="routeErrors"
                  :loading="loading"
                  :pagination="{ showSizeChanger: true, pageSizeOptions: ['5', '10', '20', '50'], showTotal: (total: number) => `共 ${total} 条记录` }"
                  :scroll="{ x: 1200 }"
                  size="small"
                  :rowKey="() => 'id_' + Math.random().toString(36).substring(2, 10)"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'error'">
                      <a-tag :color="getProblemTypeColor(record.error)">{{ record.error }}</a-tag>
                    </template>
                    <template v-else-if="column.dataIndex === 'cs_code'">
                      <span>{{ record.cs_code || '无' }}</span>
                    </template>
                    <template v-else-if="column.dataIndex === 'cable_code'">
                      <span>{{ record.cable_code || '无' }}</span>
                    </template>
                    <template v-else-if="column.dataIndex === 'zc_section_code'">
                      <span
                        :style="{
                          color: isSamePipeRecord(record) ? getSamePipeGroupColor(record) : 'inherit',
                          fontWeight: isSamePipeRecord(record) ? 'bold' : 'normal'
                        }"
                      >
                        {{ record.zc_section_code || '无' }}
                      </span>
                    </template>
                    <template v-else-if="column.dataIndex === 'gl_code'">
                      <span>{{ record.gl_code || '无' }}</span>
                    </template>
                  </template>
                </a-table>
                <a-empty v-else description="暂无同光缆/无穿管/同管道异常数据" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 悬浮按钮组 -->
    <div class="floating-buttons">
      <a-button type="primary" @click="viewHistoryVersions" class="floating-button">
        <template #icon>
          <history-outlined />
        </template>
        历史版本
      </a-button>
      <a-button
        v-if="hasMapOverlayContent"
        type="primary"
        :loading="savingVersion"
        :disabled="versionSaved"
        @click="showSaveVersionModal"
        class="floating-button"
      >
        <template #icon>
          <save-outlined />
        </template>
        {{ versionSaved ? '已保存当前版本' : '保存当前版本' }}
      </a-button>
    </div>

    <!-- 保存版本对话框 -->
    <a-modal
      v-model:visible="saveVersionModalVisible"
      title="保存当前版本"
      @ok="handleSaveVersionOk"
      @cancel="handleSaveVersionCancel"
      :maskClosable="false"
      :confirmLoading="savingVersion"
      :okButtonProps="{ disabled: savingVersion }"
    >
      <a-form :model="saveVersionForm" layout="vertical">
        <a-form-item label="版本描述" name="description">
          <a-textarea
            v-model:value="saveVersionForm.description"
            placeholder="请输入版本描述，例如：修复了某某问题"
            :rows="4"
            :maxlength="200"
            show-count
            :disabled="savingVersion"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 地图视图卡片 -->
    <a-card class="info-card" :loading="mapLoading" :style="cardStyle">
      <template #title>
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <span>地图视图</span>
          <a-tooltip>
            <template #title>
              只有当存在光路数据（同光缆/无穿管/同管道异常）时才能绘制地图。如果没有显示地图，请检查是否存在光路数据。
            </template>
            <a-button type="link" style="padding: 0;">
              <info-circle-outlined style="color: #1890ff; font-size: 16px;" />
            </a-button>
          </a-tooltip>
        </div>
      </template>
      <div class="map-container">
        <!-- 当没有光路数据时显示提示 -->
        <div v-if="routeErrors.length === 0" style="padding: 40px 0; text-align: center;">
          <a-empty description="没有可用的光路数据来绘制地图">
            <template #description>
              <div>
                <p style="font-size: 16px; margin-bottom: 8px;">没有可用的光路数据来绘制地图</p>
                <p style="color: #999;">只有当存在同光缆/无穿管/同管道异常数据时才能绘制地图</p>
              </div>
            </template>
          </a-empty>
        </div>

        <!-- 地图与支撑段列表容器 -->
        <div v-else class="map-container-layout">
          <!-- 支撑段列表区域 -->
          <div v-if="pipeColorFilters.length > 0" class="support-segment-list" style="flex-grow: 0; flex-shrink: 0;">
            <div class="support-segment-header">同光缆/同管道 列表</div>

            <!-- 分类标签 -->
            <a-radio-group v-model:value="segmentFilterType" button-style="solid" class="segment-type-filter">
              <a-radio-button value="cableSegment">同光缆清单</a-radio-button>
              <a-radio-button value="pipeSegment">同管道清单</a-radio-button>
            </a-radio-group>

            <a-list size="small" bordered class="support-segment-items">
              <a-list-item
                v-for="filter in filteredSegments"
                :key="filter.code"
                class="support-segment-item"
                :class="{ 'support-segment-item-selected': selectedColorFilters.includes(filter.code) }"
                :style="{
                  borderLeftColor: filter.color,
                  backgroundColor: selectedColorFilters.includes(filter.code) ? `rgba(${hexToRgb(filter.color)}, 0.1)` : 'transparent'
                }"
                @click="toggleColorFilter(filter.code)"
              >
                <div class="support-segment-item-content">
                  <div
                    class="color-indicator"
                    :style="{ backgroundColor: filter.color }"
                  ></div>
                  <div class="segment-info">
                    <div class="segment-code">{{ filter.code }}</div>
                    <div class="segment-name" v-if="filter.name && filter.name !== filter.code">{{ filter.name }}</div>
                    <div class="segment-type">
                      <a-tag :color="getSegmentTypeColor(filter.type)">
                        {{ getSegmentTypeLabel(filter.type) }}
                      </a-tag>
                      <a-tag v-if="filter.error" :color="getProblemTypeColor(filter.error)">
                        {{ filter.error }}
                      </a-tag>
                    </div>
                  </div>
                  <a-tag v-if="selectedColorFilters.includes(filter.code)" color="blue" class="selected-tag">已选中</a-tag>
                </div>
              </a-list-item>

              <!-- 空列表提示 -->
              <a-empty
                v-if="filteredSegments.length === 0"
                description="没有符合条件的数据"
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
                style="margin: 16px 0;"
              />

              <a-list-item class="support-segment-actions">
                <div class="support-segment-buttons">
                  <a-button
                    type="primary"
                    @click="enhanceSelectAllColorFilters"
                    :disabled="filteredSegments.length === 0 || filteredSegments.length === selectedColorFilters.length"
                  >
                    全选
                  </a-button>
                  <a-button
                    type="primary"
                    danger
                    @click="enhanceClearColorFilters"
                    :disabled="selectedColorFilters.length === 0"
                  >
                    清除
                  </a-button>
                </div>
              </a-list-item>
            </a-list>

            <!-- 设备清单 -->
            <div class="device-list-selection" style="flex-grow: 1; display: flex; flex-direction: column;">
              <div class="device-list-header">
                <div class="device-list-title">设备清单</div>
              </div>
              <div class="device-list-items" style="flex-grow: 1; min-height: 220px; max-height: none; overflow-y: auto;">
                <div v-if="uniqueDeviceList.length === 0" class="no-devices">
                  暂无设备数据
                </div>
                <div v-else class="device-items" style="display: flex; flex-direction: column; gap: 8px;">
                  <div
                    v-for="device in uniqueDeviceList"
                    :key="device.id"
                    class="device-item"
                    @click="focusOnDevice(device)"
                    style="height: 50px; margin-bottom: 2px;"
                  >
                    <div
                      class="device-icon"
                      :style="{ backgroundColor: device.device_index === 0 ? '#1890ff' : '#52c41a' }"
                    >
                      {{ getDeviceShortName(device) }}
                    </div>
                    <div class="device-info">
                      <div class="device-name">{{ device.name || '未命名设备' }}</div>
                      <div class="device-type">{{ device.spec_name || '未知类型' }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 地图容器区域 -->
          <div class="map-area" :class="{ 'map-area-full': pipeColorFilters.length === 0 }">
            <!-- 光路选择控件 -->
            <div class="map-controls">
              <div class="map-controls-content">
                <div class="optical-path-selection">
                  <a-checkbox-group v-model:value="checkedOptRoads" @change="onOptRoadCheckChange" class="optical-path-checkboxes">
                    <div v-if="optRoadGroups.length === 0" class="no-optical-paths">
                      暂无光路数据
                    </div>
                    <div v-else class="optical-path-groups">
                      <div v-for="(group, groupIndex) in optRoadGroups" :key="groupIndex" class="optical-path-group">
                        <div class="optical-path-group-row">
                          <div class="optical-path-label">光路选择：</div>
                          <div class="group-header">{{ group.name }}</div>
                          <div class="group-roads">
                            <a-checkbox
                              v-for="item in group.roads"
                              :key="item.value"
                              :value="item.value"
                              class="optical-path-checkbox"
                              :class="{ 'loading': !item.loaded }"
                              :style="{
                                borderColor: item.color,
                                backgroundColor: checkedOptRoads.includes(item.value) ? `${item.color}20` : 'transparent'
                              }"
                            >
                              <span :style="{ color: item.color, fontWeight: 'bold' }">{{ item.value }}</span>
                              <span class="color-indicator" :style="{ backgroundColor: item.color }"></span>
                              <span v-if="!item.loaded" class="loading-indicator">加载中...</span>
                            </a-checkbox>
                          </div>
                          <div class="group-actions">
                            <a-button size="small" @click="selectGroupOptRoads(group)">全选</a-button>
                            <a-button size="small" @click="unselectGroupOptRoads(group)">清空</a-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </a-checkbox-group>
                </div>

              </div>
            </div>

            <!-- 地图容器 -->
            <div class="map-wrapper">
              <!-- 地图容器 -->
              <div id="mapContainer" class="map-container-element"></div>

              <!-- 加载状态提示 (不遮挡地图) -->
              <div v-if="mapDataLoading" class="map-loading-indicator">
                <div class="map-loading-text">{{ mapLoadingText }}</div>
              </div>
            </div>


          </div>
        </div>
      </div>

      <!-- 不合规坐标表格 - 移动到地图视图卡片最下方 -->
      <div v-if="invalidCoordinates.length > 0" class="invalid-coordinates-section">
        <div class="invalid-coordinates-header">
          <div class="invalid-coordinates-title">坐标不合规清单 ({{ invalidCoordinates.length }})</div>
          <a-button
            type="link"
            size="small"
            @click="showInvalidCoordinates = !showInvalidCoordinates"
          >
            {{ showInvalidCoordinates ? '收起' : '展开' }}
          </a-button>
        </div>
        <div v-if="showInvalidCoordinates" class="invalid-coordinates-table">
          <a-table
            :dataSource="invalidCoordinates"
            :columns="invalidCoordinatesColumns"
            size="small"
            :pagination="{ pageSize: 5, showSizeChanger: true, pageSizeOptions: ['5', '10', '20'] }"
            :scroll="{ y: 200 }"
            :rowKey="() => 'id_' + Math.random().toString(36).substring(2, 10)"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'type'">
                <a-tag :color="record.type === '设备' ? 'blue' : (record.type === '光缆段' ? 'green' : 'orange')">
                  {{ text }}
                </a-tag>
              </template>
              <template v-else-if="column.dataIndex === 'reason'">
                <a-tag color="red">{{ text }}</a-tag>
              </template>
              <template v-else-if="column.dataIndex === 'cableSegment'">
                <span v-if="text">{{ text }}</span>
                <span v-else class="text-gray">-</span>
              </template>
              <template v-else-if="column.dataIndex === 'opticalPath'">
                <span v-if="text">
                  <a-tag color="purple">{{ text }}</a-tag>
                </span>
                <span v-else class="text-gray">-</span>
              </template>
              <template v-else-if="column.dataIndex === 'coordinates'">
                <span v-if="text === '缺少坐标数据'" class="text-gray">{{ text }}</span>
                <span v-else>{{ text }}</span>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message, Empty } from 'ant-design-vue';
import { LeftOutlined, InfoCircleOutlined, HistoryOutlined, SaveOutlined } from '@ant-design/icons-vue';
import { useUnifiedApi } from '@/hooks/web/useUnifiedApi';
import { useInfo } from '@/hooks/web/useRestAPI';
import useGis from '@/hooks/gis/useGis';
import { useUserStoreWithOut } from '@/store/modules/user';


defineOptions({
  name: 'DevicePairErrDetail'
});

const route = useRoute();
const router = useRouter();

// 获取用户存储
const userStore = useUserStoreWithOut();

// 获取地市代码
const areaCode = userStore.getAreaCode;
console.log('当前地市代码:', areaCode);

// 如果是江苏，默认查询无锡的数据
const defaultCityCode = (route.query.cityCode as string) || (areaCode === 'js' ? 'WX' : areaCode);
console.log('使用的城市代码:', defaultCityCode);

// 初始化统一API调用钩子
const unifiedApi = useUnifiedApi({
  rootPath: '/graph-rest-api', // 使用与其他页面一致的根路径
  defaultCityCode: defaultCityCode // 设置默认地市代码
});

const gisUtils = useGis();

// 从路由参数中获取设备对信息
const b_pair = ref(route.query.b_pair as string || '');
const deviceNameA = ref(route.query.deviceNameA as string || '');
const deviceNameB = ref(route.query.deviceNameB as string || '');
const deviceType = ref('未知'); // 默认设备类型

// 设备对信息
const devicePair = ref<any>({
  b_pair: b_pair.value,
  deviceA: {
    device_id: route.query.deviceIdA,
    device_code: route.query.deviceCodeA,
    device_name: deviceNameA.value,
    device_ip: ''
  },
  deviceB: {
    device_id: route.query.deviceIdB,
    device_code: route.query.deviceCodeB,
    device_name: deviceNameB.value,
    device_ip: ''
  },
  region: '',
  discoveryTime: ''
});

// 加载状态
const loading = ref(false);
const mapLoading = ref(false);

// 当前激活的标签页
const activeTabKey = ref('problem');

// 卡片样式
const cardStyle = {
  marginBottom: '16px'
};

// 定义问题详情类型
interface ProblemDetail {
  type: string;
  time: string;
  description: string;
  impact: string;
  source: string;
  details?: Record<string, any>;
}

// 问题详情列表
const problemDetails = ref<ProblemDetail[]>([]);

// 路由错误数据
const routeErrors = ref<any[]>([]);
const originalRouteErrors = ref<any[]>([]);
const groupedRouteErrors = ref<any[]>([]);

// 板卡异常数据
const boardErrors = ref<any[]>([]);
const originalBoardErrors = ref<any[]>([]);

// 端口&光路异常数据
const portErrors = ref<any[]>([]);
const originalPortErrors = ref<any[]>([]);

// 地图相关变量
const MapContainer = ref<any>(null);
const mapRef = ref<any>(null);
const overLayerRef = ref<any>(null);
const mapDevices = ref<any[]>([]);
const mapPipeSegments = ref<any[]>([]);
const mapLoadingText = ref('正在加载地图数据...');
const mapDataLoading = ref(false);

// 颜色筛选相关状态
const cableColorFilters = ref<{ code: string; color: string; type: string; name: string; error: string }[]>([]);
const pipeColorFilters = ref<{ code: string; color: string; type: string; name: string; error: string }[]>([]);
const selectedColorFilters = ref<string[]>([]);
const samePipeRecords = ref<Record<string, string[]>>({});
const sameCableRecords = ref<Record<string, string[]>>({});

// 段类型筛选
const segmentFilterType = ref('cableSegment');

// 计算属性：是否存在同光缆问题
const hasSameCableIssues = computed(() => {
  return originalRouteErrors.value.some(record => record.error === '同光缆');
});

// 计算属性：是否存在同管道问题
const hasSamePipeIssues = computed(() => {
  return originalRouteErrors.value.some(record => record.error === '同管道');
});

// 根据筛选类型过滤段列表
const filteredSegments = computed(() => {
  if (segmentFilterType.value === 'cableSegment') {
    // 显示同光缆问题
    return cableColorFilters.value.filter(filter =>
      filter.type === 'cable' && filter.error === '同光缆'
    );
  } else if (segmentFilterType.value === 'pipeSegment') {
    // 显示同管道问题
    return pipeColorFilters.value.filter(filter =>
      filter.type === 'pipeSegment' && filter.error === '同管道'
    );
  }

  // 默认返回空数组
  return [];
});

// 获取段类型颜色
const getSegmentTypeColor = (type: string) => {
  switch (type) {
    case 'optRoad':
      return 'purple';
    case 'cable':
      return 'blue';
    case 'cableSegment':
      return 'green';
    case 'pipeSegment':
      return 'orange';
    default:
      return 'default';
  }
};

// 获取问题类型颜色
const getProblemTypeColor = (error: string) => {
  if (!error) return 'default';

  if (error.includes('光路')) return 'purple';
  if (error.includes('光缆')) return 'blue';
  if (error.includes('板卡')) return 'red';
  if (error.includes('管道')) return 'orange';
  if (error.includes('穿管')) return 'gold';

  return 'default';
};

// 获取段类型标签
const getSegmentTypeLabel = (type: string) => {
  switch (type) {
    case 'cable':
      return '光缆';
    case 'cableSegment':
      return '光缆段';
    case 'pipeSegment':
      return '支撑段';
    default:
      return '未知';
  }
};

// 高亮线条相关
const highlightedLine = ref<any>(null);
const originalLineStyle = ref<any>(null);
const currentHighlightedPipeCode = ref<string | null>(null);
const lineLayerMap = ref<Map<string, any>>(new Map());

// 光路选项
const optRoadOptions = ref<{ value: string; color: string; devicePair?: string; loaded?: boolean }[]>([]);
const checkedOptRoads = ref<string[]>([]);

// 光路分组
interface OptRoadGroup {
  name: string;
  roads: { value: string; color: string; loaded?: boolean }[];
}
const optRoadGroups = ref<OptRoadGroup[]>([]);

// 已加载的光路编码
const loadedOptRoads = ref<Set<string>>(new Set());

// 光缆段和管道段的关系映射
const cableSegmentToPipeSegmentMap = new Map<string, Set<string>>();

// 当前选中的光缆段
const selectedCableSegment = ref<string>('');

// 重合管道段的颜色
const OVERLAP_COLOR = '#9c27b0'; // 紫色

// 设备清单
const deviceList = ref<any[]>([]);

// 去重后的设备清单
const uniqueDeviceList = computed(() => {
  // 使用Map来存储唯一设备，以设备ID为键
  const uniqueDevices = new Map();

  deviceList.value.forEach(device => {
    if (!uniqueDevices.has(device.id)) {
      uniqueDevices.set(device.id, device);
    }
  });

  // 将Map转换为数组并返回
  return Array.from(uniqueDevices.values());
});

// 当前选中的设备
const selectedDevice = ref<any>(null);

// 设备标记图层映射
const deviceMarkerMap = ref<Map<string, any>>(new Map());

// 保存版本相关状态
const savingVersion = ref(false);
const versionSaved = ref(false);
const saveVersionModalVisible = ref(false);
const saveVersionForm = ref({
  description: ''
});

// 检查地图是否有内容可以保存
const hasMapOverlayContent = computed(() => {
  return mapPipeSegments.value.length > 0 || mapDevices.value.length > 0;
});

// 不合规坐标数据
interface InvalidCoordinate {
  id: string;
  type: string;
  code: string;
  name: string;
  reason: string;
  coordinates: string;
  cableSegment?: string;  // 光缆段
  opticalPath?: string;   // 光路
  rawData?: any;          // 原始数据，用于调试
}

const invalidCoordinates = ref<InvalidCoordinate[]>([]);
const showInvalidCoordinates = ref(false);

// 定义不合规坐标表格列
const invalidCoordinatesColumns = [
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 80
  },
  {
    title: '编码',
    dataIndex: 'code',
    key: 'code',
    width: 150
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    width: 150
  },
  {
    title: '光缆段',
    dataIndex: 'cableSegment',
    key: 'cableSegment',
    width: 150
  },
  {
    title: '光路',
    dataIndex: 'opticalPath',
    key: 'opticalPath',
    width: 150
  },
  {
    title: '原因',
    dataIndex: 'reason',
    key: 'reason',
    width: 150
  },
  {
    title: '坐标',
    dataIndex: 'coordinates',
    key: 'coordinates',
    width: 200
  }
];

// 定义路由错误表格列
const getRouteErrorColumns = () => {
  // 从原始数据中提取唯一的错误类型
  const uniqueErrorTypes = [...new Set(originalRouteErrors.value.map(record => record.error))].filter(Boolean);
  const errorTypeFilters = uniqueErrorTypes.map(type => ({ text: type, value: type }));

  // 从原始数据中提取唯一的光缆编码
  const uniqueCableCodes = [...new Set(originalRouteErrors.value
    .filter(record => record.cable_code)
    .map(record => record.cable_code))];
  const cableCodeFilters = uniqueCableCodes.map(code => ({ text: code, value: code }));

  // 从原始数据中提取唯一的支撑段编码
  const uniquePipeCodes = [...new Set(originalRouteErrors.value
    .filter(record => record.zc_section_code)
    .map(record => record.zc_section_code))];
  const pipeCodeFilters = uniquePipeCodes.map(code => ({ text: code, value: code }));

  return [
    {
      title: '错误类型',
      dataIndex: 'error',
      key: 'error',
      width: 120,
      fixed: 'left',
      filters: errorTypeFilters,
      onFilter: (value: string, record: any) => record.error === value,
      filterMultiple: true
    },
    {
      title: '光路编码',
      dataIndex: 'gl_code',
      key: 'gl_code',
      width: 180
    },
    {
      title: '光缆段编码',
      dataIndex: 'cs_code',
      key: 'cs_code',
      width: 180
    },
    {
      title: '光缆编码',
      dataIndex: 'cable_code',
      key: 'cable_code',
      width: 180,
      filters: cableCodeFilters,
      onFilter: (value: string, record: any) => record.cable_code === value,
      filterMultiple: true
    },
    {
      title: '支撑段编码',
      dataIndex: 'zc_section_code',
      key: 'zc_section_code',
      width: 180,
      filters: pipeCodeFilters,
      onFilter: (value: string, record: any) => record.zc_section_code === value,
      filterMultiple: true
    },
    {
      title: '设备编码',
      dataIndex: 'device_code',
      key: 'device_code',
      width: 180
    },
    {
      title: '设备名称',
      dataIndex: 'device_name',
      key: 'device_name',
      width: 200
    },
    {
      title: '设备类型',
      dataIndex: 'eqp_spec',
      key: 'eqp_spec',
      width: 120
    },
    {
      title: '区域',
      dataIndex: 'area_name',
      key: 'area_name',
      width: 120
    },
    {
      title: '城市',
      dataIndex: 'city_name',
      key: 'city_name',
      width: 120
    }
  ];
};

// 初始化路由错误表格列
const routeErrorColumns = ref(getRouteErrorColumns());

// 定义板卡异常表格列
const boardErrorColumns = [
  {
    title: '错误类型',
    dataIndex: 'error',
    key: 'error',
    width: 120,
    fixed: 'left'
  },
  {
    title: '设备编码',
    dataIndex: 'device_code',
    key: 'device_code',
    width: 180
  },
  {
    title: '设备名称',
    dataIndex: 'device_name',
    key: 'device_name',
    width: 200
  },
  {
    title: '设备类型',
    dataIndex: 'eqp_spec',
    key: 'eqp_spec',
    width: 120
  },
  {
    title: '板卡ID',
    dataIndex: 'board_id',
    key: 'board_id',
    width: 120
  },
  {
    title: '板卡名称',
    dataIndex: 'board_name',
    key: 'board_name',
    width: 180
  },
  {
    title: '板卡类型',
    dataIndex: 'board_type',
    key: 'board_type',
    width: 120
  },
  {
    title: '区域',
    dataIndex: 'area_name',
    key: 'area_name',
    width: 120
  },
  {
    title: '城市',
    dataIndex: 'city_name',
    key: 'city_name',
    width: 120
  }
];

// 定义端口&光路异常表格列
const portErrorColumns = [
  {
    title: '错误类型',
    dataIndex: 'error',
    key: 'error',
    width: 120,
    fixed: 'left'
  },
  {
    title: '设备编码',
    dataIndex: 'device_code',
    key: 'device_code',
    width: 180
  },
  {
    title: '设备名称',
    dataIndex: 'device_name',
    key: 'device_name',
    width: 200
  },
  {
    title: '设备类型',
    dataIndex: 'eqp_spec',
    key: 'eqp_spec',
    width: 120
  },
  {
    title: '端口ID',
    dataIndex: 'port_id',
    key: 'port_id',
    width: 120
  },
  {
    title: '端口名称',
    dataIndex: 'port_name',
    key: 'port_name',
    width: 180
  },
  {
    title: '光路编码',
    dataIndex: 'gl_code',
    key: 'gl_code',
    width: 180
  },
  {
    title: '区域',
    dataIndex: 'area_name',
    key: 'area_name',
    width: 120
  },
  {
    title: '城市',
    dataIndex: 'city_name',
    key: 'city_name',
    width: 120
  }
];

// 返回上一页
const goBack = () => {
  router.back();
};

// 查看历史版本
const viewHistoryVersions = () => {
  console.log('查看历史版本');

  // 跳转到历史版本查看页面
  router.push({
    path: '/nrm/dualrouting/history-version-view',
    query: {
      protectionGroupId: devicePair.value.b_pair,
      protectionGroupName: `设备对 ${devicePair.value.deviceA?.device_name || '未知'} - ${devicePair.value.deviceB?.device_name || '未知'}`,
      protectionGroupType: 'dualDeviceDualRoute',
      scenarioType: 'dualDeviceDualRoute',
      areaName: devicePair.value.region || '',
      cityName: '',
      cityCode: defaultCityCode
    }
  });
};

// 显示保存版本对话框
const showSaveVersionModal = () => {
  // 如果已经保存过，不再显示对话框
  if (versionSaved.value) {
    message.info('当前版本已保存，无需重复保存');
    return;
  }

  // 检查是否有地图数据可以保存
  if (!hasMapOverlayContent.value) {
    message.warning('没有可用的地图数据，无法保存版本');
    return;
  }

  // 显示对话框
  saveVersionModalVisible.value = true;
};

// 处理保存版本对话框确认
const handleSaveVersionOk = async () => {
  // 如果已经在保存中，不再重复执行
  if (savingVersion.value) {
    console.log('保存操作正在进行中，请勿重复提交');
    return;
  }

  // 如果已经保存过，不再重复保存
  if (versionSaved.value) {
    message.info('当前版本已保存，无需重复保存');
    saveVersionModalVisible.value = false;
    return;
  }

  // 检查是否有地图数据可以保存
  if (!hasMapOverlayContent.value) {
    message.warning('没有可用的地图数据，无法保存版本');
    saveVersionModalVisible.value = false;
    return;
  }

  // 设置保存中状态
  savingVersion.value = true;

  try {
    console.log('开始保存版本');

    // 创建一个对象存储所有光路的原始API返回数据
    const opticalPathData: Record<string, any> = {};

    // 收集所有已加载的光路数据
    const loadedOpticalPaths = Array.from(loadedOptRoads.value);

    console.log('需要保存的光路编码:', loadedOpticalPaths);

    // 对每个光路编码调用API获取原始数据
    for (const glCode of loadedOpticalPaths) {
      try {
        console.log(`获取光路 ${glCode} 的原始数据用于保存`);

        // 创建一个临时的 infoService 来获取光路数据
        const optRoadInfoService = useInfo({
          rootPath: '/graph-rest-api'
        });

        // 设置 info 值
        optRoadInfoService.info.value = {
          code: glCode, // 光路编码
          areaCode: defaultCityCode.toLowerCase() // 城市代码，使用小写
        };

        // 调用 API 获取原始数据
        console.log(`调用 API: /api/customer-topology/opt_road_gis (光路: ${glCode})`);
        const result = await optRoadInfoService.doCreateNew('/api/customer-topology/opt_road_gis');

        if (!result) {
          console.warn(`光路 ${glCode} 没有返回数据，跳过该光路`);
          continue; // 跳过该光路，不使用备选方案
        } else {
          console.log(`光路 ${glCode} 返回数据成功，保存原始API返回数据`);

          // 保存原始API返回数据，确保包含pipe_segments和road_both_point_devices_xy字段
          opticalPathData[glCode] = result;
        }
      } catch (error) {
        console.error(`获取光路 ${glCode} 原始数据出错:`, error);
        // 出错时跳过该光路，不使用备选方案
        continue;
      }
    }

    // 构建保存参数
    const params = {
      scenarioType: 'dualDeviceDualRoute',
      protectionGroupId: devicePair.value.b_pair,
      protectionGroupName: `设备对 ${devicePair.value.deviceA?.device_name || '未知'} - ${devicePair.value.deviceB?.device_name || '未知'}`,
      // 设备对场景不应该存储deviceCode
      deviceCode: '',
      // 将b_pair存储到device_pair_id字段
      devicePairId: devicePair.value.b_pair,
      // 确保保存城市和区域代码
      cityCode: defaultCityCode.toLowerCase(),
      areaCode: areaCode.toLowerCase(),
      description: saveVersionForm.value.description,
      geojsonData: JSON.stringify({
        type: 'OpticalPathCollection',
        opticalPaths: opticalPathData,
        // 添加一些元数据
        metadata: {
          devicePair: devicePair.value.b_pair,
          deviceNameA: devicePair.value.deviceA?.device_name,
          deviceNameB: devicePair.value.deviceB?.device_name,
          deviceCodeA: devicePair.value.deviceA?.device_code,
          deviceCodeB: devicePair.value.deviceB?.device_code,
          cityCode: defaultCityCode.toLowerCase(),
          areaCode: areaCode.toLowerCase(),
          createTime: new Date().toISOString(),
          version: '1.0',
          description: saveVersionForm.value.description, // 在元数据中也添加描述
          scenarioType: 'dualDeviceDualRoute'
        }
      }),
      creator: userStore.getUserInfo?.realName || userStore.getUserInfo?.username || '未知用户'
    };

    console.log('保存版本参数:', params);

    // 调用API保存版本
    const response = await unifiedApi.callApi(
      'SLY_MAP_HISTORY_INSERT',
      'V20250516102514460',
      params,
      defaultCityCode.toLowerCase()
    );

    console.log('保存版本API响应:', response);

    if (response && (response.code === '0' || response.success)) {
      message.success('保存版本成功');
      // 标记已保存，禁用保存按钮
      versionSaved.value = true;
      // 关闭对话框
      saveVersionModalVisible.value = false;
    } else {
      message.error('保存版本失败: ' + (response?.message || '未知错误'));
    }
  } catch (error) {
    console.error('保存版本出错:', error);
    message.error('保存版本出错: ' + (error.message || '未知错误'));
  } finally {
    // 无论成功或失败，都重置保存中状态
    savingVersion.value = false;
  }
};

// 处理保存版本对话框取消
const handleSaveVersionCancel = () => {
  saveVersionModalVisible.value = false;
};

// 不再需要标签页切换函数

// 不再需要这个函数，使用上面定义的 getProblemTypeColor 函数

// 将十六进制颜色转换为 RGB 格式
const hexToRgb = (hex: string) => {
  // 去除 # 前缀
  hex = hex.replace(/^#/, '');

  // 将简写的颜色转换为完整格式
  if (hex.length === 3) {
    hex = hex.split('').map(char => char + char).join('');
  }

  // 解析 RGB 值
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  // 返回 RGB 格式的字符串
  return `${r}, ${g}, ${b}`;
};

// 处理光路选择变化
const onOptRoadCheckChange = () => {
  console.log('选中的光路:', checkedOptRoads.value);
  updateMapDisplay();
};

// 注意：全局全选和清空功能已移除，改为每个设备分组单独的全选和清空

// 全选指定设备分组的光路
const selectGroupOptRoads = (group: any) => {
  // 获取该分组中所有光路的值
  const groupRoadValues = group.roads.map((road: any) => road.value);

  // 将该分组的光路添加到已选中的光路中（避免重复）
  const newCheckedRoads = [...checkedOptRoads.value];

  groupRoadValues.forEach((value: string) => {
    if (!newCheckedRoads.includes(value)) {
      newCheckedRoads.push(value);
    }
  });

  checkedOptRoads.value = newCheckedRoads;
  console.log(`全选设备分组 ${group.name} 的光路:`, groupRoadValues);
  updateMapDisplay();
};

// 清空指定设备分组的光路
const unselectGroupOptRoads = (group: any) => {
  // 获取该分组中所有光路的值
  const groupRoadValues = group.roads.map((road: any) => road.value);

  // 从已选中的光路中移除该分组的光路
  checkedOptRoads.value = checkedOptRoads.value.filter(
    value => !groupRoadValues.includes(value)
  );

  console.log(`清空设备分组 ${group.name} 的光路`);
  updateMapDisplay();
};

// 获取设备简称
const getDeviceShortName = (device: any) => {
  if (!device) return '?';

  // 如果有设备类型名称，使用设备类型
  if (device.spec_name) {
    // 如果设备类型名称过长，只取前两个字
    return device.spec_name.length > 2 ? device.spec_name.substring(0, 2) : device.spec_name;
  } else if (device.name) {
    // 如果没有设备类型名称但有设备名称，取设备名称的第一个字
    return device.name.charAt(0);
  }

  return '设';
};

// 检查坐标是否合规
const checkCoordinateValidity = (x: number, y: number): { valid: boolean; reason: string } => {
  // 检查坐标是否为NaN
  if (isNaN(x) || isNaN(y)) {
    return { valid: false, reason: '坐标值不是有效数字' };
  }

  // 检查坐标是否在中国大陆范围内
  // 中国大陆经度范围大约为73°E-135°E，纬度范围大约为3°N-53°N
  if (x < 73 || x > 135) {
    return { valid: false, reason: '经度超出有效范围(73-135)' };
  }

  if (y < 3 || y > 53) {
    return { valid: false, reason: '纬度超出有效范围(3-53)' };
  }

  return { valid: true, reason: '' };
};

// 检查管线坐标是否合规
const checkPipeSegmentCoordinates = (segment: any): { valid: boolean; reason: string } => {
  if (!segment.wkt) {
    return { valid: false, reason: '缺少WKT坐标数据' };
  }

  try {
    // 解析WKT字符串，提取坐标点
    const wkt = segment.wkt;

    // 简单检查WKT格式是否正确
    if (!wkt.startsWith('LINESTRING') && !wkt.startsWith('MULTILINESTRING')) {
      return { valid: false, reason: 'WKT格式不正确' };
    }

    // 提取坐标点
    const coordsMatch = wkt.match(/\d+\.\d+\s+\d+\.\d+/g);
    if (!coordsMatch || coordsMatch.length < 2) {
      return { valid: false, reason: '坐标点数量不足' };
    }

    // 检查每个坐标点是否合规
    for (const coordPair of coordsMatch) {
      const [x, y] = coordPair.split(/\s+/).map(parseFloat);
      const result = checkCoordinateValidity(x, y);
      if (!result.valid) {
        return result;
      }
    }

    return { valid: true, reason: '' };
  } catch (error) {
    return { valid: false, reason: '坐标解析错误: ' + (error.message || '未知错误') };
  }
};

// 聚焦到设备
const focusOnDevice = (device: any) => {
  console.log('聚焦到设备:', device);

  if (!device || !device.id || !mapRef.value) {
    console.warn('设备信息不完整或地图未初始化');
    return;
  }

  // 设置当前选中的设备
  selectedDevice.value = device;

  // 获取设备标记
  const marker = deviceMarkerMap.value.get(device.id);
  if (!marker) {
    console.warn('找不到设备标记:', device.id);

    // 如果找不到标记但有坐标，直接定位到坐标
    if (device.pos_x && device.pos_y) {
      const pos_x = parseFloat(device.pos_x);
      const pos_y = parseFloat(device.pos_y);

      // 设置地图视图
      mapRef.value.setView([pos_y, pos_x], 16, { animate: true });

      // 创建一个临时弹窗
      const Ai = (window as any).Ai;
      const popupContent = `<div>
        <p><strong>设备名称：</strong>${device.name || '-'}</p>
        <p><strong>设备编码：</strong>${device.device_code || '-'}</p>
        <p><strong>设备类型：</strong>${device.spec_name || '-'}</p>
      </div>`;

      Ai.Popup({ offset: [0, 0] })
        .setLatLng([pos_y, pos_x])
        .setContent(popupContent)
        .openOn(mapRef.value);
    }
    return;
  }

  // 设置地图视图到设备位置
  const pos_x = parseFloat(device.pos_x);
  const pos_y = parseFloat(device.pos_y);
  mapRef.value.setView([pos_y, pos_x], 16, { animate: true });

  // 打开设备弹窗
  marker.openPopup();
};

// 初始化地图
const initializeMap = async () => {
  try {
    console.log('开始初始化地图');

    // 不显示加载状态，让地图静默初始化
    mapDataLoading.value = false;

    // 检查地图 API 是否已加载
    const Ai = (window as any).Ai;
    if (!Ai) {
      console.error('地图 API (Ai) 未加载，请确保已引入地图相关的 JS 文件');
      return false;
    }
    console.log('地图 API 已加载:', Object.keys(Ai));

    // 检查是否有路由错误数据
    if (routeErrors.value.length === 0) {
      console.warn('没有路由错误数据，不需要初始化地图');
      return true; // 返回 true 表示处理成功，只是没有数据
    }

    // 获取地图容器
    const mapContainerElement = document.getElementById('mapContainer');
    if (!mapContainerElement) {
      console.error('地图容器不存在，无法初始化地图');
      return false;
    }
    console.log('地图容器尺寸:', mapContainerElement.offsetWidth, 'x', mapContainerElement.offsetHeight);

    // 获取 AK
    try {
      const ak = await gisUtils.getAk();
      console.log('AK:', ak);

      // 创建地图对象
      const map = new Ai.Map('mapContainer', {
        ak: ak,
        crs: '',
        mapType: '',
        maxZoom: 19,
        closePopupOnClick: true,
        contextmenu: true,
        contextmenuWidth: 140,
        contextmenuItems: [
          {
            text: '获取坐标',
            callback: gisUtils.showCoordinates,
          },
          {
            text: '测量距离',
            callback: measureDistance,
          },
        ],
      });

      // 添加江苏底图 - 尝试使用更可靠的地图服务URL
      let maplayer: any;
      try {
        // 尝试使用相对路径
        maplayer = Ai.TileLayer('/gis-platform-new/elec/js_map/server/wmts');
        map.addLayer(maplayer);
        console.log('使用相对路径加载地图成功');
      } catch (error) {
        console.error('使用相对路径加载地图失败，尝试备用URL:', error);
        try {
          // 备用方案1：使用绝对路径
          maplayer = Ai.TileLayer('http://10.128.86.64:8000/serviceAgent/rest/jtgis/elec/js_map/server/wmts');
          map.addLayer(maplayer);
          console.log('使用备用URL1加载地图成功');
        } catch (error2) {
          console.error('使用备用URL1加载地图失败，尝试备用URL2:', error2);
          try {
            // 备用方案2：使用另一个绝对路径
            maplayer = Ai.TileLayer('http://10.143.25.248:21000/elec/js_map/server/wmts');
            map.addLayer(maplayer);
            console.log('使用备用URL2加载地图成功');
          } catch (error3) {
            console.error('所有地图URL都加载失败:', error3);
            message.warning('地图服务连接失败，可能影响地图显示');
          }
        }
      }

      // 创建图层
      const overLayer = new Ai.FeatureGroup();
      map.addLayer(overLayer);

      // 创建 MapContainer 对象
      const mapContainerObj = { map: map, overLayer: overLayer, ak: ak };

      // 设置地图对象
      MapContainer.value = mapContainerObj;
      mapRef.value = map;
      overLayerRef.value = overLayer;

      console.log('地图对象初始化成功:', mapRef.value);
      console.log('图层对象初始化成功:', overLayerRef.value);

      // 定位到默认城市
      gisUtils.pantoCity(MapContainer.value, defaultCityCode.toLowerCase());

      // 添加比例尺
      Ai.Scale({ position: 'bottomright' }).addTo(mapRef.value);

      console.log('地图初始化成功');
      return true;
    } catch (akError) {
      console.error('获取 AK 失败:', akError);
      return false;
    }
  } catch (error) {
    console.error('初始化地图出错:', error);
    message.error('初始化地图出错');
    return false;
  }
};

// 测量距离
const measureDistance = () => {
  if (mapRef.value) {
    const Ai = (window as any).Ai;
    if (Ai && Ai.MeasureTool) {
      Ai.MeasureTool(mapRef.value, 'distince', function () {}); // 测距
    } else {
      message.error('地图API未正确加载，无法使用测距功能');
    }
  } else {
    message.error('地图未初始化，无法使用测距功能');
  }
};

// 切换颜色筛选
const toggleColorFilter = (code: string) => {
  // 处理光缆段高亮
  handleCableSegmentHighlight(code);

  // 检查是否已经选中
  const index = selectedColorFilters.value.indexOf(code);
  const isAdding = index === -1;

  if (isAdding) {
    // 如果未选中，则添加到选中列表
    selectedColorFilters.value.push(code);
  } else {
    // 如果已经选中，则取消选中
    selectedColorFilters.value.splice(index, 1);
  }

  // 更新地图显示
  updateMapDisplay();

  // 处理路由错误数据，更新表格
  processRouteErrors();

  // 更新表格列的筛选状态
  nextTick(() => {
    // 重新渲染表格，确保筛选状态更新
    // 由于 routeErrorColumns 是 ref 对象，需要使用 value 属性访问其值
    const columns = routeErrorColumns.value;
    for (let i = 0; i < columns.length; i++) {
      const column = columns[i];
      if (column.dataIndex === 'cable_code' || column.dataIndex === 'zc_section_code') {
        // 触发表格重新渲染
        column.filtered = selectedColorFilters.value.length > 0;
      }
    }
  });

  // 只有在添加筛选时才跳转地图位置
  if (isAdding) {
    // 延迟一下，确保地图已经更新
    setTimeout(() => {
      // 先检查是否是同光缆问题
      const cableFilter = cableColorFilters.value.find(filter =>
        filter.code === code && filter.type === 'cable' && filter.error === '同光缆'
      );

      if (cableFilter) {
        // 处理同光缆问题
        // 查找与该光缆相关的记录
        const relatedRecords = originalRouteErrors.value.filter(
          record => record.error === '同光缆' && record.cable_code === code
        );

        if (relatedRecords.length > 0) {
          // 从这些记录中提取光缆段编码
          const cableSegmentCodes = new Set<string>();
          relatedRecords.forEach(record => {
            if (record.cs_code) {
              cableSegmentCodes.add(record.cs_code);
            }
          });

          // 收集所有相关的管道段
          const relatedPipeSegments = new Set<string>();
          cableSegmentCodes.forEach(csCode => {
            const pipeSegments = cableSegmentToPipeSegmentMap.get(csCode);
            if (pipeSegments && pipeSegments.size > 0) {
              pipeSegments.forEach(ps => relatedPipeSegments.add(ps));
            }
          });

          // 找到第一个相关的管道段
          if (relatedPipeSegments.size > 0) {
            const firstPipeCode = Array.from(relatedPipeSegments)[0];
            const segment = mapPipeSegments.value.find(s =>
              (s.code === firstPipeCode || s.zc_section_code === firstPipeCode) &&
              s.a_pos_x && s.a_pos_y && s.z_pos_x && s.z_pos_y
            );

            if (segment) {
              // 计算管道段的中心点
              const centerX = (parseFloat(segment.a_pos_x) + parseFloat(segment.z_pos_x)) / 2;
              const centerY = (parseFloat(segment.a_pos_y) + parseFloat(segment.z_pos_y)) / 2;

              // 定位到该管道段，使用适当的缩放级别
              mapRef.value.setView([centerY, centerX], 17, { animate: true });
            }
          }
        }
      } else {
        // 处理支撑段或光缆段问题
        // 找到对应的管道段
        const segment = mapPipeSegments.value.find(s =>
          (s.code === code || s.zc_section_code === code) &&
          s.a_pos_x && s.a_pos_y && s.z_pos_x && s.z_pos_y
        );

        if (segment) {
          // 计算管道段的中心点
          const centerX = (parseFloat(segment.a_pos_x) + parseFloat(segment.z_pos_x)) / 2;
          const centerY = (parseFloat(segment.a_pos_y) + parseFloat(segment.z_pos_y)) / 2;

          // 定位到该管道段，使用适当的缩放级别
          mapRef.value.setView([centerY, centerX], 17, { animate: true });
        }
      }
    }, 100);
  }
};

// 清除颜色筛选
const clearColorFilters = () => {
  // 清空选中列表
  selectedColorFilters.value = [];

  // 清除光缆段高亮
  selectedCableSegment.value = '';
  resetPipeSegmentColors();

  // 更新地图显示
  updateMapDisplay();

  // 处理路由错误数据，更新表格
  processRouteErrors();

  // 延迟一下，确保地图已经更新
  setTimeout(() => {
    // 清除筛选时，显示所有管道段，使用适当的缩放级别
    if (overLayerRef.value && mapRef.value) {
      const bounds = overLayerRef.value.getBounds();
      if (bounds) {
        // 使用 fitBounds 而不是固定的缩放级别，以显示所有管道段
        mapRef.value.fitBounds(bounds, {
          padding: [50, 50],
          maxZoom: 15 // 限制最大缩放级别，避免缩放过大
        });
      }
    }
  }, 100);
};

// 全选所有段
const selectAllColorFilters = () => {
  console.log('全选所有段，当前筛选类型:', segmentFilterType.value);

  // 将当前筛选类型的所有段编码添加到选中列表
  selectedColorFilters.value = filteredSegments.value.map(filter => filter.code);

  // 如果是光缆段，需要高亮显示相关的管道段
  if (segmentFilterType.value === 'cableSegment') {
    console.log('全选光缆段，高亮显示相关管道段');

    // 清除之前的高亮
    resetPipeSegmentColors();

    // 获取第一个光缆段编码
    const firstCableSegmentCode = filteredSegments.value.length > 0 ? filteredSegments.value[0].code : null;

    // 如果有光缆段，设置为当前选中的光缆段
    if (firstCableSegmentCode) {
      selectedCableSegment.value = firstCableSegmentCode;

      // 高亮显示所有光缆段相关的管道段
      filteredSegments.value.forEach(segment => {
        const relatedPipeSegments = cableSegmentToPipeSegmentMap.get(segment.code);
        if (relatedPipeSegments && relatedPipeSegments.size > 0) {
          console.log(`光缆段 ${segment.code} 有 ${relatedPipeSegments.size} 个相关管道段`);

          // 遍历所有管道段，设置颜色
          mapPipeSegments.value.forEach(pipeSegment => {
            const pipeCode = pipeSegment.code || pipeSegment.zc_section_code;
            if (pipeCode && relatedPipeSegments.has(pipeCode)) {
              // 如果是相关管道段，设置为紫色
              pipeSegment.highlightColor = OVERLAP_COLOR;
              pipeSegment.isHighlighted = true;
            }
          });
        }
      });
    }
  }

  // 更新地图显示
  updateMapDisplay();

  // 延迟一下，确保地图已经更新
  setTimeout(() => {
    // 如果有段，将视图设置到第一个段的中心点
    if (filteredSegments.value.length > 0 && mapRef.value) {
      // 获取第一个段的编码
      const firstSegmentCode = filteredSegments.value[0].code;
      console.log('第一个段编码:', firstSegmentCode);

      // 在地图数据中查找该段
      let firstSegment: any = null;

      if (segmentFilterType.value === 'cableSegment') {
        // 如果是光缆段，需要找到相关的管道段
        const relatedPipeSegments = cableSegmentToPipeSegmentMap.get(firstSegmentCode);
        if (relatedPipeSegments && relatedPipeSegments.size > 0) {
          const firstPipeCode = Array.from(relatedPipeSegments)[0];
          firstSegment = mapPipeSegments.value.find(segment => {
            const segmentCode = segment.code || segment.zc_section_code;
            return segmentCode === firstPipeCode;
          });
        }
      } else {
        // 如果是支撑段，直接查找
        firstSegment = mapPipeSegments.value.find(segment => {
          const segmentCode = segment.code || segment.zc_section_code;
          return segmentCode === firstSegmentCode;
        });
      }

      if (firstSegment && firstSegment.a_pos_x && firstSegment.a_pos_y &&
          firstSegment.z_pos_x && firstSegment.z_pos_y) {
        // 获取第一个段的中心点
        const a_pos_x = parseFloat(String(firstSegment.a_pos_x));
        const a_pos_y = parseFloat(String(firstSegment.a_pos_y));
        const z_pos_x = parseFloat(String(firstSegment.z_pos_x));
        const z_pos_y = parseFloat(String(firstSegment.z_pos_y));

        // 计算中心点
        const centerX = (a_pos_x + z_pos_x) / 2;
        const centerY = (a_pos_y + z_pos_y) / 2;

        console.log('设置视图到中心点:', centerY, centerX);
        // 设置视图到第一个段的中心点
        mapRef.value.setView([centerY, centerX], 16, { animate: true });
      } else {
        console.log('找不到第一个段或坐标不完整，显示所有段');
        // 如果找不到第一个段或坐标不完整，则显示所有段
        if (overLayerRef.value) {
          const bounds = overLayerRef.value.getBounds();
          if (bounds) {
            mapRef.value.fitBounds(bounds, {
              padding: [50, 50],
              maxZoom: 15 // 限制最大缩放级别
            });
          }
        }
      }
    }
  }, 100);
};

// 高亮显示管道段
const highlightPipeSegment = (lineLayer: any) => {
  try {
    // 如果已经有高亮的线条，先恢复其原始样式
    if (highlightedLine.value && originalLineStyle.value) {
      highlightedLine.value.setStyle(originalLineStyle.value);
    }

    // 保存当前线条的原始样式
    originalLineStyle.value = {
      color: lineLayer.options.color,
      weight: lineLayer.options.weight,
      opacity: lineLayer.options.opacity
    };

    // 设置高亮样式
    lineLayer.setStyle({
      color: '#722ed1', // 高亮颜色（紫色）
      weight: 6,     // 加粗线条
      opacity: 1     // 完全不透明
    });

    // 记录当前高亮的线条
    highlightedLine.value = lineLayer;
    currentHighlightedPipeCode.value = lineLayer.pipeCode;

    // 显示弹窗
    if (lineLayer.pipeCode) {
      // 获取线条的中心点
      const center = lineLayer.getCenter();

      // 创建弹窗内容
      let popupContent = `<div style="padding: 8px;">`;

      // 添加管道段信息
      popupContent += `<p style="margin: 0; font-weight: bold;">管道段：${lineLayer.pipeCode}</p>`;

      // 添加错误类型
      if (lineLayer.error) {
        popupContent += `<p style="margin: 4px 0 0;">错误类型：${lineLayer.error}</p>`;
      }

      // 添加光路信息
      if (lineLayer.optRoadList && lineLayer.optRoadList.length > 0) {
        popupContent += `<p style="margin: 4px 0 0;">光路：`;
        lineLayer.optRoadList.forEach((road: any, index: number) => {
          const roadColor = road.color || '#1890ff';
          popupContent += `<span style="color: ${roadColor}; font-weight: bold; margin-right: 4px;">${road.code}</span>`;
          if (index < lineLayer.optRoadList.length - 1) {
            popupContent += ', ';
          }
        });
        popupContent += `</p>`;
      }

      popupContent += `</div>`;

      // 创建并显示弹窗
      const Ai = (window as any).Ai;
      Ai.Popup({ offset: [0, 0] })
        .setLatLng(center)
        .setContent(popupContent)
        .openOn(mapRef.value);
    }
  } catch (error) {
    console.error('高亮管道段出错:', error);
  }
};

// 更新地图显示
const updateMapDisplay = () => {
  try {
    console.log('执行 updateMapDisplay 函数');

    // 检查地图 API 是否已加载
    const Ai = (window as any).Ai;
    if (!Ai) {
      console.error('地图 API (Ai) 未加载，请确保已引入地图相关的 JS 文件');
      return;
    }

    // 检查地图对象是否已初始化
    if (!mapRef.value) {
      console.error('地图对象未初始化，尝试重新初始化地图');
      // 尝试重新初始化地图
      initializeMap().then(result => {
        if (result) {
          console.log('地图重新初始化成功，再次尝试更新地图显示');
          // 延迟一下再尝试更新地图显示
          setTimeout(updateMapDisplay, 500);
        } else {
          console.error('地图重新初始化失败');
        }
      });
      return;
    }

    // 检查地图容器是否存在
    const mapContainerElement = document.getElementById('mapContainer');
    if (!mapContainerElement) {
      console.error('地图容器不存在，无法更新地图显示');
      return;
    }
    console.log('地图容器尺寸:', mapContainerElement.offsetWidth, 'x', mapContainerElement.offsetHeight);

    // 检查是否有数据可供显示
    if (mapPipeSegments.value.length === 0 && mapDevices.value.length === 0) {
      console.warn('没有管道段和设备数据，无法更新地图显示');
      return;
    }
    console.log('管道段数量:', mapPipeSegments.value.length);
    console.log('设备数量:', mapDevices.value.length);

    // 根据选中的光路筛选管道段
    const filteredSegments = mapPipeSegments.value.filter(segment => {
      // 如果没有选中任何光路，不显示任何管道段，只显示设备
      if (checkedOptRoads.value.length === 0) {
        return false; // 不显示任何管道段
      }

      // 如果管道段有光路信息，检查是否包含选中的光路
      if (segment.opt_road_list && segment.opt_road_list.length > 0) {
        return segment.opt_road_list.some((road: any) =>
          checkedOptRoads.value.includes(road.code)
        );
      }

      // 默认不显示
      return false;
    });

    // 标记选中的支撑段，以便绘制时特殊处理
    filteredSegments.forEach(segment => {
      const pipeCode = segment.code || segment.zc_section_code;

      // 只有当选中了支撑段，并且当前支撑段在选中列表中时，才标记为高亮
      if (selectedColorFilters.value.length > 0 && pipeCode && selectedColorFilters.value.includes(pipeCode)) {
        segment.isHighlighted = true; // 标记为高亮显示
      } else {
        segment.isHighlighted = false; // 默认不高亮
      }
    });

    // 保存当前高亮的线条信息，以便重新绘制后仍然保持高亮
    const currentHighlightedPipeCode = highlightedLine.value ?
      Array.from(lineLayerMap.value.entries())
        .find(([_, line]) => line === highlightedLine.value)?.[0] : null;

    // 清除现有图层
    if (overLayerRef.value && overLayerRef.value.clearLayers) {
      // 清除图层
      overLayerRef.value.clearLayers();
      // 清除线条对象存储
      lineLayerMap.value.clear();
      // 重置高亮状态
      highlightedLine.value = null;
      originalLineStyle.value = null;

      // 清空不合规坐标列表
      invalidCoordinates.value = [];

      // 保存高亮的管道段编码，以便重新绘制后再次高亮
      if (currentHighlightedPipeCode) {
        // 在重新绘制完成后，将在下面的代码中恢复高亮
        setTimeout(() => {
          // 尝试找到新绘制的线条
          const newLine = lineLayerMap.value.get(currentHighlightedPipeCode);
          if (newLine) {
            // 保存原始样式
            originalLineStyle.value = {
              color: newLine.options.color,
              weight: newLine.options.weight,
              opacity: newLine.options.opacity
            };

            // 设置高亮样式
            newLine.setStyle({
              color: '#722ed1', // 高亮颜色（紫色）
              weight: 6,     // 加粗线条
              opacity: 1     // 完全不透明
            });

            // 记录当前高亮的线条
            highlightedLine.value = newLine;
          }
        }, 100);
      }
    } else {
      console.error('图层对象不存在或没有 clearLayers 方法');
      return;
    }

    // 绘制管道段线条
    for (const segment of filteredSegments) {
      try {
        // 检查坐标是否有效
        if (!segment.a_pos_x || !segment.a_pos_y || !segment.z_pos_x || !segment.z_pos_y) {
          console.warn('管道段坐标不完整，跳过:', segment);

          // 获取光缆段和光路信息
          const cableSegment = segment.cs_code || segment.cable_sect_code || (segment.type === 'cableSegment' ? segment.code : '');

          // 获取光路信息
          let opticalPath = '';
          if (segment.opt_road_list && segment.opt_road_list.length > 0) {
            opticalPath = segment.opt_road_list.map((road: any) => road.code).join(', ');
          } else if (segment.gl_code) {
            opticalPath = segment.gl_code;
          }

          // 获取坐标信息，即使不完整也显示
          let coordinatesInfo = '缺少坐标数据';
          if (segment.a_pos_x && segment.a_pos_y) {
            coordinatesInfo = `A端: (${segment.a_pos_x}, ${segment.a_pos_y})`;
            if (segment.z_pos_x || segment.z_pos_y) {
              coordinatesInfo += `, Z端: (${segment.z_pos_x || '?'}, ${segment.z_pos_y || '?'})`;
            }
          } else if (segment.z_pos_x && segment.z_pos_y) {
            coordinatesInfo = `Z端: (${segment.z_pos_x}, ${segment.z_pos_y})`;
            if (segment.a_pos_x || segment.a_pos_y) {
              coordinatesInfo = `A端: (${segment.a_pos_x || '?'}, ${segment.a_pos_y || '?'}), ` + coordinatesInfo;
            }
          } else if (segment.a_pos_x || segment.a_pos_y || segment.z_pos_x || segment.z_pos_y) {
            coordinatesInfo = `部分坐标: A(${segment.a_pos_x || '?'}, ${segment.a_pos_y || '?'}), Z(${segment.z_pos_x || '?'}, ${segment.z_pos_y || '?'})`;
          }

          // 添加到不合规坐标列表
          invalidCoordinates.value.push({
            id: segment.id || `segment_${Math.random().toString(36).substring(2, 10)}`,
            type: segment.type === 'cableSegment' ? '光缆段' : '支撑段',
            code: segment.code || segment.zc_section_code || '未知编码',
            name: segment.name || segment.zc_section_name || '未知名称',
            reason: '坐标不完整',
            coordinates: coordinatesInfo,
            cableSegment: cableSegment,
            opticalPath: opticalPath,
            rawData: segment
          });

          continue;
        }

        // 创建线条
        const a_pos_x = parseFloat(segment.a_pos_x);
        const a_pos_y = parseFloat(segment.a_pos_y);
        const z_pos_x = parseFloat(segment.z_pos_x);
        const z_pos_y = parseFloat(segment.z_pos_y);

        // 获取光缆段和光路信息
        const cableSegment = segment.cs_code || segment.cable_sect_code || (segment.type === 'cableSegment' ? segment.code : '');

        // 获取光路信息
        let opticalPath = '';
        if (segment.opt_road_list && segment.opt_road_list.length > 0) {
          opticalPath = segment.opt_road_list.map((road: any) => road.code).join(', ');
        } else if (segment.gl_code) {
          opticalPath = segment.gl_code;
        }

        // 检查A端坐标是否合规
        const aResult = checkCoordinateValidity(a_pos_x, a_pos_y);
        if (!aResult.valid) {
          console.warn(`管道段A端${aResult.reason}，跳过:`, segment);

          // 添加到不合规坐标列表
          invalidCoordinates.value.push({
            id: segment.id || `segment_${Math.random().toString(36).substring(2, 10)}`,
            type: segment.type === 'cableSegment' ? '光缆段' : '支撑段',
            code: segment.code || segment.zc_section_code || '未知编码',
            name: segment.name || segment.zc_section_name || '未知名称',
            reason: `A端${aResult.reason}`,
            coordinates: `A端: (${a_pos_x}, ${a_pos_y}), Z端: (${z_pos_x}, ${z_pos_y})`,
            cableSegment: cableSegment,
            opticalPath: opticalPath,
            rawData: segment
          });

          continue;
        }

        // 检查Z端坐标是否合规
        const zResult = checkCoordinateValidity(z_pos_x, z_pos_y);
        if (!zResult.valid) {
          console.warn(`管道段Z端${zResult.reason}，跳过:`, segment);

          // 添加到不合规坐标列表
          invalidCoordinates.value.push({
            id: segment.id || `segment_${Math.random().toString(36).substring(2, 10)}`,
            type: segment.type === 'cableSegment' ? '光缆段' : '支撑段',
            code: segment.code || segment.zc_section_code || '未知编码',
            name: segment.name || segment.zc_section_name || '未知名称',
            reason: `Z端${zResult.reason}`,
            coordinates: `A端: (${a_pos_x}, ${a_pos_y}), Z端: (${z_pos_x}, ${z_pos_y})`,
            cableSegment: cableSegment,
            opticalPath: opticalPath,
            rawData: segment
          });

          continue;
        }

        // 创建线条
        const lineWktStr = `LINESTRING (${a_pos_x} ${a_pos_y}, ${z_pos_x} ${z_pos_y})`;

        // 确定线条颜色
        let lineColor = '#1890ff'; // 默认颜色

        // 如果有光路信息，使用光路颜色
        if (segment.opt_road_list && segment.opt_road_list.length > 0) {
          // 查找选中的光路
          const selectedRoad = segment.opt_road_list.find((road: any) =>
            checkedOptRoads.value.includes(road.code)
          );

          if (selectedRoad) {
            // 查找光路选项中的颜色
            const optionItem = optRoadOptions.value.find(option => option.value === selectedRoad.code);
            if (optionItem) {
              // 使用光路选项中的颜色，确保一致性
              lineColor = optionItem.color;
              // 同时更新光路对象的颜色，确保弹窗中显示的颜色也一致
              selectedRoad.color = optionItem.color;
            } else if (selectedRoad.color) {
              // 如果在选项中找不到，但光路对象有颜色，则使用光路对象的颜色
              lineColor = selectedRoad.color;
            }
          } else if (segment.color) {
            // 如果没有选中的光路，但管道段有颜色，则使用管道段的颜色
            lineColor = segment.color;
          }
        } else if (segment.color) {
          // 如果没有光路信息，但管道段有颜色，则使用管道段的颜色
          lineColor = segment.color;
        }

        // 检查是否需要高亮显示（光缆段高亮）
        if (segment.highlightColor) {
          // 如果设置了高亮颜色，优先使用高亮颜色
          lineColor = segment.highlightColor;
        }

        // 根据是否高亮设置不同的样式
        const lineStyle = segment.isHighlighted || segment.highlightColor ?
          {
            color: segment.highlightColor || OVERLAP_COLOR, // 使用设置的高亮颜色或默认紫色
            weight: 6,     // 加粗线条
            opacity: 1     // 完全不透明
          } : {
            color: lineColor, // 使用与光路选项一致的颜色
            weight: 4,
            opacity: 0.6 // 半透明效果，使所有线条默认为半透明
          };

        // 创建线条图层
        const lineLayer = new Ai.Polyline(lineWktStr, lineStyle);

        // 添加到图层
        if (lineLayer) {
          // 设置管道段编码属性
          lineLayer.pipeCode = segment.code || segment.zc_section_code;
          lineLayer.error = segment.error;

          // 设置光路信息
          if (segment.opt_road_list && segment.opt_road_list.length > 0) {
            lineLayer.optRoadList = segment.opt_road_list;
          }

          // 添加到图层
          lineLayer.addTo(overLayerRef.value);

          // 存储线条对象，使用管道段编码或ID作为键
          const lineKey = segment.code || segment.zc_section_code || segment.id || `line_${Math.random()}`;
          lineLayerMap.value.set(lineKey, lineLayer);

          // 添加点击事件
          lineLayer.on('click', (e: any) => {
            // 高亮显示该管道段
            highlightPipeSegment(lineLayer);

            // 阻止事件冒泡
            try {
              // 使用Ai地图库的DomEvent，如果可用
              const Ai = (window as any).Ai;
              if (Ai && Ai.DomEvent) {
                Ai.DomEvent.stopPropagation(e);
              }
            } catch (err) {
              console.error('阻止事件冒泡出错:', err);
            }
          });

          // 创建弹窗内容
          let popupContent = `<div style="padding: 8px;">`;

          // 添加管道段信息
          if (segment.code || segment.zc_section_code) {
            popupContent += `<p style="margin: 0; font-weight: bold;">管道段：${segment.code || segment.zc_section_code}</p>`;
          }

          // 添加错误类型
          if (segment.error) {
            popupContent += `<p style="margin: 4px 0 0;">错误类型：${segment.error}</p>`;
          }

          // 添加光路信息
          if (segment.opt_road_list && segment.opt_road_list.length > 0) {
            popupContent += `<p style="margin: 4px 0 0;">光路：`;
            segment.opt_road_list.forEach((road: any, index: number) => {
              const roadColor = road.color || '#1890ff';
              popupContent += `<span style="color: ${roadColor}; font-weight: bold; margin-right: 4px;">${road.code}</span>`;
              if (index < segment.opt_road_list.length - 1) {
                popupContent += ', ';
              }
            });
            popupContent += `</p>`;
          }

          popupContent += `</div>`;

          // 添加弹窗
          lineLayer.bindPopup(popupContent);
        }
      } catch (err) {
        console.error('绘制管道段出错:', err, segment);
      }
    }

    // 清空设备清单
    deviceList.value = [];

    // 保存已绘制的设备ID，用于去重
    const drawnDeviceIds = new Set();

    // 绘制设备
    for (const device of mapDevices.value) {
      try {
        // 检查设备ID是否已经绘制过
        if (drawnDeviceIds.has(device.id)) {
          console.log('设备已绘制，跳过:', device.id, device.name);

          // 虽然不重复绘制，但仍然添加到设备清单中
          deviceList.value.push({
            ...device,
            device_index: device.id === devicePair.value.deviceA.device_id ? 0 : 1 // 设置设备索引，用于区分A/B设备
          });

          continue;
        }

        // 检查坐标是否有效
        if (!device.pos_x || !device.pos_y) {
          console.warn('设备坐标不完整，跳过:', device);

          // 获取设备相关的光路信息
          let relatedOpticalPaths = '';
          if (device.opt_road_list && device.opt_road_list.length > 0) {
            relatedOpticalPaths = device.opt_road_list.map((road: any) => road.code).join(', ');
          }

          // 获取坐标信息，即使不完整也显示
          let coordinatesInfo = '缺少坐标数据';
          if (device.pos_x || device.pos_y) {
            coordinatesInfo = `部分坐标: (${device.pos_x || '?'}, ${device.pos_y || '?'})`;
          }

          // 添加到不合规坐标列表
          invalidCoordinates.value.push({
            id: device.id || `device_${Math.random().toString(36).substring(2, 10)}`,
            type: '设备',
            code: device.device_code || '未知编码',
            name: device.name || '未知设备',
            reason: '坐标不完整',
            coordinates: coordinatesInfo,
            opticalPath: relatedOpticalPaths,
            rawData: device
          });

          continue;
        }

        const pos_x = parseFloat(device.pos_x);
        const pos_y = parseFloat(device.pos_y);

        // 获取设备相关的光路信息
        let relatedOpticalPaths = '';
        if (device.opt_road_list && device.opt_road_list.length > 0) {
          relatedOpticalPaths = device.opt_road_list.map((road: any) => road.code).join(', ');
        }

        // 检查坐标是否合规
        const result = checkCoordinateValidity(pos_x, pos_y);
        if (!result.valid) {
          console.warn(`设备${result.reason}，跳过:`, device);

          // 添加到不合规坐标列表
          invalidCoordinates.value.push({
            id: device.id || `device_${Math.random().toString(36).substring(2, 10)}`,
            type: '设备',
            code: device.device_code || '未知编码',
            name: device.name || '未知设备',
            reason: result.reason,
            coordinates: `(${pos_x}, ${pos_y})`,
            opticalPath: relatedOpticalPaths,
            rawData: device
          });

          continue;
        }

        // 获取设备类型的简称
        const deviceText = getDeviceShortName(device);

        // 创建自定义圆形图标
        const deviceColor = device.device_index === 0 ? '#1890ff' : '#52c41a'; // A设备蓝色，B设备绿色
        const divIconHTML = `
          <div style="
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: ${deviceColor};
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            font-size: 14px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
          ">${deviceText}</div>
        `;

        // 创建自定义图标
        const icon = new Ai.DivIcon({
          html: divIconHTML,
          iconSize: [30, 30],
          iconAnchor: [15, 15],
          className: 'custom-device-icon'
        });

        // 创建点标记
        const pointWktStr = `POINT (${pos_x} ${pos_y})`;
        const markerLayer = new Ai.Point(pointWktStr, { icon });

        // 添加到图层
        overLayerRef.value.addLayer(markerLayer);

        // 添加弹窗
        const popupContent = `<div>
          <p><strong>设备名称：</strong>${device.name || '-'}</p>
          <p><strong>设备编码：</strong>${device.device_code || '-'}</p>
          <p><strong>设备类型：</strong>${device.spec_name || '-'}</p>
        </div>`;

        markerLayer.bindPopup(popupContent);

        // 存储设备标记
        deviceMarkerMap.value.set(device.id, markerLayer);

        // 标记设备已绘制
        drawnDeviceIds.add(device.id);

        // 添加到设备清单
        deviceList.value.push({
          ...device,
          device_index: device.id === devicePair.value.deviceA.device_id ? 0 : 1 // 设置设备索引，用于区分A/B设备
        });
      } catch (err) {
        console.error('绘制设备出错:', err, device);
      }
    }

    // 对设备清单进行排序，A设备在前，B设备在后
    deviceList.value.sort((a, b) => {
      // 首先按设备索引排序
      if (a.device_index !== b.device_index) {
        return a.device_index - b.device_index;
      }

      // 如果设备索引相同，按设备名称排序
      return (a.name || '').localeCompare(b.name || '');
    });

    console.log('设备清单:', deviceList.value);

    // 调整地图视图范围
    if (filteredSegments.length > 0 || mapDevices.value.length > 0) {
      try {
        const bounds = overLayerRef.value.getBounds();
        if (bounds) {
          mapRef.value.flyToBounds(bounds);
        } else {
          // 如果无法获取边界，则使用默认定位
          gisUtils.pantoCity(MapContainer.value, defaultCityCode.toLowerCase());
        }
      } catch (boundsError) {
        // 如果出错，则使用默认定位
        gisUtils.pantoCity(MapContainer.value, defaultCityCode.toLowerCase());
      }
    }

    // 关闭加载状态
    mapDataLoading.value = false;

    // 如果有不合规坐标，自动显示表格
    if (invalidCoordinates.value.length > 0) {
      console.log(`发现 ${invalidCoordinates.value.length} 条不合规坐标`);
      showInvalidCoordinates.value = true;
    }
  } catch (error) {
    console.error('更新地图显示出错:', error);

    // 出错时也关闭加载状态
    mapDataLoading.value = false;
  }
};

// 获取地图数据
const fetchMapData = async () => {
  console.log('=== 开始获取地图数据 ===');
  // 不设置整体地图加载状态，只显示提示信息
  mapDataLoading.value = true;
  mapLoadingText.value = '正在加载地图数据...';

  try {
    // 检查是否有路由错误数据
    console.log('路由错误数据长度:', routeErrors.value.length);
    if (routeErrors.value.length === 0) {
      console.warn('没有路由错误数据，无法绘制地图');
      mapLoadingText.value = '没有可用的光路数据来绘制地图';
      console.log('=== 地图数据获取失败：没有路由错误数据 ===');
      return false;
    }

    // 从路由错误数据中提取不重复的光路编码
    const glCodes = routeErrors.value
      .filter(error => error.gl_code) // 只考虑有光路编码的记录
      .map(error => error.gl_code);

    // 去重
    const uniqueGlCodes = [...new Set(glCodes)];
    console.log('提取的不重复光路编码:', uniqueGlCodes);

    if (uniqueGlCodes.length === 0) {
      console.warn('没有光路编码，无法获取光路路由数据');
      message.warning('没有光路编码，无法获取光路路由数据');
      mapLoadingText.value = '没有可用的光路编码来绘制地图';
      return false;
    }

    // 从路由错误数据中收集光路对应的设备对信息
    const optRoadDevicePairs = new Map(); // 存储光路对应的设备对信息
    routeErrors.value.forEach(error => {
      if (error.gl_code && error.device_name) {
        // 使用设备名称作为设备对的标识
        const devicePair = error.device_name;
        optRoadDevicePairs.set(error.gl_code, devicePair);
      }
    });

    // 先创建所有光路选项，但标记为未加载（灰色）
    optRoadOptions.value = uniqueGlCodes.map(code => ({
      value: code,
      color: '#cccccc', // 默认为灰色
      devicePair: optRoadDevicePairs.get(code) || '未知设备',
      loaded: false
    }));

    // 按设备对分组光路
    const groupMap = new Map<string, { value: string; color: string; loaded: boolean }[]>();

    optRoadOptions.value.forEach(option => {
      const devicePair = option.devicePair || '其他';
      if (!groupMap.has(devicePair)) {
        groupMap.set(devicePair, []);
      }
      groupMap.get(devicePair)?.push({
        value: option.value,
        color: option.color,
        loaded: false
      });
    });

    // 转换为分组数组
    optRoadGroups.value = Array.from(groupMap.entries()).map(([name, roads]) => ({
      name,
      roads
    }));

    console.log('初始光路分组:', optRoadGroups.value);

    // 清空已加载的光路集合
    loadedOptRoads.value.clear();

    // 从路由错误数据中提取光缆段编码，用于左侧列表
    const cableSegmentCodes = routeErrors.value
      .filter(error => error.cs_code) // 只考虑有光缆段编码的记录
      .map(error => ({
        code: error.cs_code,
        type: 'cableSegment',
        name: error.cs_name || error.cs_code,
        error: error.error
      }));

    // 从路由错误数据中提取支撑段编码，用于左侧列表
    const pipeSegmentCodes = routeErrors.value
      .filter(error => error.zc_section_code) // 只考虑有支撑段编码的记录
      .map(error => ({
        code: error.zc_section_code,
        type: 'pipeSegment',
        name: error.zc_section_name || error.zc_section_code,
        error: error.error
      }));

    // 合并光缆段和支撑段数据
    const allSegments = [...cableSegmentCodes, ...pipeSegmentCodes];
    const uniqueSegments: Array<{
      code: string;
      type: string;
      name: string;
      error: string;
      color?: string;
    }> = [];
    const seenCodes = new Set();

    for (const segment of allSegments) {
      if (!seenCodes.has(segment.code)) {
        seenCodes.add(segment.code);
        uniqueSegments.push(segment);
      }
    }

    // 为每个段分配颜色
    const colors = ['#1890ff', '#f5222d', '#52c41a', '#faad14', '#722ed1', '#eb2f96', '#fa541c', '#13c2c2'];
    uniqueSegments.forEach((segment, index) => {
      segment.color = colors[index % colors.length];
    });

    // 更新支撑段筛选列表
    pipeColorFilters.value = uniqueSegments.map(segment => ({
      code: segment.code,
      color: segment.color || colors[0],
      type: segment.type,
      name: segment.name,
      error: segment.error
    }));

    console.log('支撑段筛选列表:', pipeColorFilters.value);

    // 从路由错误数据中提取设备和支撑段数据
    mapDevices.value = [];
    mapPipeSegments.value = [];
    console.log('已清空地图设备和支撑段数据');

    // 清空设备和支撑段数据，准备使用光路数据填充
    console.log('清空设备和支撑段数据，准备使用光路数据填充');

    // 使用提取的光路编码查询光路数据
    console.log('开始查询光路数据');

    // 创建一个数组来存储所有光路的管道段数据
    const allPipeSegments: any[] = [];
    // 创建一个数组来存储所有光路的设备数据
    const allDevices: any[] = [];
    // 创建一个集合来记录已经添加的设备 ID，避免重复
    const deviceIds = new Set();

    // 使用所有提取的光路编码
    console.log(`准备查询 ${uniqueGlCodes.length} 条光路数据`);

    // 创建查询函数
    const fetchOptRoadData = async (currentGlCode: string) => {
      try {
        console.log('获取光路数据:', currentGlCode);

        // 创建一个临时的 infoService
        const optRoadInfoService = useInfo({
          rootPath: '/graph-rest-api'
        });

        // 设置 info 值
        optRoadInfoService.info.value = {
          code: currentGlCode, // 光路编码
          areaCode: defaultCityCode.toLowerCase() // 城市代码，使用小写
        };

        // 调用 API
        console.log(`调用 API: /api/customer-topology/opt_road_gis (光路: ${currentGlCode})`);
        const result = await optRoadInfoService.doCreateNew('/api/customer-topology/opt_road_gis');

        if (!result) {
          console.warn(`光路 ${currentGlCode} 没有返回数据`);
          return null;
        }

        console.log(`光路 ${currentGlCode} 返回数据成功`);
        return { glCode: currentGlCode, data: result };
      } catch (error) {
        console.error(`获取光路 ${currentGlCode} 数据出错:`, error);
        return null;
      }
    };

    try {
      // 并行执行查询，同时获取所有光路数据
      console.log(`开始并行获取 ${uniqueGlCodes.length} 条光路数据`);

      // 更新加载状态文本
      mapLoadingText.value = `正在加载光路数据 (0/${uniqueGlCodes.length})...`;

      // 为每个光路分配颜色
      const colorMap = new Map();
      const colors = ['#1890ff', '#f5222d', '#52c41a', '#faad14', '#722ed1', '#eb2f96', '#fa541c', '#13c2c2'];

      // 为每个光路预先分配颜色
      uniqueGlCodes.forEach((glCode, index) => {
        colorMap.set(glCode, colors[index % colors.length]);
      });

      // 创建一个计数器，用于跟踪已处理的光路数量
      let processedCount = 0;

      // 创建一个互斥锁，用于确保同时只有一个光路在更新地图
      let updating = false;

      // 处理单个光路数据的函数
      const processOptRoadData = async (glCode: string) => {
        try {
          console.log(`开始获取光路 ${glCode} 数据`);

          // 获取光路数据
          const result = await fetchOptRoadData(glCode);
          if (!result) {
            console.warn(`光路 ${glCode} 数据获取失败`);
            return null;
          }

          const { glCode: currentGlCode, data } = result;
          console.log(`光路 ${currentGlCode} 数据获取成功，准备处理`);

          // 标记该光路已加载，并更新其颜色
          loadedOptRoads.value.add(currentGlCode);

          // 更新光路选项中的颜色
          const optionIndex = optRoadOptions.value.findIndex(option => option.value === currentGlCode);
          if (optionIndex >= 0) {
            optRoadOptions.value[optionIndex].color = colorMap.get(currentGlCode) || '#1890ff';
            optRoadOptions.value[optionIndex].loaded = true;
          }

          // 更新光路分组中的颜色
          optRoadGroups.value.forEach(group => {
            const roadIndex = group.roads.findIndex(road => road.value === currentGlCode);
            if (roadIndex >= 0) {
              group.roads[roadIndex].color = colorMap.get(currentGlCode) || '#1890ff';
              group.roads[roadIndex].loaded = true;
            }
          });

          // 处理设备数据
          const newDevices: any[] = [];
          if (data.road_both_point_devices_xy) {
            const tempObj = data.road_both_point_devices_xy;

            // 处理 A 端设备
            if (tempObj.a_id && !deviceIds.has(tempObj.a_id)) {
              deviceIds.add(tempObj.a_id);
              newDevices.push({
                "id": tempObj.a_id,
                "spec_id": tempObj.a_spec_id,
                "pos_y": tempObj.a_pos_y,
                "pos_x": tempObj.a_pos_x,
                'spec_name': tempObj.a_spec_name,
                'name': tempObj.a_name,
              });
            }

            // 处理 Z 端设备
            if (tempObj.z_id && !deviceIds.has(tempObj.z_id)) {
              deviceIds.add(tempObj.z_id);
              newDevices.push({
                "id": tempObj.z_id,
                "spec_id": tempObj.z_spec_id,
                "pos_y": tempObj.z_pos_y,
                "pos_x": tempObj.z_pos_x,
                'spec_name': tempObj.z_spec_name,
                'name': tempObj.z_name,
              });
            }
          }

          // 处理管道段数据
          const newPipeSegments: any[] = [];
          if (data.pipe_segments && data.pipe_segments.length > 0) {
            // 创建一个映射，用于存储光缆段和管道段的关系
            const cableSegmentToPipeSegment = new Map<string, string[]>();

            // 在每个管道段上添加光路信息
            const pipeSegmentsWithOptRoad = data.pipe_segments.map((segment: any) => {
              // 确保 opt_road_list 存在
              if (!segment.opt_road_list) {
                segment.opt_road_list = [];
              }

              // 添加当前光路信息
              const hasCurrentOptRoad = segment.opt_road_list.some((road: any) => road.code === currentGlCode);
              if (!hasCurrentOptRoad) {
                segment.opt_road_list.push({
                  code: currentGlCode,
                  name: `光路 ${currentGlCode}`,
                  color: colorMap.get(currentGlCode) // 设置光路颜色
                });
              }

              // 处理光缆段和管道段的关系
              if (segment.cable_sect_list && Array.isArray(segment.cable_sect_list)) {
                segment.cable_sect_list.forEach((cableSect: any) => {
                  if (cableSect.code) {
                    // 如果映射中还没有这个光缆段，创建一个新数组
                    if (!cableSegmentToPipeSegment.has(cableSect.code)) {
                      cableSegmentToPipeSegment.set(cableSect.code, []);
                    }

                    // 将当前管道段添加到光缆段对应的数组中
                    const pipeSegmentCode = segment.code || segment.zc_section_code;
                    if (pipeSegmentCode && !cableSegmentToPipeSegment.get(cableSect.code)?.includes(pipeSegmentCode)) {
                      cableSegmentToPipeSegment.get(cableSect.code)?.push(pipeSegmentCode);
                    }
                  }
                });
              }

              return segment;
            });

            // 将光缆段和管道段的关系存储到全局变量中
            cableSegmentToPipeSegment.forEach((pipeSegments, cableSegment) => {
              if (!cableSegmentToPipeSegmentMap.has(cableSegment)) {
                cableSegmentToPipeSegmentMap.set(cableSegment, new Set(pipeSegments));
              } else {
                // 合并已有的管道段和新的管道段
                const existingPipeSegments = cableSegmentToPipeSegmentMap.get(cableSegment)!;
                pipeSegments.forEach(segment => existingPipeSegments.add(segment));
              }
            });

            newPipeSegments.push(...pipeSegmentsWithOptRoad);
          }

          // 增加已处理的光路计数
          processedCount++;

          // 更新加载状态文本
          mapLoadingText.value = `已加载 ${processedCount}/${uniqueGlCodes.length} 条光路数据...`;

          return {
            glCode: currentGlCode,
            devices: newDevices,
            pipeSegments: newPipeSegments
          };
        } catch (error) {
          console.error(`处理光路 ${glCode} 数据出错:`, error);
          return null;
        }
      };

      // 更新地图数据的函数
      const updateMapData = (results: any[]) => {
        // 如果正在更新，等待下一次更新
        if (updating) {
          return;
        }

        updating = true;

        try {
          // 合并所有设备数据，并进行去重
          results.forEach(result => {
            if (result && result.devices) {
              // 对每个设备进行检查，避免重复添加
              result.devices.forEach(newDevice => {
                // 检查设备是否已存在
                const existingDeviceIndex = allDevices.findIndex(device =>
                  device.id === newDevice.id ||
                  (device.pos_x === newDevice.pos_x &&
                   device.pos_y === newDevice.pos_y &&
                   device.name === newDevice.name)
                );

                // 如果设备不存在，则添加
                if (existingDeviceIndex === -1) {
                  allDevices.push(newDevice);
                }
              });
            }
          });

          // 合并所有管道段数据
          results.forEach(result => {
            if (result && result.pipeSegments) {
              for (const newSegment of result.pipeSegments) {
                // 检查是否已经存在相同的管道段
                const existingSegmentIndex = allPipeSegments.findIndex(segment =>
                  segment.id === newSegment.id ||
                  (segment.code === newSegment.code && segment.code) ||
                  (segment.a_pos_x === newSegment.a_pos_x &&
                   segment.a_pos_y === newSegment.a_pos_y &&
                   segment.z_pos_x === newSegment.z_pos_x &&
                   segment.z_pos_y === newSegment.z_pos_y)
                );

                if (existingSegmentIndex >= 0) {
                  // 如果已经存在相同的管道段，合并 opt_road_list
                  const existingSegment = allPipeSegments[existingSegmentIndex];

                  // 确保 opt_road_list 存在
                  if (!existingSegment.opt_road_list) {
                    existingSegment.opt_road_list = [];
                  }

                  // 合并光路列表，避免重复
                  if (newSegment.opt_road_list) {
                    for (const newRoad of newSegment.opt_road_list) {
                      const hasRoad = existingSegment.opt_road_list.some((road: any) => road.code === newRoad.code);
                      if (!hasRoad) {
                        existingSegment.opt_road_list.push(newRoad);
                      }
                    }
                  }
                } else {
                  // 如果不存在相同的管道段，添加新管道段
                  allPipeSegments.push(newSegment);
                }
              }
            }
          });

          // 更新数据
          mapPipeSegments.value = [...allPipeSegments]; // 使用新数组触发响应式更新
          mapDevices.value = [...allDevices]; // 使用新数组触发响应式更新

          // 设置管道段颜色（使用第一个光路的颜色）
          mapPipeSegments.value.forEach(segment => {
            if (segment.opt_road_list && segment.opt_road_list.length > 0) {
              segment.color = segment.opt_road_list[0].color;
            } else {
              // 如果没有光路，使用默认颜色
              segment.color = '#999999';
            }
          });

          // 更新地图显示
          console.log(`更新地图显示，当前已处理 ${processedCount}/${uniqueGlCodes.length} 条光路数据`);
          console.log('当前管道段数量:', allPipeSegments.length);
          console.log('当前设备数量:', allDevices.length);

          mapLoadingText.value = `正在绘制光路 (${processedCount}/${uniqueGlCodes.length})...`;
          updateMapDisplay();
        } finally {
          updating = false;
        }
      };

      // 并行获取所有光路数据
      const promises = uniqueGlCodes.map(glCode => processOptRoadData(glCode));

      // 创建一个定时器，定期更新地图数据
      const updateInterval = setInterval(async () => {
        // 获取已完成的结果
        const results = await Promise.allSettled(promises);
        const completedResults = results
          .filter(result => result.status === 'fulfilled' && result.value)
          .map(result => (result as PromiseFulfilledResult<any>).value);

        // 更新地图数据
        updateMapData(completedResults);

        // 如果所有光路都已处理完毕，清除定时器
        if (processedCount >= uniqueGlCodes.length) {
          clearInterval(updateInterval);
        }
      }, 1000); // 每秒更新一次

      // 等待所有光路数据获取完成
      const results = await Promise.all(promises);

      // 清除定时器
      clearInterval(updateInterval);

      // 最后更新一次地图数据，确保所有数据都已显示
      updateMapData(results.filter(Boolean));

      console.log('所有光路数据获取完成');
      console.log('总管道段数量:', allPipeSegments.length);
      console.log('总设备数量:', allDevices.length);

      // 设置数据（最终确认）
      mapPipeSegments.value = allPipeSegments;
      mapDevices.value = allDevices;

      // 收集所有已加载的光路编码
      const allLoadedOptRoads = new Set<string>();

      // 从管道段中收集已加载的光路信息
      mapPipeSegments.value.forEach(segment => {
        if (segment.opt_road_list) {
          segment.opt_road_list.forEach((road: any) => {
            allLoadedOptRoads.add(road.code);
          });
        }
      });

      console.log('已加载的光路:', Array.from(allLoadedOptRoads));
      console.log('光路分组:', optRoadGroups.value);

      // 默认选中所有光路
      checkedOptRoads.value = Array.from(optRoadOptions.value.map(option => option.value));

    } catch (error) {
      console.error('获取光路数据出错:', error);
      message.error('获取光路数据出错: ' + (error.message || '未知错误'));
    }

    // 所有光路数据已处理完毕
    console.log('所有光路数据处理完毕，地图已更新');

    // 检查地图对象是否已初始化
    if (!mapRef.value) {
      console.error('地图对象未初始化，无法更新地图显示');
      console.log('=== 地图数据获取成功，但地图未初始化 ===');
      return true; // 返回成功，因为数据获取成功了，只是地图未初始化
    }

    // 确保地图容器存在
    const mapContainerElement = document.getElementById('mapContainer');
    if (!mapContainerElement) {
      console.error('地图容器不存在，无法更新地图显示');
      console.log('=== 地图数据获取成功，但地图容器不存在 ===');
      return true; // 返回成功，因为数据获取成功了，只是地图容器不存在
    }

    // 不再需要在这里调用updateMapDisplay，因为已经在每条光路处理后调用过了

    console.log('=== 地图数据获取成功 ===');
    return true;
  } catch (error: any) {
    console.error('获取地图数据出错:', error);
    console.error('错误详情:', error.message);
    console.error('错误堆栈:', error.stack);
    message.error(`获取地图数据出错: ${error.message || '未知错误'}`);
    console.log('=== 地图数据获取异常 ===');
    return false;
  } finally {
    mapLoading.value = false;
    console.log('地图加载状态已重置');
  }
};

// 高亮显示管道段

// 测量距离
// 获取设备对基本信息
const fetchDevicePairInfo = async () => {
  console.log('=== 开始获取设备对基本信息 ===');
  console.log('路由参数:', route.query);
  console.log('b_pair:', b_pair.value);
  console.log('deviceNameA:', deviceNameA.value);
  console.log('deviceNameB:', deviceNameB.value);
  console.log('defaultCityCode:', defaultCityCode);

  // 检查b_pair是否为空
  if (!b_pair.value) {
    console.error('错误: b_pair参数为空，无法查询设备对信息');
    message.error('设备对标识为空，无法查询详情');
    return false;
  }

  try {
    // 构建查询参数
    const params = {
      b_pair: b_pair.value
    };

    console.log('API请求参数:', JSON.stringify(params));
    console.log('API请求城市代码:', defaultCityCode);
    console.log('API请求接口:', 'SLY_PAIR_DEVICE_INFO_QUERY');
    console.log('API请求版本:', 'V20250423204627250');
    console.log('API请求根路径:', '/graph-rest-api');

    // 直接使用 SLY_PAIR_DEVICE_INFO_QUERY 接口获取设备对信息
    console.time('设备对基本信息API请求耗时');
    const result = await unifiedApi.callApi(
      'SLY_PAIR_DEVICE_INFO_QUERY',
      'V20250423204627250',
      params,
      defaultCityCode.toLowerCase()
    );
    console.timeEnd('设备对基本信息API请求耗时');

    console.log('设备对基本信息API返回完整结果:', result);

    if (result && result.data) {
      // 处理返回的数据
      const pairInfo = result.data;
      console.log('设备对基本信息数据:', pairInfo);
      console.log('设备对基本信息数据类型:', Array.isArray(pairInfo) ? 'Array' : typeof pairInfo);
      console.log('设备对基本信息数据长度:', Array.isArray(pairInfo) ? pairInfo.length : '非数组');

      // 根据返回的数据结构构建 devicePair 对象
      if (Array.isArray(pairInfo) && pairInfo.length >= 2) {
        // 根据接口定义，数组中包含两个设备的信息
        // 根据device_index字段区分设备A和设备B
        // 通常device_index=1的是设备A，device_index=2的是设备B
        const deviceA = pairInfo.find(d => d.device_index === 1) || pairInfo[0];
        const deviceB = pairInfo.find(d => d.device_index === 2) || pairInfo[1];

        console.log('设备对基本信息 - 设备A:', deviceA);
        console.log('设备对基本信息 - 设备B:', deviceB);

        devicePair.value = {
          b_pair: deviceA.b_pair || b_pair.value,
          deviceA: {
            device_id: deviceA.device_id || route.query.deviceIdA,
            device_code: deviceA.device_code || route.query.deviceCodeA,
            device_name: deviceA.device_name || deviceNameA.value,
            device_ip: deviceA.device_ip || '未知',
            device_type: deviceA.eqp_spec || '未知'
          },
          deviceB: {
            device_id: deviceB.device_id || route.query.deviceIdB,
            device_code: deviceB.device_code || route.query.deviceCodeB,
            device_name: deviceB.device_name || deviceNameB.value,
            device_ip: deviceB.device_ip || '未知',
            device_type: deviceB.eqp_spec || '未知'
          },
          region: `${deviceA.city_name || ''} ${deviceA.area_name || ''}`,
          discoveryTime: deviceA.create_date ? new Date(deviceA.create_date).toLocaleString() : '未知'
        };

        console.log('构建的设备对信息:', devicePair.value);
      } else if (Array.isArray(pairInfo) && pairInfo.length === 1) {
        // 如果只返回了一条记录，使用这条记录作为设备A，并尝试从路由参数获取设备B信息
        console.log('只有一条设备记录，使用它作为设备A:', pairInfo[0]);

        const deviceA = pairInfo[0];

        devicePair.value = {
          b_pair: deviceA.b_pair || b_pair.value,
          deviceA: {
            device_id: deviceA.device_id || route.query.deviceIdA,
            device_code: deviceA.device_code || route.query.deviceCodeA,
            device_name: deviceA.device_name || deviceNameA.value,
            device_ip: deviceA.device_ip || '未知',
            device_type: deviceA.eqp_spec || '未知'
          },
          deviceB: {
            device_id: route.query.deviceIdB,
            device_code: route.query.deviceCodeB,
            device_name: deviceNameB.value,
            device_ip: '未知',
            device_type: deviceType.value || '未知'
          },
          region: `${deviceA.city_name || ''} ${deviceA.area_name || ''}`,
          discoveryTime: deviceA.create_date ? new Date(deviceA.create_date).toLocaleString() : '未知'
        };

        console.log('构建的设备对信息:', devicePair.value);
      } else {
        console.log('API返回的数据为空或不是数组，使用路由参数构建基本信息');

        // 如果没有返回数据，使用路由参数构建基本信息
        devicePair.value = {
          b_pair: b_pair.value,
          deviceA: {
            device_id: route.query.deviceIdA,
            device_code: route.query.deviceCodeA,
            device_name: deviceNameA.value,
            device_ip: '未知',
            device_type: deviceType.value || '未知'
          },
          deviceB: {
            device_id: route.query.deviceIdB,
            device_code: route.query.deviceCodeB,
            device_name: deviceNameB.value,
            device_ip: '未知',
            device_type: deviceType.value || '未知'
          },
          region: '未知',
          discoveryTime: '未知'
        };

        console.log('使用路由参数构建的设备对信息:', devicePair.value);
      }

      console.log('=== 设备对基本信息获取成功 ===');
      return true;
    } else {
      console.warn('API返回结果为空或没有data字段');
      console.log('API返回结果:', result);
      message.warning('未找到设备对基本信息');
      console.log('=== 设备对基本信息获取失败 ===');
      return false;
    }
  } catch (error) {
    console.error('获取设备对基本信息出错:', error);
    console.error('错误详情:', error.message);
    console.error('错误堆栈:', error.stack);
    message.error(`获取设备对基本信息出错: ${error.message || '未知错误'}`);
    console.log('=== 设备对基本信息获取异常 ===');
    return false;
  }
};

// 获取设备对路由错误数据
const fetchPairRouteErrors = async () => {
  console.log('=== 开始获取设备对路由错误数据 ===');
  console.log('b_pair:', b_pair.value);
  console.log('defaultCityCode:', defaultCityCode);

  // 检查b_pair是否为空
  if (!b_pair.value) {
    console.error('错误: b_pair参数为空，无法查询设备对路由错误数据');
    message.error('设备对标识为空，无法查询路由错误数据');
    return false;
  }

  try {
    // 构建查询参数
    const params = {
      b_pair: b_pair.value
    };

    console.log('API请求参数:', JSON.stringify(params));
    console.log('API请求城市代码:', defaultCityCode);
    console.log('API请求根路径:', '/graph-rest-api');

    // 1. 调用API获取路由错误数据
    console.log('=== 开始获取路由错误数据 ===');
    console.log('API请求接口:', 'SLY_PAIR_ROUTE_ERR_QUERY');
    console.log('API请求版本:', 'V20250423204823717');
    console.time('路由错误数据API请求耗时');
    const routeResult = await unifiedApi.getPairRouteErrors(defaultCityCode, params);
    console.timeEnd('路由错误数据API请求耗时');
    console.log('路由错误数据API返回完整结果:', routeResult);

    // 2. 调用API获取板卡错误数据
    console.log('=== 开始获取板卡错误数据 ===');
    console.log('API请求接口:', 'SLY_PAIR_DEVICE_ERR_QUERY');
    console.log('API请求版本:', 'V20250423205059645');
    console.time('板卡错误数据API请求耗时');
    const deviceErrResult = await unifiedApi.getPairDeviceErrors(defaultCityCode, params);
    console.timeEnd('板卡错误数据API请求耗时');
    console.log('板卡错误数据API返回完整结果:', deviceErrResult);

    // 3. 调用API获取缺端口和缺光路错误数据
    console.log('=== 开始获取网络源错误数据 ===');
    console.log('API请求接口:', 'SLY_PAIR_NETSOURCE_QUERY');
    console.log('API请求版本:', 'V20250423205242697');
    console.time('网络源错误数据API请求耗时');
    const netsourceResult = await unifiedApi.getPairNetsourceErrors(defaultCityCode, params);
    console.timeEnd('网络源错误数据API请求耗时');
    console.log('网络源错误数据API返回完整结果:', netsourceResult);

    // 合并所有错误数据
    let allErrors: any[] = [];
    let routeZErrors: any[] = []; // 专门存储同光缆&无穿管&同管道异常数据

    // 处理路由错误数据
    if (routeResult && routeResult.data) {
      console.log('路由错误数据类型:', Array.isArray(routeResult.data) ? 'Array' : typeof routeResult.data);
      console.log('路由错误数据长度:', Array.isArray(routeResult.data) ? routeResult.data.length : '非数组');
      console.log('路由错误数据详情:', routeResult.data);

      if (Array.isArray(routeResult.data)) {
        // 将路由错误数据添加到专门的数组中
        routeZErrors = routeResult.data;
        console.log('路由错误数据长度:', routeZErrors.length);

        // 同时添加到所有错误数据中
        allErrors = allErrors.concat(routeResult.data);
        console.log('合并后的错误数据长度:', allErrors.length);

        // 更新路由错误数据状态
        routeErrors.value = routeZErrors;
        originalRouteErrors.value = JSON.parse(JSON.stringify(routeZErrors));

        // 分析相同管道的记录
        console.log('开始分析相同管道记录');
        analyzeSamePipeRecords();
        console.log('相同管道记录分析完成');

        // 更新表格列的筛选项
        routeErrorColumns.value = getRouteErrorColumns();
        console.log('表格列的筛选项已更新');
      } else {
        console.warn('路由错误数据不是数组，无法合并');
        // 即使没有数据，也要初始化空数组
        routeErrors.value = [];
        originalRouteErrors.value = [];
      }
    } else {
      console.warn('路由错误数据API返回结果为空或没有data字段');
      // 即使没有数据，也要初始化空数组
      routeErrors.value = [];
      originalRouteErrors.value = [];
    }

    // 处理板卡错误数据
    let boardCardErrors: any[] = []; // 专门存储板卡异常数据

    if (deviceErrResult && deviceErrResult.data) {
      console.log('板卡错误数据类型:', Array.isArray(deviceErrResult.data) ? 'Array' : typeof deviceErrResult.data);
      console.log('板卡错误数据长度:', Array.isArray(deviceErrResult.data) ? deviceErrResult.data.length : '非数组');
      console.log('板卡错误数据详情:', deviceErrResult.data);

      if (Array.isArray(deviceErrResult.data)) {
        // 为板卡错误数据添加 error 字段，因为 sly_pair_gl_bk_err 表中没有 error 字段
        boardCardErrors = deviceErrResult.data.map((item: any) => ({
          ...item,
          error: '板卡异常' // 添加默认的错误类型
        }));
        console.log('处理后的板卡错误数据:', boardCardErrors);
        console.log('板卡错误数据长度:', boardCardErrors.length);

        // 更新板卡错误数据状态
        boardErrors.value = boardCardErrors;
        originalBoardErrors.value = JSON.parse(JSON.stringify(boardCardErrors));

        // 同时添加到所有错误数据中
        allErrors = allErrors.concat(boardCardErrors);
        console.log('合并后的错误数据长度:', allErrors.length);

        // 不再需要切换标签页，因为所有表格都显示在同一页面
      } else {
        console.warn('板卡错误数据不是数组，无法合并');
        // 即使没有数据，也要初始化空数组
        boardErrors.value = [];
        originalBoardErrors.value = [];
      }
    } else {
      console.warn('板卡错误数据API返回结果为空或没有data字段');
      // 即使没有数据，也要初始化空数组
      boardErrors.value = [];
      originalBoardErrors.value = [];
    }

    // 处理网络源错误数据（端口&光路异常）
    let portAndOpticalErrors: any[] = []; // 专门存储端口&光路异常数据

    if (netsourceResult && netsourceResult.data) {
      console.log('网络源错误数据类型:', Array.isArray(netsourceResult.data) ? 'Array' : typeof netsourceResult.data);
      console.log('网络源错误数据长度:', Array.isArray(netsourceResult.data) ? netsourceResult.data.length : '非数组');
      console.log('网络源错误数据详情:', netsourceResult.data);

      if (Array.isArray(netsourceResult.data)) {
        // 将网络源错误数据添加到专门的数组中
        portAndOpticalErrors = netsourceResult.data;
        console.log('端口&光路异常数据长度:', portAndOpticalErrors.length);

        // 更新端口&光路异常数据状态
        portErrors.value = portAndOpticalErrors;
        originalPortErrors.value = JSON.parse(JSON.stringify(portAndOpticalErrors));

        // 同时添加到所有错误数据中
        allErrors = allErrors.concat(portAndOpticalErrors);
        console.log('合并后的错误数据长度:', allErrors.length);

        // 不再需要切换标签页，因为所有表格都显示在同一页面
      } else {
        console.warn('网络源错误数据不是数组，无法合并');
        // 即使没有数据，也要初始化空数组
        portErrors.value = [];
        originalPortErrors.value = [];
      }
    } else {
      console.warn('网络源错误数据API返回结果为空或没有data字段');
      // 即使没有数据，也要初始化空数组
      portErrors.value = [];
      originalPortErrors.value = [];
    }

    console.log('最终合并的所有错误数据:', allErrors);
    console.log('最终合并的错误数据长度:', allErrors.length);

    // 添加问题详情
    problemDetails.value = []; // 清空现有问题详情
    console.log('开始处理问题详情');

    if (allErrors.length > 0) {
      allErrors.forEach((error: any, index: number) => {
        console.log(`处理第 ${index + 1} 条错误数据:`, error);

        let source = '';
        const errorType = error.error || '未知错误';
        console.log('错误类型:', errorType);

        if (errorType && (errorType.includes('光缆') || errorType.includes('穿管') || errorType.includes('管道'))) {
          source = '路由错误表 (sly_pair_route_z)';
        } else if (errorType && errorType.includes('板卡')) {
          source = '板卡错误表 (sly_pair_gl_bk_err)';
        } else {
          source = '缺端口和缺光路表 (sly_pair_device_netsource)';
        }
        console.log('错误来源:', source);

        const problemDetail = {
          type: errorType,
          time: new Date().toLocaleString(),
          description: `该设备对存在${errorType}问题，可能影响路由连接。`,
          impact: `影响光路: ${error.gl_code || '无'}, 光缆段: ${error.cs_code || '无'}`,
          source: source
        };
        console.log('创建的问题详情:', problemDetail);

        problemDetails.value.push(problemDetail);
      });
      console.log('问题详情处理完成，共 ' + problemDetails.value.length + ' 条');
      console.log('=== 设备对路由错误数据获取成功 ===');
    } else {
      console.warn('未找到任何错误数据');
      console.log('=== 设备对路由错误数据获取完成，但没有数据 ===');
    }

    return true;
  } catch (error: any) {
    console.error('获取路由错误数据出错:', error);
    console.error('错误详情:', error.message);
    console.error('错误堆栈:', error.stack);
    message.error(`获取路由错误数据出错: ${error.message || '未知错误'}`);
    console.log('=== 设备对路由错误数据获取异常 ===');
    return false;
  }
};

// 管道编码到颜色的映射
const pipeColorMap = new Map<string, string>();
const pipeColors = ['#f50', '#2db7f5', '#87d068', '#108ee9', '#722ed1', '#eb2f96', '#faad14', '#a0d911', '#1890ff', '#fa541c'];
let colorIndex = 0;

// 检查记录是否属于相同管道组或同光缆组
const isSamePipeRecord = (record: any) => {
  // 检查同管道问题
  if (record.error === '同管道' && record.zc_section_code) {
    return samePipeRecords.value[record.zc_section_code] &&
           samePipeRecords.value[record.zc_section_code].length > 1;
  }

  // 检查同光缆问题
  if (record.error === '同光缆' && record.cable_code) {
    return sameCableRecords.value[record.cable_code] &&
           sameCableRecords.value[record.cable_code].length > 1;
  }

  return false;
};

// 获取相同管道组或同光缆组的颜色
const getSamePipeGroupColor = (record: any) => {
  // 处理同管道问题
  if (record.error === '同管道' && record.zc_section_code) {
    const pipeCode = record.zc_section_code;

    // 查找对应的筛选项
    const filter = pipeColorFilters.value.find(
      f => f.type === 'pipeSegment' && f.error === '同管道' && f.code === pipeCode
    );

    if (filter) {
      return filter.color;
    }
  }

  // 处理同光缆问题
  if (record.error === '同光缆' && record.cable_code) {
    const cableCode = record.cable_code;

    // 查找对应的筛选项
    const filter = cableColorFilters.value.find(
      f => f.type === 'cable' && f.error === '同光缆' && f.code === cableCode
    );

    if (filter) {
      return filter.color;
    }
  }

  return '';
};

// 分析相同管道和同光缆的记录
const analyzeSamePipeRecords = () => {
  // 清空当前结果
  samePipeRecords.value = {};
  sameCableRecords.value = {};
  pipeColorMap.clear();
  colorIndex = 0;
  cableColorFilters.value = [];
  pipeColorFilters.value = [];
  selectedColorFilters.value = [];

  // 用于存储光路编码
  const glCodes = new Set<string>();

  // 用于存储光缆编码及其相关记录
  const cableCodes = new Map<string, {
    name: string,
    error: string,
    records: any[]
  }>();

  // 用于存储光缆段编码
  const cableSegmentCodes = new Map<string, {
    name: string,
    error: string,
    records: any[]
  }>();

  // 用于存储支撑段编码及其相关记录
  const pipeSegmentCodes = new Map<string, {
    name: string,
    error: string,
    records: any[]
  }>();

  // 分析路由错误表中的记录
  // 使用原始数据进行分析，确保分析结果的准确性
  originalRouteErrors.value.forEach(record => {
    // 提取光路编码
    if (record.gl_code) {
      glCodes.add(record.gl_code);
    }

    // 提取光缆编码 - 同光缆问题
    if (record.cable_code && record.error === '同光缆') {
      // 为同光缆问题创建记录组
      if (!sameCableRecords.value[record.cable_code]) {
        sameCableRecords.value[record.cable_code] = [];
      }
      sameCableRecords.value[record.cable_code].push(record.gl_id + '_' + record.error);

      if (!cableCodes.has(record.cable_code)) {
        cableCodes.set(record.cable_code, {
          name: record.cable_name || record.cable_code,
          error: record.error,
          records: []
        });
      }
      cableCodes.get(record.cable_code)?.records.push(record);
    }

    // 提取光缆段编码
    if (record.cs_code) {
      if (!cableSegmentCodes.has(record.cs_code)) {
        cableSegmentCodes.set(record.cs_code, {
          name: record.cs_name || record.cs_code,
          error: record.error || '同光缆段',
          records: []
        });
      }
      cableSegmentCodes.get(record.cs_code)?.records.push(record);
    }

    // 提取支撑段编码 - 同管道问题
    if (record.error === '同管道' && record.zc_section_code) {
      // 为同管道问题创建记录组
      if (!samePipeRecords.value[record.zc_section_code]) {
        samePipeRecords.value[record.zc_section_code] = [];
      }
      samePipeRecords.value[record.zc_section_code].push(record.gl_id + '_' + record.error);

      // 存储支撑段信息
      if (!pipeSegmentCodes.has(record.zc_section_code)) {
        pipeSegmentCodes.set(record.zc_section_code, {
          name: record.zc_section_name || record.zc_section_code,
          error: record.error,
          records: []
        });
      }
      pipeSegmentCodes.get(record.zc_section_code)?.records.push(record);
    }
  });

  console.log('提取的光路编码:', Array.from(glCodes));
  console.log('提取的光缆编码:', Array.from(cableCodes.keys()));
  console.log('提取的光缆段编码:', Array.from(cableSegmentCodes.keys()));
  console.log('提取的支撑段编码:', Array.from(pipeSegmentCodes.keys()));

  // 为每种类型分配不同的颜色
  const colors = ['#1890ff', '#f5222d', '#52c41a', '#faad14', '#722ed1', '#eb2f96', '#fa541c', '#13c2c2'];
  // 重置颜色索引
  colorIndex = 0;

  // 添加同光缆问题到光缆颜色筛选列表
  Array.from(cableCodes.entries()).forEach(([code, info]) => {
    // 只有有记录的光缆才添加到筛选列表
    if (info.records.length > 0) {
      const color = colors[colorIndex % colors.length];
      colorIndex++;

      cableColorFilters.value.push({
        code: code,
        color: color,
        type: 'cable',
        name: info.name,
        error: '同光缆'
      });
    }
  });

  // 添加光缆段编码到筛选列表（非同光缆问题）
  Array.from(cableSegmentCodes.entries()).forEach(([code, info]) => {
    // 跳过已经添加的同光缆问题
    if (info.error !== '同光缆' && info.records.length > 0) {
      const color = colors[colorIndex % colors.length];
      colorIndex++;

      cableColorFilters.value.push({
        code: code,
        color: color,
        type: 'cableSegment',
        name: info.name,
        error: info.error
      });
    }
  });

  // 添加支撑段编码到支撑段颜色筛选列表（同管道问题）
  Array.from(pipeSegmentCodes.entries()).forEach(([pipeCode, info]) => {
    // 只有有记录的支撑段才添加到筛选列表
    if (info.records.length > 0) {
      // 获取颜色
      const color = colors[colorIndex % colors.length];
      colorIndex++;

      // 添加到颜色筛选列表
      pipeColorFilters.value.push({
        code: pipeCode,
        color: color,
        type: 'pipeSegment',
        name: info.name,
        error: '同管道'
      });
    }
  });

  console.log('相同光缆记录分析结果:', sameCableRecords.value);
  console.log('相同管道记录分析结果:', samePipeRecords.value);
  console.log('光缆颜色筛选按钮:', cableColorFilters.value);
  console.log('支撑段颜色筛选按钮:', pipeColorFilters.value);
};





// 处理路由错误数据
const processRouteErrors = () => {
  // 如果没有原始数据，则不进行处理
  if (originalRouteErrors.value.length === 0) {
    console.log('没有原始数据，不进行处理');
    return;
  }

  // 每次处理前先从原始数据恢复路由错误数据
  // 注意这里不直接修改 routeErrors.value，因为它是原始数据源
  // 而是使用深拷贝创建一份新数据进行处理
  routeErrors.value = JSON.parse(JSON.stringify(originalRouteErrors.value));

  // 清空当前分组数据
  groupedRouteErrors.value = [];

  // 更新表格列的筛选项
  routeErrorColumns.value = getRouteErrorColumns();

  console.log(`处理后的路由错误数据: 原始数量=${routeErrors.value.length}`);
};

// 高亮显示与光缆段或同光缆相关的管道段
const handleCableSegmentHighlight = (code: string) => {
  console.log('处理光缆/光缆段高亮:', code);

  // 先在光缆颜色筛选中查找
  let filterItem = cableColorFilters.value.find(filter => filter.code === code);

  // 如果在光缆颜色筛选中没找到，再在支撑段颜色筛选中查找
  if (!filterItem) {
    filterItem = pipeColorFilters.value.find(filter => filter.code === code);
  }

  if (!filterItem) {
    console.warn(`未找到筛选项: ${code}`);
    return;
  }

  console.log('找到筛选项:', filterItem);

  // 如果是光缆段或同光缆，处理高亮显示相关管道段
  if (filterItem.type === 'cableSegment' || filterItem.type === 'cable') {
    // 如果当前已选中，取消选中
    if (selectedCableSegment.value === code) {
      selectedCableSegment.value = '';
      // 恢复所有管道段的原始颜色
      resetPipeSegmentColors();
    } else {
      // 否则选中该光缆/光缆段，高亮显示相关管道段
      selectedCableSegment.value = code;
      // 高亮显示与该光缆/光缆段相关的管道段
      highlightRelatedPipeSegments(code);
    }
  }
};

// 高亮显示与光缆段或同光缆相关的管道段
const highlightRelatedPipeSegments = (code: string) => {
  console.log('高亮显示与光缆/光缆段相关的管道段:', code);

  // 先检查是否是同光缆问题
  const cableFilter = cableColorFilters.value.find(filter =>
    filter.code === code && filter.type === 'cable' && filter.error === '同光缆'
  );

  if (cableFilter) {
    console.log('处理同光缆问题:', cableFilter);

    // 查找所有与该光缆相关的管道段
    // 在路由错误数据中查找与该光缆相关的记录
    const relatedRecords = originalRouteErrors.value.filter(
      record => record.error === '同光缆' && record.cable_code === code
    );

    if (relatedRecords.length === 0) {
      console.warn(`未找到与光缆 ${code} 相关的记录`);
      return;
    }

    console.log(`找到 ${relatedRecords.length} 条与光缆 ${code} 相关的记录`);

    // 从这些记录中提取光缆段编码
    const cableSegmentCodes = new Set<string>();
    relatedRecords.forEach(record => {
      if (record.cs_code) {
        cableSegmentCodes.add(record.cs_code);
      }
    });

    if (cableSegmentCodes.size === 0) {
      console.warn(`未找到与光缆 ${code} 相关的光缆段`);
      return;
    }

    console.log(`找到 ${cableSegmentCodes.size} 个与光缆 ${code} 相关的光缆段:`, Array.from(cableSegmentCodes));

    // 收集所有相关的管道段
    const relatedPipeSegments = new Set<string>();
    cableSegmentCodes.forEach(csCode => {
      const pipeSegments = cableSegmentToPipeSegmentMap.get(csCode);
      if (pipeSegments && pipeSegments.size > 0) {
        pipeSegments.forEach(ps => relatedPipeSegments.add(ps));
      }
    });

    if (relatedPipeSegments.size === 0) {
      console.warn(`未找到与光缆 ${code} 相关的管道段`);
      return;
    }

    console.log(`找到 ${relatedPipeSegments.size} 个与光缆 ${code} 相关的管道段:`, Array.from(relatedPipeSegments));

    // 遍历所有管道段，设置颜色
    mapPipeSegments.value.forEach(segment => {
      const segmentCode = segment.code || segment.zc_section_code;
      if (segmentCode && relatedPipeSegments.has(segmentCode)) {
        // 如果是相关管道段，设置为紫色
        segment.highlightColor = OVERLAP_COLOR;
        segment.isHighlighted = true;
      } else {
        // 否则恢复原始颜色
        segment.highlightColor = undefined;
        segment.isHighlighted = false;
      }
    });
  } else {
    // 处理光缆段问题
    // 获取与该光缆段相关的管道段
    const relatedPipeSegments = cableSegmentToPipeSegmentMap.get(code);
    if (!relatedPipeSegments || relatedPipeSegments.size === 0) {
      console.warn(`未找到与光缆段 ${code} 相关的管道段`);
      return;
    }

    console.log(`找到 ${relatedPipeSegments.size} 个相关管道段:`, Array.from(relatedPipeSegments));

    // 遍历所有管道段，设置颜色
    mapPipeSegments.value.forEach(segment => {
      const segmentCode = segment.code || segment.zc_section_code;
      if (segmentCode && relatedPipeSegments.has(segmentCode)) {
        // 如果是相关管道段，设置为紫色
        segment.highlightColor = OVERLAP_COLOR;
        segment.isHighlighted = true;
      } else {
        // 否则恢复原始颜色
        segment.highlightColor = undefined;
        segment.isHighlighted = false;
      }
    });
  }

  // 更新地图显示
  updateMapDisplay();
};

// 恢复所有管道段的原始颜色
const resetPipeSegmentColors = () => {
  console.log('恢复所有管道段的原始颜色');

  // 遍历所有管道段，恢复原始颜色
  mapPipeSegments.value.forEach(segment => {
    segment.highlightColor = undefined;
    segment.isHighlighted = false;
  });

  // 更新地图显示
  updateMapDisplay();
};

// 增强现有的全选和清除函数
const enhanceSelectAllColorFilters = () => {
  console.log('增强版全选函数');

  // 调用原始函数
  selectAllColorFilters();

  // 更新路由错误数据
  processRouteErrors();
};

const enhanceClearColorFilters = () => {
  // 调用原始函数
  clearColorFilters();

  // 取消光缆段高亮
  selectedCableSegment.value = '';
  resetPipeSegmentColors();

  // 更新路由错误数据
  processRouteErrors();
};


// 组件挂载时初始化
onMounted(async () => {
  console.log('=== 组件挂载，开始初始化 ===');
  console.log('路由参数:', route.query);
  console.log('b_pair:', b_pair.value);

  // 检查b_pair是否为空
  if (!b_pair.value) {
    console.error('错误: b_pair参数为空，无法初始化页面');
    message.error('设备对标识为空，无法显示详情');
    return;
  }

  // 优化加载流程：先初始化UI和地图，然后异步加载数据
  try {
    // 1. 先初始化地图容器和UI
    // 确保 DOM 已经渲染完成
    await new Promise(resolve => setTimeout(resolve, 100));

    // 初始化地图（如果地图容器存在）
    const mapContainerElement = document.getElementById('mapContainer');
    if (mapContainerElement) {
      console.log('地图容器已存在，尺寸:', mapContainerElement.offsetWidth, 'x', mapContainerElement.offsetHeight);

      // 初始化地图，但不加载数据
      initializeMap().then(result => {
        if (result) {
          console.log('地图初始化成功，等待数据加载');
          mapDataLoading.value = true; // 显示地图加载中状态
          mapLoadingText.value = '正在加载数据...';
        } else {
          console.warn('地图初始化失败，将在数据加载后重试');
        }
      });
    }

    // 2. 并行加载所有数据
    loading.value = true; // 显示整体加载状态

    // 并行请求所有数据
    const [infoResult, errorsResult] = await Promise.all([
      fetchDevicePairInfo(),
      fetchPairRouteErrors()
    ]);

    // 3. 处理数据加载结果
    if (!infoResult) {
      console.error('获取设备对基本信息失败');
      message.error('获取设备对基本信息失败');
    }

    if (!errorsResult) {
      console.error('获取路由错误数据失败');
      message.error('获取路由错误数据失败');
    }

    // 4. 如果有路由错误数据，加载地图数据
    if (routeErrors.value.length > 0) {
      console.log('有路由错误数据，准备加载地图数据');

      // 不管地图是否已初始化，都先开始加载数据
      // 这样用户可以先看到表格数据，而地图可以在后台慢慢加载
      console.log('开始异步加载地图数据');

      // 使用setTimeout将地图数据加载放到下一个事件循环中
      setTimeout(() => {
        // 如果地图已初始化，直接加载数据
        if (mapRef.value) {
          console.log('地图已初始化，直接加载数据');
          fetchMapData();
        } else {
          console.log('地图未初始化，等待地图初始化后加载数据');

          // 如果地图未初始化，等待地图初始化后加载数据
          const mapContainerElement = document.getElementById('mapContainer');
          if (mapContainerElement) {
            // 初始化地图
            initializeMap().then(mapInitResult => {
              if (mapInitResult) {
                console.log('延迟初始化地图成功，加载地图数据');
                fetchMapData();
              } else {
                console.error('延迟初始化地图失败');
                // 不显示错误消息，避免打扰用户
                console.log('地图初始化失败，但不影响其他功能');
              }
            });
          } else {
            console.error('地图容器不存在，无法初始化地图');
          }
        }
      }, 100); // 短暂延迟，让UI先渲染
    } else {
      console.log('没有路由错误数据，不加载地图数据');
    }

    console.log('=== 组件初始化完成 ===');
  } catch (error) {
    console.error('初始化出错:', error);
    console.error('错误详情:', error.message);
    console.error('错误堆栈:', error.stack);
    message.error('初始化出错: ' + (error.message || '未知错误'));
  } finally {
    loading.value = false; // 关闭整体加载状态
  }
});
</script>

<style lang="less" scoped>
.device-detail-container {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: baseline;
}

.page-title h1 {
  font-size: 20px;
  font-weight: 500;
  margin: 0;
  color: #1f1f1f;
}

.page-title .subtitle {
  font-size: 14px;
  color: #666;
  margin-left: 12px;
}

.info-card {
  margin-bottom: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.problem-details {
  padding: 16px;
}

.problem-item {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background-color: #fafafa;
}

.problem-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.problem-time {
  color: #999;
  font-size: 12px;
}

.problem-content p {
  margin-bottom: 8px;
}

.empty-data {
  padding: 40px 0;
  text-align: center;
}

.color-filter-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  width: 100%;
}

.color-filter-item:hover {
  background-color: #f5f5f5;
}

.color-filter-item.selected {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

.color-indicator {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
}

.filter-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #1890ff;
  border-left: 4px solid #1890ff;
  padding-left: 12px;
  line-height: 22px;
}

.problem-item {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background-color: #fafafa;
}

/* 问题描述样式 */
.problem-description {
  padding: 16px;
}

.problem-tables {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.problem-table-section {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 16px;
  border: 1px solid #f0f0f0;
}

.table-title {
  padding: 12px 16px;
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  display: flex;
  align-items: center;
  position: relative;
}

.table-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: #1890ff;
}

.table-title .ant-tag {
  font-size: 14px;
  padding: 2px 8px;
  height: auto;
  line-height: 1.5;
  font-weight: 500;
  border-radius: 2px;
  margin-right: 8px;
}

.table-title-count {
  font-size: 14px;
  color: #8c8c8c;
  font-weight: normal;
  margin-left: 4px;
}

/* 颜色筛选按钮样式 */
.color-filter-buttons {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  background-color: #fafafa;
  padding: 10px;
  border-radius: 2px;
  border: 1px solid #f0f0f0;
  margin-bottom: 12px;
}

.filter-label {
  font-weight: 500;
  margin-right: 12px;
  color: #595959;
  font-size: 13px;
}

.color-filter-buttons .ant-btn {
  height: 28px;
  padding: 0 10px;
  border-radius: 2px;
  transition: all 0.2s;
  border-width: 2px !important;
  border-style: solid !important;
  font-size: 12px;
}

.color-filter-buttons .ant-btn:hover {
  opacity: 0.85;
  transform: translateY(-1px);
}

/* 紧凑型空数据提示 */
.empty-data-compact {
  padding: 8px 0;
}

.empty-data-compact .ant-empty {
  margin: 8px 0;
}

.empty-data-compact .ant-empty-image {
  height: 32px;
  margin-bottom: 4px;
}

.empty-data-compact .ant-empty-description {
  font-size: 12px;
  color: #999;
}


:deep(.ant-table-thead > tr > th) {
    background: #f5f7fa;
    font-weight: 500;
    border-bottom: 1px solid #e8e8e8;
    transition: background 0.3s ease;
  }

  :deep(.ant-table-thead > tr > th:hover) {
    background: #e6f7ff;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background: #f0f7ff;
  }

  :deep(.ant-modal-body) {
    max-height: 70vh;
    overflow-y: auto;
  }

  :deep(.ant-descriptions-item-label) {
    width: 120px;
    font-weight: 500;
    background: #f5f7fa;
  }

  :deep(.ant-descriptions-item-content) {
    color: #333;
  }

  /* 高亮列样式 */
  :deep(.highlight-column) {
    background-color: #f0f7ff;
    font-weight: bold;
  }

  /* 地图相关样式 */
  .map-container-layout {
    display: flex;
    margin-top: 16px;
    gap: 16px;
    height: 750px; /* 增加高度，确保左右两侧高度一致，能容纳4行设备 */
  }

  .support-segment-list {
    width: 30%;
    height: 100%;
    display: flex;
    flex-direction: column;
    flex-grow: 0;
    flex-shrink: 0;
  }

  .support-segment-header {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 12px;
    color: #1f1f1f;
  }

  .support-segment-items {
    flex: 0 1 auto;
    overflow-y: auto;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    max-height: 250px; /* 减少高度，给设备清单留出更多空间 */
  }

  .support-segment-item {
    transition: all 0.2s;
    border-left: 4px solid transparent;
    cursor: pointer;
  }

  .support-segment-item:hover {
    background-color: #f5f5f5;
  }

  .support-segment-item-selected {
    border-left-width: 4px;
    border-left-style: solid;
  }

  .support-segment-item-content {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .color-indicator {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    margin-right: 8px;
  }

  .segment-info {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .segment-code {
    font-size: 13px;
    font-weight: 500;
    color: #1f1f1f;
  }

  .segment-name {
    font-size: 12px;
    color: #666;
  }

  .segment-type {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }

  .segment-type .ant-tag {
    margin-right: 0;
    font-size: 11px;
    line-height: 1.2;
    padding: 2px 4px;
  }

  .selected-tag {
    margin-left: 8px;
  }

  .segment-type-filter {
    margin-bottom: 12px;
    display: flex;
    width: 100%;
  }

  .segment-type-filter .ant-radio-button-wrapper {
    flex: 1;
    text-align: center;
    padding: 0 8px;
    font-size: 12px;
  }

  .support-segment-actions {
    padding: 8px;
    background-color: #fafafa;
  }

  .support-segment-buttons {
    display: flex;
    width: 100%;
    gap: 8px;
  }

  .support-segment-buttons .ant-btn {
    flex: 1;
  }

  .map-area {
    width: 70%;
    display: flex;
    flex-direction: column;
  }

  .map-area-full {
    width: 100%;
  }

  .map-controls {
    padding: 12px;
    background-color: #fafafa;
    border-radius: 4px;
    margin-bottom: 12px;
    border: 1px solid #f0f0f0;
  }

  .map-controls-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
  }

  .optical-path-selection {
    display: flex;
    align-items: center;
    flex-grow: 1;
    overflow-x: auto;
  }

  .optical-path-label {
    font-weight: bold;
    margin-right: 10px;
    white-space: nowrap;
    color: #1f1f1f;
  }

  .optical-path-checkboxes {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    max-width: 80%;
  }

  .optical-path-selection {
    display: flex;
    flex-direction: column;
    max-height: 120px; /* 减少高度 */
    overflow-y: auto;
    padding-right: 10px;
  }

  .optical-path-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px; /* 减少间距 */
  }

  .optical-path-label {
    font-weight: bold;
    margin-right: 8px;
    min-width: 70px;
    white-space: nowrap;
  }

  .optical-path-actions {
    display: flex;
    gap: 8px;
  }

  .optical-path-groups {
    display: flex;
    flex-direction: column;
    gap: 8px; /* 减少间距 */
  }

  .optical-path-group {
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 6px; /* 减少内边距 */
    background-color: #fafafa;
  }

  .optical-path-group-row {
    display: flex;
    align-items: center;
  }

  .group-header {
    font-weight: bold;
    min-width: 120px; /* 固定宽度 */
    padding-right: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .group-roads {
    display: flex;
    flex-wrap: wrap;
    gap: 6px; /* 减少间距 */
    flex: 1;
  }

  .group-actions {
    display: flex;
    gap: 6px;
    margin-left: 10px;
    white-space: nowrap;
  }

  .no-optical-paths {
    padding: 16px;
    text-align: center;
    color: #999;
  }

  .optical-path-checkbox {
    margin-right: 0;
    font-size: 13px;
    padding: 2px 8px;
    border: 2px solid;
    border-radius: 4px;
    white-space: nowrap;
    position: relative;
    transition: all 0.3s;
    display: flex;
    align-items: center;
  }

  .optical-path-checkbox .color-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-left: 6px;
    display: inline-block;
  }

  .optical-path-checkbox:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  .optical-path-checkbox.loading {
    opacity: 0.7;
    border-style: dashed;
  }

  .loading-indicator {
    font-size: 12px;
    color: #999;
    margin-left: 6px;
    font-style: italic;
  }

  /* 设备清单样式 */
  .device-list-selection {
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    margin-bottom: 0; /* 移除底部边距 */
    flex-grow: 1; /* 允许设备清单区域占据剩余空间 */
    min-height: 220px; /* 确保有足够的高度容纳4行设备 */
  }

  .device-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e8e8e8;
  }

  .device-list-title {
    font-weight: bold;
    font-size: 14px;
  }

  .device-list-items {
    flex-grow: 1; /* 允许设备列表占据剩余空间 */
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 8px;
    background-color: #fafafa;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }

  .device-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
    height: auto; /* 自动调整高度 */
  }

  .device-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    width: 100%;
    box-sizing: border-box;
    background-color: #fff;
    height: 50px; /* 固定每个设备项的高度 */
    margin-bottom: 2px; /* 添加底部边距 */
  }

  .device-item:hover {
    background-color: #f5f5f5;
    border-color: #1890ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }

  .device-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-weight: bold;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .device-info {
    overflow: hidden;
  }

  .device-name {
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .device-type {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .no-devices {
    padding: 16px;
    text-align: center;
    color: #999;
  }

  .map-tools {
    white-space: nowrap;
    margin-left: 16px;
  }

  .map-wrapper {
    position: relative;
    height: 650px; /* 增加高度 */
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }

  .map-loading-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 4px;
    z-index: 1000;
    pointer-events: none; /* 允许点击穿透 */
  }

  .map-loading-text {
    font-size: 14px;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  }

  /* 不合规坐标表格样式 */
  .invalid-coordinates-section {
    margin-top: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }

  .invalid-coordinates-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 12px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fafafa;
  }

  .invalid-coordinates-title {
    font-weight: bold;
    font-size: 14px;
    color: #f5222d;
  }

  .invalid-coordinates-table {
    padding: 4px;
  }

  .text-gray {
    color: #999;
    font-style: italic;
  }

  .map-container-element {
    width: 100%;
    height: 650px; /* 增加高度 */
    border: 1px solid #eee;
    border-radius: 4px;
    overflow: hidden;
  }

  /* 悬浮按钮样式 */
  .floating-buttons {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1000;

    .floating-button {
      width: 120px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      border-radius: 4px;

      &:hover {
        transform: translateX(-5px);
        transition: transform 0.3s ease;
      }
    }
  }
</style>
