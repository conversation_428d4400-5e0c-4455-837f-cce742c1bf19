<template>
  <div class="history-version-container centered-container">
    <!-- 右键菜单 -->
    <a-dropdown :visible="contextMenuVisible" :trigger="['contextmenu']" @visibleChange="handleContextMenuVisibleChange">
      <template #overlay>
        <a-menu>
          <a-menu-item v-if="contextMenuVersion && contextMenuVersion.id !== 'current'" key="edit" @click="editVersionDescription">
            <edit-outlined /> 修改备注
          </a-menu-item>
          <a-menu-item v-if="contextMenuVersion && contextMenuVersion.id !== 'current'" key="delete" @click="deleteVersion">
            <delete-outlined /> 删除版本
          </a-menu-item>
        </a-menu>
      </template>
      <div class="context-menu-trigger" :style="contextMenuStyle"></div>
    </a-dropdown>

    <!-- 修改备注对话框 -->
    <a-modal
      v-model:visible="editDescriptionModalVisible"
      title="修改版本备注"
      @ok="handleEditDescriptionOk"
      @cancel="handleEditDescriptionCancel"
      :maskClosable="false"
    >
      <a-form :model="editDescriptionForm" layout="vertical">
        <a-form-item label="版本备注" name="description">
          <a-textarea
            v-model:value="editDescriptionForm.description"
            placeholder="请输入版本备注"
            :rows="4"
            :maxlength="200"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-modal>
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h1>保护组历史版本查看</h1>
        <span class="subtitle">{{ protectionGroupInfo.name || '未知保护组' }}</span>
      </div>
      <div class="page-actions">
        <a-button @click="goBack">
          <template #icon>
            <left-outlined />
          </template>
          返回
        </a-button>
      </div>
    </div>

    <!-- 保护组基本信息 -->
    <a-card class="info-card" title="保护组基本信息" :loading="loading">
      <!-- A环设备基本信息 -->
      <template v-if="protectionGroupInfo.type === 'ringNetwork'">
        <a-descriptions bordered :column="4">
          <!-- 最重要的信息放在最显著位置 -->
          <a-descriptions-item label="环号" :span="1" style="font-weight: bold;">
            {{ protectionGroupInfo.ringNumber || route.query.ringNum || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="区域" :span="1">
            {{ protectionGroupInfo.area || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="城市" :span="2">
            {{ protectionGroupInfo.city || '未知' }}
          </a-descriptions-item>

          <!-- B1设备信息 -->
          <a-descriptions-item label="B1设备名称" :span="1">
            {{ protectionGroupInfo.b1Name || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="B1设备编码" :span="1" style="font-weight: bold;">
            {{ protectionGroupInfo.b1Code || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="B1设备IP" :span="2">
            {{ protectionGroupInfo.b1Ip || '未知' }}
          </a-descriptions-item>

          <!-- B2设备信息 -->
          <a-descriptions-item label="B2设备名称" :span="1">
            {{ protectionGroupInfo.b2Name || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="B2设备编码" :span="1" style="font-weight: bold;">
            {{ protectionGroupInfo.b2Code || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="B2设备IP" :span="2">
            {{ protectionGroupInfo.b2Ip || '未知' }}
          </a-descriptions-item>

          <!-- 逻辑设备信息 (次要信息) -->
          <!-- <a-descriptions-item label="A设备名称" :span="2">
            {{ protectionGroupInfo.lgcEqpName || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="A设备编码" :span="2">
            {{ protectionGroupInfo.lgcEqpNo || '未知' }}
          </a-descriptions-item> -->
        </a-descriptions>
      </template>

      <!-- 设备对双路由/三路由基本信息 -->
      <template v-else-if="protectionGroupInfo.type === 'dualDeviceDualRoute' || protectionGroupInfo.type === 'dualDeviceTripleRoute'">
        <a-descriptions bordered :column="4">
          <a-descriptions-item label="设备对ID" :span="2">
            {{ protectionGroupInfo.devicePairId || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="保护组类型" :span="1">
            {{ getProtectionGroupTypeLabel(protectionGroupInfo.type) }}
          </a-descriptions-item>
          <a-descriptions-item label="区域" :span="1">
            {{ protectionGroupInfo.area || '未知' }}
          </a-descriptions-item>

          <!-- 设备A信息 -->
          <a-descriptions-item label="设备A名称" :span="1">
            {{ protectionGroupInfo.deviceA?.device_name || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="设备A编码" :span="1">
            {{ protectionGroupInfo.deviceA?.device_code || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="设备A类型" :span="1">
            {{ protectionGroupInfo.deviceA?.device_type || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="设备A IP" :span="1">
            {{ protectionGroupInfo.deviceA?.device_ip || '未知' }}
          </a-descriptions-item>

          <!-- 设备B信息 -->
          <a-descriptions-item label="设备B名称" :span="1">
            {{ protectionGroupInfo.deviceB?.device_name || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="设备B编码" :span="1">
            {{ protectionGroupInfo.deviceB?.device_code || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="设备B类型" :span="1">
            {{ protectionGroupInfo.deviceB?.device_type || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="设备B IP" :span="1">
            {{ protectionGroupInfo.deviceB?.device_ip || '未知' }}
          </a-descriptions-item>

          <!-- 发现时间 -->
          <a-descriptions-item label="发现时间" :span="4">
            {{ protectionGroupInfo.discoveryTime || '未知' }}
          </a-descriptions-item>
        </a-descriptions>
      </template>

      <!-- 单设备双上联基本信息 -->
      <template v-else-if="protectionGroupInfo.type === 'singleDeviceDualUplink'">
        <a-descriptions bordered :column="3">
          <a-descriptions-item label="设备名称" :span="1">
            {{ protectionGroupInfo.deviceName || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="设备编码" :span="1">
            {{ protectionGroupInfo.deviceCode || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="设备类型" :span="1">
            {{ protectionGroupInfo.deviceType || '未知' }}
          </a-descriptions-item>

          <a-descriptions-item label="所属区域" :span="1">
            {{ protectionGroupInfo.area || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="IP地址" :span="1">
            {{ protectionGroupInfo.ipAddress || '未知' }}
          </a-descriptions-item>
          <a-descriptions-item label="问题类型" :span="1">
            {{ protectionGroupInfo.problemType || '未知' }}
          </a-descriptions-item>

          <a-descriptions-item label="场景类型" :span="1">
            {{ getProtectionGroupTypeLabel(protectionGroupInfo.type) }}
          </a-descriptions-item>
          <a-descriptions-item label="发现时间" :span="2">
            {{ protectionGroupInfo.discoveryTime || '未知' }}
          </a-descriptions-item>
        </a-descriptions>
      </template>

      <!-- 默认情况 -->
      <template v-else>
        <a-empty description="暂无保护组信息" />
      </template>
    </a-card>


    <!-- 地图对比 -->
    <a-card class="map-comparison-card" title="地图对比">
      <template #extra>
        <a-spin v-if="dataLoading" size="small" />
      </template>
      <div class="map-comparison-header">
        <div class="map-comparison-title">地图对比</div>
        <div class="map-comparison-actions">
          <a-button
            type="primary"
            size="small"
            @click="showOpticalPathList"
            style="margin-left: 8px;"
          >
            <eye-outlined /> 查看光路清单
          </a-button>
        </div>
      </div>
      <div class="map-comparison-container">
        <!-- 左侧地图（当前版本） -->
        <div class="map-container left-map">
          <div class="map-title">当前版本</div>
          <div class="map-content">
            <OpticalPathMap
              ref="currentMapRef"
              :optical-paths="currentOpticalPathObjects"
              :city-code="defaultCityCode"
              :title="'当前版本地图'"
              :auto-load="true"
              :show-devices="true"
              :show-pipe-segments="true"
              :show-legend="true"
              @map-initialized="handleCurrentMapInitialized"
              @data-loaded="handleCurrentMapDataLoaded"
              @error="handleMapError"
            />
          </div>
        </div>

        <!-- 右侧地图（历史版本） -->
        <div class="map-container right-map">
          <div class="map-title">历史版本: {{ getSelectedVersionName() }}</div>
          <div class="map-content">
            <OpticalPathMapByGeojson
              ref="historyMapRef"
              :geojsonData="historyVersionGeojson"
              :city-code="defaultCityCode"
              :mapId="'historyMap'"
              :title="'历史版本地图'"
              :auto-load="true"
              :show-devices="true"
              :show-pipe-segments="true"
              :show-legend="true"
              @map-initialized="handleHistoryMapInitialized"
              @data-loaded="handleHistoryMapDataLoaded"
              @error="handleMapError"
            />
            <!-- 版本选择覆盖层 - 只有在没有历史版本时才显示 -->
            <div v-if="selectedVersion === 'current' && versionList.length <= 1" class="version-overlay">
              <div class="overlay-content">
                <a-empty description="没有历史版本数据" />
              </div>
            </div>
            <!-- 加载中覆盖层 -->
            <div v-if="historyMapLoading" class="loading-overlay">
              <a-spin tip="加载历史版本地图中..." size="large" />
            </div>
          </div>
        </div>
      </div>
    </a-card>



    <!-- 光路清单对比 -->
    <a-card v-if="showOpticalPaths" class="optical-path-card" title="光路清单对比" :loading="loading">
      <div class="optical-path-container">
        <div class="optical-path-list left-list">
          <div class="optical-path-title">当前版本</div>
          <a-table
            :dataSource="currentOpticalPaths"
            :columns="opticalPathColumns"
            :pagination="{ pageSize: 5 }"
            size="small"
            :rowKey="record => record.code"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-button type="link" size="small" @click="highlightOpticalPath(record.code, 'current')">
                  高亮显示
                </a-button>
              </template>
            </template>
          </a-table>
        </div>

        <div class="optical-path-list right-list">
          <div class="optical-path-title">历史版本: {{ getSelectedVersionName() }}</div>
          <a-empty v-if="selectedVersion === 'current'" description="请选择一个历史版本" />
          <a-table
            v-else
            :dataSource="historyOpticalPaths"
            :columns="opticalPathColumns"
            :pagination="{ pageSize: 5 }"
            size="small"
            :rowKey="record => record.code"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-button type="link" size="small" @click="highlightOpticalPath(record.code, 'history')">
                  高亮显示
                </a-button>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-card>

    <!-- 版本时间轴 -->
    <a-card class="version-timeline-card" title="版本历史" :loading="loading">
      <div class="version-timeline-content">
        <div class="timeline-container">
          <!-- 版本行容器 - 使用flex布局实现自动换行 -->
          <div class="version-rows-container">
            <!-- 计算每行最多5个版本，自动换行 -->
            <div
              v-for="(rowVersions, rowIndex) in getVersionRows()"
              :key="'row-' + rowIndex"
              class="version-row"
            >
              <!-- 时间轴 -->
              <div class="timeline-line"></div>

              <!-- 版本节点 -->
              <div
                v-for="(version, colIndex) in rowVersions"
                :key="version.id"
                class="timeline-node"
                :class="{
                  'active': selectedVersion === version.id,
                  'current-version': version.id === 'current'
                }"
                :style="getNodeStyle(rowIndex, colIndex, rowVersions.length)"
                @click="version.id !== 'current' && handleVersionChange({ target: { value: version.id } })"
                @contextmenu.prevent="version.id !== 'current' && showVersionContextMenu($event, version)"
              >
                <div class="snapshot-icon">
                  <div class="cd-icon" v-if="version.id !== 'current'">
                    <div class="cd-inner"></div>
                  </div>
                  <div class="folder-icon" v-else>
                    <div class="folder-front"></div>
                    <div class="folder-back"></div>
                  </div>
                </div>
                <div class="timeline-label">
                  <div class="version-name">{{ version.name }}</div>
                  <div class="version-date">{{ formatDate(version.date) }}</div>
                  <div v-if="version.creator" class="version-creator">{{ version.creator }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 版本对比信息 -->
        <div class="version-comparison-info" v-if="selectedVersion !== 'current'">
          <div class="comparison-title">
            <clock-circle-outlined /> 版本对比
          </div>
          <div class="comparison-content">
            <div class="comparison-item">
              <div class="comparison-label">当前版本:</div>
              <div class="comparison-value">{{ formatDate(new Date().toISOString()) }}</div>
            </div>
            <div class="comparison-item">
              <div class="comparison-label">历史版本:</div>
              <div class="comparison-value">{{ getSelectedVersionName() }} ({{ formatDate(getSelectedVersionDate()) }})</div>
            </div>
            <div class="comparison-item">
              <div class="comparison-label">时间差:</div>
              <div class="comparison-value">{{ getTimeDifference() }}</div>
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 问题对比 -->
    <a-card class="problem-comparison-card" title="问题对比" :loading="loading">
      <div class="problem-comparison-container">
        <a-empty v-if="selectedVersion === 'current'" description="请选择一个历史版本进行对比" />
        <div v-else class="problem-comparison-content">
          <a-empty description="暂无问题对比数据" />
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { LeftOutlined, SwapOutlined, EyeOutlined, ClockCircleOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { useUnifiedApi } from '@/hooks/web/useUnifiedApi';
import { useUserStoreWithOut } from '@/store/modules/user';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import zhCN from 'dayjs/locale/zh-cn';
import OpticalPathMap from './OpticalPathMap.vue';
import OpticalPathMapByGeojson from './OpticalPathMapByGeojson.vue';

import { notification } from 'ant-design-vue';


// 扩展dayjs以支持相对时间
dayjs.extend(relativeTime);
// 设置dayjs使用中文
dayjs.locale('zh-cn');

defineOptions({
  name: 'HistoryVersionView'
});

const route = useRoute();
const router = useRouter();

// 获取用户存储
const userStore = useUserStoreWithOut();

// 获取地市代码
const areaCode = userStore.getAreaCode;
console.log('当前地市代码:', areaCode);

// 如果是江苏，默认查询南京的数据
const initialCityCode = (route.query.cityCode as string) || (areaCode === 'js' ? 'nj' : areaCode);
// 使用ref，这样可以在后续修改
const defaultCityCode = ref(initialCityCode);
console.log('使用的城市代码:', defaultCityCode.value);

// 初始化统一API调用钩子
const unifiedApi = useUnifiedApi({
  rootPath: '/graph-rest-api', // 使用与其他页面一致的根路径
  defaultCityCode: defaultCityCode.value // 设置默认地市代码
});

// 从路由参数中获取保护组信息
const protectionGroupId = ref(route.query.protectionGroupId as string || '');
const protectionGroupType = ref(route.query.protectionGroupType as string || '');
const scenarioType = ref(route.query.scenarioType as string || '');

// 加载状态
const loading = ref(false);

// 保护组基本信息
const protectionGroupInfo = ref<any>({
  id: protectionGroupId.value,
  name: route.query.protectionGroupName || '未知保护组',
  type: protectionGroupType.value,
  area: route.query.areaName || '未知',
  city: route.query.cityName || '未知'
});

// 版本列表 - 初始只有当前版本
const versionList = ref<any[]>([
  { id: 'current', name: '当前版本', date: new Date().toISOString(), description: '当前工作版本' }
]);

// 当前选中的版本
const selectedVersion = ref('current');

// 是否交换地图位置
const isSwapped = ref(false);

// 是否显示光路清单
const showOpticalPaths = ref(false);

// 当前版本光路清单
const currentOpticalPaths = ref<any[]>([]);

// 历史版本光路清单
const historyOpticalPaths = ref<any[]>([]);

// 当前版本光路对象（带颜色）
const currentOpticalPathObjects = ref<any[]>([]);

// 历史版本光路对象（带颜色）
const historyOpticalPathObjects = ref<any[]>([]);

// 历史版本 GeoJSON 数据
const historyVersionGeojson = ref<any>(null);

// 地图组件引用
const currentMapRef = ref<any>(null);
const historyMapRef = ref<any>(null);

// 地图容器对象
const currentMapContainer = ref<any>(null);
const historyMapContainer = ref<any>(null);

// 右键菜单相关状态
const contextMenuVisible = ref(false);
const contextMenuStyle = ref({
  position: 'fixed',
  top: '0px',
  left: '0px',
  display: 'none'
});
const contextMenuVersion = ref<any>(null);

// 修改备注对话框相关状态
const editDescriptionModalVisible = ref(false);
const editDescriptionForm = ref({
  id: '',
  description: ''
});

// 光路清单表格列定义
const opticalPathColumns = [
  {
    title: '光路编码',
    dataIndex: 'code',
    key: 'code',
    width: 180
  },
  {
    title: '光路名称',
    dataIndex: 'name',
    key: 'name',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 100
  }
];

// 地图相关状态
const dataLoading = ref(false); // 数据加载状态，不影响地图显示
const historyMapLoading = ref(false); // 历史版本地图加载状态

// 获取选中版本的名称
const getSelectedVersionName = () => {
  const version = versionList.value.find(v => v.id === selectedVersion.value);
  return version ? version.name : '未知版本';
};

// 获取选中版本的日期
const getSelectedVersionDate = () => {
  const version = versionList.value.find(v => v.id === selectedVersion.value);
  return version ? version.date : '';
};

// 获取时间差
const getTimeDifference = () => {
  if (selectedVersion.value === 'current') {
    return '无';
  }

  const version = versionList.value.find(v => v.id === selectedVersion.value);
  if (!version) {
    return '未知';
  }

  const versionDate = dayjs(version.date);
  const currentDate = dayjs();

  // 计算时间差
  return versionDate.from(currentDate, true) + '前';
};

// 获取版本行数据 - 每行最多5个版本
const getVersionRows = () => {
  const rows: any[][] = [];
  const maxPerRow = 5;

  // 复制版本列表，以便按时间倒序排列（最新的在最前面）
  const sortedVersions = [...versionList.value].sort((a, b) => {
    // 确保当前版本始终在第一行第一个位置
    if (a.id === 'current') return -1;
    if (b.id === 'current') return 1;
    // 其他版本按时间倒序排列
    return new Date(b.date).getTime() - new Date(a.date).getTime();
  });

  // 将版本分组到行中，每行最多5个
  for (let i = 0; i < sortedVersions.length; i += maxPerRow) {
    rows.push(sortedVersions.slice(i, i + maxPerRow));
  }

  return rows;
};

// 获取节点样式 - 根据行和列计算位置
const getNodeStyle = (rowIndex: number, colIndex: number, totalInRow: number) => {
  // 计算在行中的位置百分比
  const leftPercentage = colIndex * (100 / (totalInRow - 1 || 1));

  // 如果只有一个元素，居中显示
  if (totalInRow === 1) {
    return { left: '50%' };
  }

  return {
    left: `${leftPercentage}%`
  };
};

// 格式化日期
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm');
};

// 不再需要获取光路坐标数据的函数，由OpticalPathMap组件处理

// 获取保护组类型标签
const getProtectionGroupTypeLabel = (type: string) => {
  switch (type) {
    case 'singleDeviceDualUplink':
      return '单设备双上联';
    case 'dualDeviceDualRoute':
      return '设备对双路由';
    case 'dualDeviceTripleRoute':
      return '设备对三路由';
    case 'ringNetwork':
      return 'A环';
    default:
      return type || '未知';
  }
};

// 处理版本变更
const handleVersionChange = (e: any) => {
  console.log('切换到版本:', e.target.value);
  selectedVersion.value = e.target.value;

  // 如果选择了当前版本，清除历史版本数据
  if (selectedVersion.value === 'current') {
    historyVersionGeojson.value = null;
    historyOpticalPathObjects.value = [];
    historyOpticalPaths.value = [];

    // 确保历史地图已初始化
    if (historyMapRef.value && historyMapContainer.value) {
      // 清空历史地图图层
      historyMapContainer.value.overLayer.clearLayers();
    }
  }
  // 如果选择了历史版本，加载历史版本数据
  else {
    // 显示加载状态
    historyMapLoading.value = true;

    // 加载历史版本数据
    loadHistoryVersionData(selectedVersion.value);

    // 加载历史版本光路清单
    loadHistoryOpticalPaths(selectedVersion.value);
  }
};

// 切换地图位置 - 由UI按钮调用
const switchMapPositions = () => {
  isSwapped.value = !isSwapped.value;
  console.log('切换地图位置:', isSwapped.value ? '历史版本在左' : '当前版本在左');
};

// 显示光路清单
const showOpticalPathList = () => {
  showOpticalPaths.value = !showOpticalPaths.value;
  console.log('显示光路清单:', showOpticalPaths.value);

  // 如果显示光路清单，加载当前版本光路清单
  if (showOpticalPaths.value && currentOpticalPaths.value.length === 0) {
    // 当前版本光路清单已经在loadSingleDeviceMapData中通过saveCurrentOpticalPaths保存
    // 如果还没有数据，重新加载当前版本数据
    loadCurrentVersionData();
  }

  // 如果显示光路清单，并且选择了历史版本，加载历史版本光路清单
  if (showOpticalPaths.value && selectedVersion.value !== 'current' && historyOpticalPaths.value.length === 0) {
    loadHistoryOpticalPaths(selectedVersion.value);
  }
};

// 高亮显示光路
const highlightOpticalPath = (code: string, type: 'current' | 'history') => {
  console.log('高亮显示光路:', code, type);

  // 根据类型选择地图组件
  const mapRef = type === 'current' ? currentMapRef.value : historyMapRef.value;

  if (!mapRef) {
    console.error('地图组件不存在，无法高亮显示光路');
    message.error('地图组件不存在，无法高亮显示光路');
    return;
  }

  // 特殊处理"重叠部分"
  if (code === 'overlapping') {
    console.log(`高亮显示${type === 'current' ? '当前' : '历史'}版本重叠部分`);
    const result = mapRef.highlightOverlappingSegments();

    if (result) {
      console.log(`成功高亮显示${type === 'current' ? '当前' : '历史'}版本重叠部分`);
    } else {
      console.warn(`未找到${type === 'current' ? '当前' : '历史'}版本重叠部分`);
      message.warning(`未找到${type === 'current' ? '当前' : '历史'}版本重叠部分`);
    }
    return;
  }

  // 高亮显示光路
  const result = mapRef.highlightOpticalPath(code);

  if (result) {
    console.log(`成功高亮显示${type === 'current' ? '当前' : '历史'}版本光路:`, code);
  } else {
    console.warn(`未找到${type === 'current' ? '当前' : '历史'}版本光路:`, code);
    message.warning(`未找到${type === 'current' ? '当前' : '历史'}版本光路: ${code}`);

    // 尝试高亮所有管线
    mapRef.highlightAllPaths();
  }
};

// 不再需要初始化地图函数，由OpticalPathMap组件处理



// 不再需要导入gis函数













// 加载当前版本数据
const loadCurrentVersionData = async () => {
  try {
    console.log('加载当前版本数据');

    // 根据保护组类型加载不同的数据
    if (protectionGroupType.value === 'singleDeviceDualUplink') {
      await loadSingleDeviceMapData();
    } else if (protectionGroupType.value === 'dualDeviceDualRoute' || protectionGroupType.value === 'dualDeviceTripleRoute') {
      // 实现设备对双路由/三路由地图数据加载
      await loadDevicePairMapData();
    } else if (protectionGroupType.value === 'ringNetwork') {
      // 实现A环地图数据加载
      await loadARingMapData();
    } else {
      console.warn('未知的保护组类型:', protectionGroupType.value);
      message.warning('未知的保护组类型');
    }

    console.log('当前版本数据加载完成');
  } catch (error) {
    console.error('加载当前版本数据失败:', error);
    console.error('错误详情:', error.stack);
    message.error('加载当前版本数据失败');
  }
};

// 加载单设备地图数据
const loadSingleDeviceMapData = async () => {
  try {
    // 构建查询参数
    const params = {
      deviceCode: protectionGroupId.value
    };

    console.log('查询单设备光路信息，设备编码:', protectionGroupId.value);
    console.log('使用API: SLY_DEVICE_GL_INFO_QUERY, 版本: V20250519112813991');

    // 开始加载数据
    dataLoading.value = true;

    // 调用API获取设备光路信息
    const glResponse = await unifiedApi.callApi(
      'SLY_DEVICE_GL_INFO_QUERY',
      'V20250519112813991',
      params,
      defaultCityCode.value.toLowerCase()
    );

    console.log('单设备光路信息API响应:', glResponse);

    if (glResponse && glResponse.data && glResponse.data.length > 0) {
      console.log('找到设备光路信息，数量:', glResponse.data.length);

      // 保存当前版本光路清单
      saveCurrentOpticalPaths(glResponse.data);

      // 完成加载
      dataLoading.value = false;
    } else {
      console.warn('未找到设备光路信息');
      message.warning('未找到设备光路信息');
      dataLoading.value = false;
    }
  } catch (error) {
    console.error('加载单设备地图数据失败:', error);
    message.error('加载单设备地图数据失败');
    dataLoading.value = false;
  }
};

// 加载设备对地图数据（双路由或三路由）
const loadDevicePairMapData = async () => {
  try {
    // 构建查询参数
    const params = {
      b_pair: protectionGroupId.value
    };

    console.log('查询设备对光路信息，设备对ID:', protectionGroupId.value);
    console.log('使用API: SLY_TRIPLE_PAIR_GL_INFO_QUERY, 版本: V20250520150541560');

    // 开始加载数据
    dataLoading.value = true;

    // 调用API获取设备对光路信息
    const glResponse = await unifiedApi.callApi(
      'SLY_TRIPLE_PAIR_GL_INFO_QUERY',
      'V20250520150541560',
      params,
      defaultCityCode.value.toLowerCase()
    );

    console.log('设备对光路信息API响应:', glResponse);

    if (glResponse && glResponse.data && glResponse.data.length > 0) {
      console.log('找到设备对光路信息，数量:', glResponse.data.length);

      // 对gl_code字段进行去重处理
      const uniqueGlCodes = new Set<string>();
      const uniqueGlData: any[] = [];

      // 收集所有不重复的光路编码
      glResponse.data.forEach((item: any) => {
        if (item.gl_code && !uniqueGlCodes.has(item.gl_code)) {
          uniqueGlCodes.add(item.gl_code);
          uniqueGlData.push({
            gl_code: item.gl_code,
            gl_name: item.gl_name || item.gl_code,
            device_code: item.device_code,
            device_name: item.device_name
          });
        }
      });

      console.log('去重后的光路数量:', uniqueGlData.length);
      console.log('去重后的光路编码:', Array.from(uniqueGlCodes));

      // 保存当前版本光路清单
      saveCurrentOpticalPaths(uniqueGlData);

      // 完成加载
      dataLoading.value = false;
    } else {
      console.warn('未找到设备对光路信息');
      message.warning('未找到设备对光路信息');
      dataLoading.value = false;
    }
  } catch (error) {
    console.error('加载设备对地图数据失败:', error);
    message.error('加载设备对地图数据失败');
    dataLoading.value = false;
  }
};

// 加载A环地图数据
const loadARingMapData = async () => {
  try {
    console.log('加载A环地图数据');

    // 优先使用路由参数中的b1Code和b2Code
    let b1Code = route.query.b1Code as string;
    let b2Code = route.query.b2Code as string;

    // 如果路由参数中没有b1Code和b2Code，则从protectionGroupId中提取
    if (!b1Code || !b2Code) {
      console.log('路由参数中没有b1Code和b2Code，从protectionGroupId中提取');
      [b1Code, b2Code] = protectionGroupId.value.split('_');
    }

    // 优先使用路由参数中的ringNum
    const ringNum = route.query.ringNum as string || route.query.ringNumber as string;

    console.log('A环地图数据参数:', {
      b1Code: b1Code,
      b2Code: b2Code,
      ringNum: ringNum,
      protectionGroupId: protectionGroupId.value
    });

    if (!b1Code || !b2Code) {
      console.error('无效的参数，无法获取b1_code和b2_code');
      message.error('无效的参数，无法获取B设备编码');
      dataLoading.value = false;
      return;
    }

    // 构建查询参数
    const params = {
      b1Code: b1Code,
      b2Code: b2Code,
      ringNum: ringNum,
      // 添加分页参数
      pageSize: 1000,
      currentPage: 1
    };

    console.log('A环设备详情查询参数:', params);
    console.log('使用API: SLY_A_RING_DEVICE_DETAIL_QUERY, 版本: V20250506090903494');

    // 开始加载数据
    dataLoading.value = true;

    // 调用API获取A环设备详情
    const response = await unifiedApi.callApi(
      'SLY_A_RING_DEVICE_DETAIL_QUERY',
      'V20250506090903494',
      params,
      defaultCityCode.value.toLowerCase()
    );

    console.log('A环设备详情API响应:', response);

    if (response && response.data && response.data.length > 0) {
      // 提取环内设备列表
      const deviceDataArray = response.data;
      console.log('A环设备列表数量:', deviceDataArray.length);

      // 从环内设备列表中提取光路信息
      const uniqueGlCodes = new Set<string>();
      const uniqueGlData: any[] = [];

      // 收集所有不重复的光路编码
      deviceDataArray.forEach((device: any) => {
        if (device.gl_code && !uniqueGlCodes.has(device.gl_code)) {
          uniqueGlCodes.add(device.gl_code);
          uniqueGlData.push({
            gl_code: device.gl_code,
            gl_name: device.gl_name || device.gl_code,
            device_code: device.device_code,
            device_name: device.device_name,
            // 保存原始API响应数据，确保包含road_both_point_devices_xy等字段
            raw_data: device
          });
        }
      });

      console.log('从A环设备列表中提取的光路数量:', uniqueGlData.length);
      console.log('从A环设备列表中提取的光路编码:', Array.from(uniqueGlCodes));

      if (uniqueGlData.length === 0) {
        console.warn('从A环设备列表中未提取到光路信息');
        message.warning('从A环设备列表中未提取到光路信息');

        // 尝试使用备选方法获取光路信息 - 使用SLY_TRIPLE_PAIR_GL_INFO_QUERY接口
        await tryAlternativeMethodForARing(b1Code, b2Code);
      } else {
        // 保存当前版本光路清单
        saveCurrentOpticalPaths(uniqueGlData);
      }

      // 完成加载
      dataLoading.value = false;
    } else {
      console.warn('未找到A环设备详情数据');
      message.warning('请先完善A环信息，否则无法绘制光路图');


      // 尝试使用备选方法获取光路信息
      await tryAlternativeMethodForARing(b1Code, b2Code);

      dataLoading.value = false;
    }
  } catch (error) {
    console.error('加载A环地图数据失败:', error);
    console.error('错误详情:', error.stack);
    message.error('加载A环地图数据失败');
    dataLoading.value = false;
  }
};

// A环场景的备选光路获取方法
const tryAlternativeMethodForARing = async (b1Code: string, b2Code: string) => {
  try {
    console.log('尝试使用备选方法获取A环光路信息');

    // 构建查询参数
    const params = {
      b_pair: `${b1Code}_${b2Code}`
    };

    console.log('备选方法查询参数:', params);
    console.log('使用API: SLY_TRIPLE_PAIR_GL_INFO_QUERY, 版本: V20250520150541560');

    // 调用API获取设备对光路信息
    const glResponse = await unifiedApi.callApi(
      'SLY_TRIPLE_PAIR_GL_INFO_QUERY',
      'V20250520150541560',
      params,
      defaultCityCode.value.toLowerCase()
    );

    console.log('备选方法API响应:', glResponse);

    if (glResponse && glResponse.data && glResponse.data.length > 0) {
      console.log('找到设备对光路信息，数量:', glResponse.data.length);

      // 保存当前版本光路清单
      saveCurrentOpticalPaths(glResponse.data);
    } else {
      console.warn('备选方法未找到光路信息');
      message.warning('请先完善A环信息，否则无法绘制光路图');
    }
  } catch (error) {
    console.error('备选方法获取光路信息失败:', error);
    message.error('备选方法获取光路信息失败');
  }
};

// 处理地图数据 - 这个函数不再需要，由OpticalPathMap组件处理
const processMapData = (data: any[]) => {
  console.log('processMapData函数已被OpticalPathMap组件替代');
  return { pipeSegments: [], devices: [], opticalPaths: {} };
};

// 获取随机颜色 - 这个函数不再需要，由OpticalPathMap组件处理
const getRandomColor = (seed: string) => {
  console.log('getRandomColor函数已被OpticalPathMap组件替代');
  return '#1890ff';
};

// 检查和修复管线段坐标 - 这个函数不再需要，由OpticalPathMap组件处理
const fixPipeSegmentsCoordinates = (pipeSegments: any[]) => {
  console.log('fixPipeSegmentsCoordinates函数已被OpticalPathMap组件替代');
  return pipeSegments || [];
};

// 绘制地图数据 - 这个函数不再需要，我们使用OpticalPathMap组件来绘制地图
// 保留这个函数是为了兼容旧代码，但它不会被调用
const drawMapData = (mapContainer: any, mapData: any) => {
  console.log('drawMapData函数已被OpticalPathMap组件替代，不再需要手动绘制地图');
  console.log('地图数据:', mapData);
};

// 加载历史版本数据
const loadHistoryVersionData = async (versionId: string) => {
  try {
    console.log('%c加载历史版本数据:', 'background: #222; color: #bada55; font-size: 16px;', versionId);
    dataLoading.value = true; // 数据加载状态
    historyMapLoading.value = true; // 历史版本地图加载状态

    // 构建查询参数
    const params = {
      id: versionId
    };

    console.log('%c历史版本详情查询参数:', 'background: #222; color: #bada55; font-size: 16px;', JSON.stringify(params, null, 2));
    console.log('%c保护组类型:', 'background: #222; color: #bada55; font-size: 16px;', protectionGroupType.value);
    console.log('%c场景类型:', 'background: #222; color: #bada55; font-size: 16px;', scenarioType.value);
    console.log('%c保护组ID:', 'background: #222; color: #bada55; font-size: 16px;', protectionGroupId.value);

    // 调用API获取历史版本数据
    const response = await unifiedApi.callApi(
      'SLY_MAP_HISTORY_DETAIL_QUERY',
      'V20250520104231091',
      params,
      defaultCityCode.value.toLowerCase()
    );

    console.log('%c历史版本数据API响应:', 'background: #222; color: #bada55; font-size: 16px;', response);

    // 如果没有数据，输出更详细的信息
    if (!response || !response.data) {
      console.log('%c未找到历史版本详情数据，检查查询参数:', 'background: #ff5555; color: white; font-size: 16px;');
      console.log('%c版本ID:', 'background: #ff5555; color: white; font-size: 16px;', versionId);
      console.log('%c保护组类型:', 'background: #ff5555; color: white; font-size: 16px;', protectionGroupType.value);
      console.log('%c场景类型:', 'background: #ff5555; color: white; font-size: 16px;', scenarioType.value);
    }

    if (response && response.data) {
      console.log('%c历史版本数据详情:', 'background: #222; color: #bada55; font-size: 16px;', response.data);

      // 检查geojson_data是否存在
      if (response.data.geojson_data) {
        console.log('%c历史版本包含geojson数据', 'background: #222; color: #bada55; font-size: 16px;');

        // 检查geojson_data的类型
        const dataType = typeof response.data.geojson_data;
        console.log('%cgeojson_data类型:', 'background: #222; color: #bada55; font-size: 16px;', dataType);

        // 如果是字符串，尝试输出长度
        if (dataType === 'string') {
          console.log('%cgeojson_data字符串长度:', 'background: #222; color: #bada55; font-size: 16px;', response.data.geojson_data.length);

          try {
            // 解析 JSON 字符串
            const parsedData = JSON.parse(response.data.geojson_data);
            historyVersionGeojson.value = parsedData;
            console.log('%c成功解析geojson数据:', 'background: #222; color: #bada55; font-size: 16px;', parsedData);

            // 检查是否包含光路数据
            if (parsedData.opticalPaths) {
              console.log('%c光路数量:', 'background: #222; color: #bada55; font-size: 16px;', Object.keys(parsedData.opticalPaths).length);

              // 检查是否有重叠部分
              let hasOverlapping = false;

              // 遍历所有管道段，检查是否有多条光路共用同一个管道段
              if (parsedData.pipeSegments && parsedData.pipeSegments.length > 0) {
                for (const segment of parsedData.pipeSegments) {
                  if (segment.opt_road_list && segment.opt_road_list.length > 1) {
                    hasOverlapping = true;
                    console.log('%c发现重叠的管道段:', 'background: #222; color: #bada55; font-size: 16px;',
                      segment.opt_road_list.map((p: any) => p.code).join(', '));
                    break;
                  }
                }
              }

              console.log('%c是否有重叠部分:', 'background: #222; color: #bada55; font-size: 16px;', hasOverlapping);
            }
          } catch (parseError) {
            console.error('解析geojson数据失败:', parseError);
            message.error('解析历史版本地图数据失败');
          }
        } else if (dataType === 'object') {
          // 直接使用对象
          historyVersionGeojson.value = response.data.geojson_data;
          console.log('%c直接使用geojson对象数据', 'background: #222; color: #bada55; font-size: 16px;');
        }
      } else {
        console.log('%c历史版本不包含geojson数据', 'background: #ff5555; color: white; font-size: 16px;');
        historyVersionGeojson.value = null;
        message.warning('历史版本不包含地图数据');
      }

      // 加载历史版本光路清单
      loadHistoryOpticalPaths(versionId);
    } else {
      console.warn('%c未找到历史版本数据', 'background: #ff5555; color: white; font-size: 16px;');
      message.warning('未找到历史版本数据');
      historyVersionGeojson.value = null;
    }

    dataLoading.value = false; // 完成后关闭数据加载状态

    // 延迟关闭地图加载状态，给地图渲染一些时间
    setTimeout(() => {
      historyMapLoading.value = false;
    }, 500);
  } catch (error) {
    console.error('加载历史版本数据失败:', error);
    message.error('加载历史版本数据失败');
    dataLoading.value = false; // 出错时也关闭加载状态
    historyMapLoading.value = false; // 出错时也关闭地图加载状态
    historyVersionGeojson.value = null;
  }
};

// 保存当前版本光路清单
const saveCurrentOpticalPaths = (data: any[]) => {
  try {
    console.log('保存当前版本光路清单:', data);

    // 提取光路信息
    const paths: any[] = [];
    const pathMap = new Map<string, any>();

    data.forEach(item => {
      if (item.gl_code && !pathMap.has(item.gl_code)) {
        pathMap.set(item.gl_code, {
          code: item.gl_code,
          name: item.gl_name || item.gl_code
        });
      }
    });

    // 转换为数组
    pathMap.forEach(path => {
      paths.push(path);
    });

    // 保存光路清单
    currentOpticalPaths.value = paths;
    console.log('当前版本光路清单:', currentOpticalPaths.value);

    // 创建带颜色的光路对象 - 使用更鲜艳的颜色
    const colors = [
      '#1890ff', // 蓝色
      '#f5222d', // 红色
      '#52c41a', // 绿色
      '#faad14', // 黄色
      '#722ed1', // 紫色
      '#eb2f96', // 粉色
      '#fa541c', // 橙色
      '#13c2c2'  // 青色
    ];

    // 创建光路对象列表，确保每条光路使用不同颜色
    const opticalPathObjects = paths.map((path, index) => ({
      code: path.code,
      color: colors[index % colors.length],
      name: path.name
    }));

    // 保存
    currentOpticalPathObjects.value = opticalPathObjects;
    console.log('创建当前版本光路对象:', opticalPathObjects.length);
  } catch (error) {
    console.error('保存当前版本光路清单失败:', error);
  }
};

// 处理当前地图初始化完成
const handleCurrentMapInitialized = (mapContainer: any) => {
  console.log('当前版本地图初始化完成:', mapContainer);
  currentMapContainer.value = mapContainer;
};

// 处理当前地图数据加载完成
const handleCurrentMapDataLoaded = (data: any) => {
  console.log('当前版本地图数据加载完成:', data);
  message.success('当前版本地图数据加载完成');
};

// 处理历史地图初始化完成
const handleHistoryMapInitialized = (mapContainer: any) => {
  console.log('历史版本地图初始化完成:', mapContainer);
  historyMapContainer.value = mapContainer;
};

// 处理历史地图数据加载完成
const handleHistoryMapDataLoaded = (data: any) => {
  console.log('历史版本地图数据加载完成:', data);

  // 检查是否有重叠部分
  if (data && data.hasOverlappingPaths) {
    console.log('%c历史版本地图中存在重叠部分', 'background: #222; color: #bada55; font-size: 16px;');

    // 统计重叠的管道段数量
    if (data.pipeSegments && data.pipeSegments.length > 0) {
      let overlappingCount = 0;
      for (const segment of data.pipeSegments) {
        if (segment.opt_road_list && segment.opt_road_list.length > 1) {
          overlappingCount++;
        }
      }
      console.log(`%c发现 ${overlappingCount} 个重叠的管道段`, 'background: #222; color: #bada55; font-size: 16px;');
    }
  } else {
    console.log('%c历史版本地图中没有重叠部分', 'background: #222; color: #bada55; font-size: 16px;');
  }

  message.success('历史版本地图数据加载完成');
};

// 处理地图错误
const handleMapError = (error: string) => {
  console.error('地图组件错误:', error);
  message.error(error);
};

// 加载历史版本光路清单
const loadHistoryOpticalPaths = async (versionId: string) => {
  try {
    console.log('加载历史版本光路清单:', versionId);

    // 构建查询参数
    const params = {
      id: versionId
    };

    // 调用API获取历史版本数据
    const response = await unifiedApi.callApi(
      'SLY_MAP_HISTORY_DETAIL_QUERY',
      'V20250520104231091',
      params,
      defaultCityCode.value.toLowerCase()
    );

    if (response && response.data && response.data.geojson_data) {
      console.log('%c历史版本数据:', 'background: #222; color: #bada55; font-size: 16px;', response.data);
      console.log('%c历史版本ID:', 'background: #222; color: #bada55; font-size: 16px;', response.data.id);
      console.log('%c历史版本创建时间:', 'background: #222; color: #bada55; font-size: 16px;', response.data.create_time);

      // 解析geojson_data字段
      let mapData: any;
      try {
        // 如果geojson_data已经是对象，直接使用
        if (typeof response.data.geojson_data === 'object') {
          console.log('%cgeojson_data 是对象类型', 'background: #222; color: #bada55; font-size: 16px;');
          mapData = response.data.geojson_data;
        } else {
          // 否则尝试解析JSON字符串
          console.log('%c尝试解析 geojson_data JSON字符串', 'background: #222; color: #bada55; font-size: 16px;');
          mapData = JSON.parse(response.data.geojson_data);
        }

        console.log('%cgeojson_data 解析结果:', 'background: #222; color: #bada55; font-size: 16px;', mapData);

        // 检查并输出管道段和设备数据
        if (mapData.pipeSegments) {
          console.log('%c管道段数量:', 'background: #222; color: #bada55; font-size: 16px;', mapData.pipeSegments.length);
        }

        if (mapData.devices) {
          console.log('%c设备数量:', 'background: #222; color: #bada55; font-size: 16px;', mapData.devices.length);
        }

        // 提取光路信息
        const paths: any[] = [];
        if (mapData.opticalPaths) {
          console.log('%c光路数量:', 'background: #222; color: #bada55; font-size: 16px;', Object.keys(mapData.opticalPaths).length);
          console.log('%c光路数据结构:', 'background: #222; color: #bada55; font-size: 16px;', mapData.opticalPaths);

          // 遍历所有光路
          Object.keys(mapData.opticalPaths).forEach(key => {
            const path = mapData.opticalPaths[key];
            console.log(`%c处理光路 ${key}:`, 'background: #222; color: #bada55; font-size: 16px;', path);

            // 使用光路编码作为 code，优先使用 gl_code 字段，其次使用 key
            const code = path.gl_code || key;
            // 优先使用 gl_name 字段，其次使用 name 字段，最后使用 code
            const name = path.gl_name || path.name || code;

            console.log(`%c提取的光路信息: code=${code}, name=${name}`, 'background: #222; color: #bada55; font-size: 16px;');

            paths.push({
              code: code,
              name: name
            });
          });
        }

        // 保存光路清单
        historyOpticalPaths.value = paths;
        console.log('%c历史版本光路清单:', 'background: #222; color: #bada55; font-size: 16px;', historyOpticalPaths.value);

        // 创建带颜色的光路对象 - 使用更鲜艳的颜色
        const colors = [
          '#1890ff', // 蓝色
          '#f5222d', // 红色
          '#52c41a', // 绿色
          '#faad14', // 黄色
          '#722ed1', // 紫色
          '#eb2f96', // 粉色
          '#fa541c', // 橙色
          '#13c2c2'  // 青色
        ];

        // 创建光路对象列表，确保每条光路使用不同颜色
        const opticalPathObjects = paths.map((path, index) => ({
          code: path.code,
          color: colors[index % colors.length],
          name: path.name
        }));

        // 保存
        historyOpticalPathObjects.value = opticalPathObjects;
        console.log('创建历史版本光路对象:', opticalPathObjects.length);

        // 检查是否有重叠部分
        if (mapData.pipeSegments && mapData.pipeSegments.length > 0) {
          let hasOverlapping = false;
          let overlappingCount = 0;

          for (const segment of mapData.pipeSegments) {
            if (segment.opt_road_list && segment.opt_road_list.length > 1) {
              hasOverlapping = true;
              overlappingCount++;
              console.log(`%c发现重叠的管道段，包含光路: ${segment.opt_road_list.map((p: any) => p.code).join(', ')}`, 'background: #222; color: #bada55; font-size: 16px;');
            }
          }

          console.log(`%c是否有重叠部分: ${hasOverlapping}, 重叠管道段数量: ${overlappingCount}`, 'background: #222; color: #bada55; font-size: 16px;');
        }
      } catch (error) {
        console.error('解析历史版本光路清单失败:', error);
        message.error('解析历史版本光路清单失败');
      }
    } else {
      console.warn('未找到历史版本数据');
      historyOpticalPaths.value = [];
    }
  } catch (error) {
    console.error('加载历史版本光路清单失败:', error);
    message.error('加载历史版本光路清单失败');
  }
};

// 清除所有高亮
const clearHighlights = (type: 'current' | 'history') => {
  try {
    console.log('清除所有高亮:', type);

    // 根据类型选择地图组件
    const mapRef = type === 'current' ? currentMapRef.value : historyMapRef.value;

    if (!mapRef) {
      console.error('地图组件不存在，无法清除高亮');
      return;
    }

    // 调用地图组件的清除高亮方法
    mapRef.clearHighlights();
  } catch (error) {
    console.error('清除所有高亮失败:', error);
  }
};



// 解析历史版本地图数据 - 这个函数不再需要，由OpticalPathMap组件处理
const parseHistoryMapData = (data: any) => {
  console.log('parseHistoryMapData函数已被OpticalPathMap组件替代');
  return {
    pipeSegments: [],
    devices: [],
    opticalPaths: {}
  };
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 加载保护组信息
const loadProtectionGroupInfo = async () => {
  loading.value = true;

  try {
    // 根据保护组类型加载不同的信息
    if (protectionGroupType.value === 'ringNetwork') {
      await loadARingDeviceInfo();
    } else if (protectionGroupType.value === 'dualDeviceDualRoute') {
      await loadDevicePairDualRouteInfo();
    } else if (protectionGroupType.value === 'dualDeviceTripleRoute') {
      await loadDevicePairTripleRouteInfo();
    } else if (protectionGroupType.value === 'singleDeviceDualUplink') {
      await loadSingleDeviceInfo();
    }
  } catch (error) {
    console.error('加载保护组信息失败:', error);
    message.error('加载保护组信息失败');
  } finally {
    loading.value = false;
  }
};

// 加载A环设备信息
const loadARingDeviceInfo = async () => {
  try {
    console.log('加载A环设备信息');

    // 优先使用路由参数中的b1Code和b2Code
    let b1Code = route.query.b1Code as string;
    let b2Code = route.query.b2Code as string;

    // 如果路由参数中没有b1Code和b2Code，则从protectionGroupId中提取
    if (!b1Code || !b2Code) {
      console.log('路由参数中没有b1Code和b2Code，从protectionGroupId中提取');
      [b1Code, b2Code] = protectionGroupId.value.split('_');
    }

    // 优先使用路由参数中的ringNum
    const ringNum = route.query.ringNum as string || route.query.ringNumber as string;

    console.log('A环设备信息参数:', {
      b1Code: b1Code,
      b2Code: b2Code,
      ringNum: ringNum,
      protectionGroupId: protectionGroupId.value
    });

    if (!b1Code || !b2Code) {
      console.error('无效的参数，无法获取b1_code和b2_code');
      message.error('无效的参数，无法获取B设备编码');
      return;
    }

    // 构建查询参数
    const params = {
      b1Code: b1Code,
      b2Code: b2Code,
      ringNum: ringNum,
      // 添加分页参数
      pageSize: 1000,
      currentPage: 1
    };

    console.log('A环设备详情查询参数:', params);

    // 调用API获取A环设备详情
    const response = await unifiedApi.callApi(
      'SLY_A_RING_DEVICE_DETAIL_QUERY',
      'V20250506090903494', // 使用与ARingDeviceDetail.vue相同的版本号
      params,
      defaultCityCode.value.toLowerCase()
    );

    console.log('A环设备详情API响应:', response);

    if (response && response.data && response.data.length > 0) {
      const deviceData = response.data[0];
      console.log('A环设备详情数据:', deviceData);

      // 更新保护组信息
      protectionGroupInfo.value = {
        ...protectionGroupInfo.value,
        lgcEqpName: deviceData.lgc_eqp_name,
        lgcEqpNo: deviceData.lgc_eqp_no,
        lgcEqpIp: deviceData.lgc_eqp_ip,
        adminIp: deviceData.admin_ip,
        b1Name: deviceData.b1_name,
        b1Code: deviceData.b1_code,
        b1Ip: deviceData.b1_ip,
        b2Name: deviceData.b2_name,
        b2Code: deviceData.b2_code,
        b2Ip: deviceData.b2_ip,
        ringNumber: ringNum || deviceData.ring_num, // 优先使用URL参数中的环号
        glCode: deviceData.gl_code,
        // 添加其他可能有用的字段
        eqpRes: deviceData.eqp_res,
        lgcEqpSpecNo: deviceData.lgc_eqp_spec_no
      };
    } else {
      console.warn('未找到A环设备详情数据');
      message.warning('请先完善A环信息，否则无法绘制光路图');

      // 即使没有找到数据，也设置一些基本信息
      protectionGroupInfo.value = {
        ...protectionGroupInfo.value,
        b1Code: b1Code,
        b2Code: b2Code,
        ringNumber: ringNum || '未知',
        area: route.query.areaName as string || '未知',
        city: route.query.cityName as string || '未知'
      };
    }
  } catch (error) {
    console.error('加载A环设备信息失败:', error);
    message.error('加载A环设备信息失败: ' + (error.message || '未知错误'));
    throw error;
  }
};

// 加载设备对双路由信息
const loadDevicePairDualRouteInfo = async () => {
  try {
    console.log('%c加载设备对双路由信息，设备对ID:', 'background: #222; color: #bada55; font-size: 16px;', protectionGroupId.value);

    // 首先尝试从路由参数中提取设备名称
    const protectionGroupName = route.query.protectionGroupName as string || '';
    let deviceAName = route.query.deviceAName as string || '';
    let deviceBName = route.query.deviceBName as string || '';

    // 如果路由参数中没有设备名称，尝试从保护组名称中提取
    if ((!deviceAName || !deviceBName) && protectionGroupName && protectionGroupName.includes('-')) {
      const deviceNames = protectionGroupName.split('-');
      if (deviceNames.length >= 2) {
        deviceAName = deviceAName || deviceNames[0].trim();
        deviceBName = deviceBName || deviceNames[1].trim();
      }
    }

    console.log('%c从路由参数提取的设备名称:', 'background: #222; color: #bada55; font-size: 16px;', {
      deviceAName,
      deviceBName,
      protectionGroupName
    });

    // 构建查询参数
    const params = {
      b_pair: protectionGroupId.value
    };

    console.log('%c设备对双路由信息查询参数:', 'background: #222; color: #bada55; font-size: 16px;', params);
    console.log('%c使用API: SLY_PAIR_DEVICE_INFO_QUERY, 版本: V20250423204627250', 'background: #222; color: #bada55; font-size: 16px;');
    console.log('%c城市代码:', 'background: #222; color: #bada55; font-size: 16px;', defaultCityCode.value.toLowerCase());

    // 调用API获取设备对基本信息
    const response = await unifiedApi.callApi(
      'SLY_PAIR_DEVICE_INFO_QUERY',
      'V20250423204627250',
      params,
      defaultCityCode.value.toLowerCase()
    );

    console.log('%c设备对双路由信息API响应:', 'background: #222; color: #bada55; font-size: 16px;', response);

    if (response && response.data && response.data.length > 0) {
      const pairData = response.data;
      console.log('%c设备对双路由信息数据:', 'background: #222; color: #bada55; font-size: 16px;', pairData);

      // 尝试多种方式提取设备A和设备B的信息
      let deviceA: any = {};
      let deviceB: any = {};

      // 方法1: 使用device_index字段
      if (pairData.some((d: any) => d.device_index !== undefined)) {
        deviceA = pairData.find((d: any) => d.device_index === 1) || {};
        deviceB = pairData.find((d: any) => d.device_index === 2) || {};
        console.log('%c使用device_index字段区分设备:', 'background: #222; color: #bada55; font-size: 16px;');
      }
      // 方法2: 如果有两条记录，假设第一条是设备A，第二条是设备B
      else if (pairData.length >= 2) {
        deviceA = pairData[0];
        deviceB = pairData[1];
        console.log('%c使用数组索引区分设备:', 'background: #222; color: #bada55; font-size: 16px;');
      }
      // 方法3: 如果只有一条记录，使用它作为设备A
      else if (pairData.length === 1) {
        deviceA = pairData[0];
        console.log('%c只有一条设备记录，使用它作为设备A:', 'background: #222; color: #bada55; font-size: 16px;');
      }

      console.log('%c设备A信息:', 'background: #222; color: #bada55; font-size: 16px;', deviceA);
      console.log('%c设备B信息:', 'background: #222; color: #bada55; font-size: 16px;', deviceB);

      // 更新保护组信息，优先使用API返回的数据，然后是路由参数，最后是默认值
      protectionGroupInfo.value = {
        ...protectionGroupInfo.value,
        devicePairId: protectionGroupId.value,
        deviceA: {
          device_id: deviceA.device_id || '',
          device_code: deviceA.device_code || '',
          device_name: deviceA.device_name || deviceAName || '未知',
          device_ip: deviceA.device_ip || '未知',
          device_type: deviceA.eqp_spec || deviceA.device_type || '未知'
        },
        deviceB: {
          device_id: deviceB.device_id || '',
          device_code: deviceB.device_code || '',
          device_name: deviceB.device_name || deviceBName || '未知',
          device_ip: deviceB.device_ip || '未知',
          device_type: deviceB.eqp_spec || deviceB.device_type || '未知'
        },
        area: `${deviceA.city_name || ''} ${deviceA.area_name || route.query.areaName || '未知'}`.trim(),
        discoveryTime: deviceA.create_date ? new Date(deviceA.create_date).toLocaleString() : '未知'
      };

      console.log('%c更新后的保护组信息:', 'background: #222; color: #bada55; font-size: 16px;', protectionGroupInfo.value);
    } else {
      console.warn('%c未找到设备对双路由信息数据，使用路由参数作为备选', 'background: #ff5555; color: white; font-size: 16px;');

      // 即使没有找到数据，也设置一些基本信息
      protectionGroupInfo.value = {
        ...protectionGroupInfo.value,
        devicePairId: protectionGroupId.value,
        deviceA: {
          device_id: '',
          device_code: '',
          device_name: deviceAName || '未知',
          device_ip: '未知',
          device_type: '未知'
        },
        deviceB: {
          device_id: '',
          device_code: '',
          device_name: deviceBName || '未知',
          device_ip: '未知',
          device_type: '未知'
        },
        area: route.query.areaName as string || '未知',
        discoveryTime: '未知'
      };

      console.log('%c使用备选信息更新保护组信息:', 'background: #ff5555; color: white; font-size: 16px;', protectionGroupInfo.value);
      message.warning('未找到设备对双路由信息数据，使用默认值');
    }

    // 尝试从保护组ID中提取更多信息
    if (protectionGroupId.value) {
      // 如果设备名称仍然是未知，尝试从保护组ID中提取
      if (protectionGroupInfo.value.deviceA.device_name === '未知' || protectionGroupInfo.value.deviceB.device_name === '未知') {
        const idParts = protectionGroupId.value.split('-');
        if (idParts.length >= 2) {
          // 假设保护组ID的格式是 "设备A-设备B"
          if (protectionGroupInfo.value.deviceA.device_name === '未知') {
            protectionGroupInfo.value.deviceA.device_name = idParts[0];
          }
          if (protectionGroupInfo.value.deviceB.device_name === '未知' && idParts.length > 1) {
            protectionGroupInfo.value.deviceB.device_name = idParts[idParts.length - 1];
          }
          console.log('%c从保护组ID提取的设备名称:', 'background: #222; color: #bada55; font-size: 16px;', {
            deviceA: protectionGroupInfo.value.deviceA.device_name,
            deviceB: protectionGroupInfo.value.deviceB.device_name
          });
        }
      }
    }
  } catch (error) {
    console.error('加载设备对双路由信息失败:', error);
    console.error('错误详情:', error.message);
    console.error('错误堆栈:', error.stack);
    message.error('加载设备对双路由信息失败: ' + (error.message || '未知错误'));

    // 出错时也设置一些基本信息
    const protectionGroupName = route.query.protectionGroupName as string || '';
    let deviceAName = route.query.deviceAName as string || '';
    let deviceBName = route.query.deviceBName as string || '';

    // 如果路由参数中没有设备名称，尝试从保护组名称中提取
    if ((!deviceAName || !deviceBName) && protectionGroupName && protectionGroupName.includes('-')) {
      const deviceNames = protectionGroupName.split('-');
      if (deviceNames.length >= 2) {
        deviceAName = deviceAName || deviceNames[0].trim();
        deviceBName = deviceBName || deviceNames[1].trim();
      }
    }

    protectionGroupInfo.value = {
      ...protectionGroupInfo.value,
      devicePairId: protectionGroupId.value,
      deviceA: {
        device_id: '',
        device_code: '',
        device_name: deviceAName || '未知',
        device_ip: '未知',
        device_type: '未知'
      },
      deviceB: {
        device_id: '',
        device_code: '',
        device_name: deviceBName || '未知',
        device_ip: '未知',
        device_type: '未知'
      },
      area: route.query.areaName as string || '未知',
      discoveryTime: '未知'
    };

    // 尝试从保护组ID中提取更多信息
    if (protectionGroupId.value) {
      // 如果设备名称仍然是未知，尝试从保护组ID中提取
      if (protectionGroupInfo.value.deviceA.device_name === '未知' || protectionGroupInfo.value.deviceB.device_name === '未知') {
        const idParts = protectionGroupId.value.split('-');
        if (idParts.length >= 2) {
          // 假设保护组ID的格式是 "设备A-设备B"
          if (protectionGroupInfo.value.deviceA.device_name === '未知') {
            protectionGroupInfo.value.deviceA.device_name = idParts[0];
          }
          if (protectionGroupInfo.value.deviceB.device_name === '未知' && idParts.length > 1) {
            protectionGroupInfo.value.deviceB.device_name = idParts[idParts.length - 1];
          }
        }
      }
    }

    console.log('%c出错时使用备选信息更新保护组信息:', 'background: #ff5555; color: white; font-size: 16px;', protectionGroupInfo.value);
  }
};

// 加载设备对三路由信息
const loadDevicePairTripleRouteInfo = async () => {
  try {
    console.log('%c加载设备对三路由信息，设备对ID:', 'background: #222; color: #bada55; font-size: 16px;', protectionGroupId.value);

    // 首先尝试从路由参数中提取设备名称
    const protectionGroupName = route.query.protectionGroupName as string || '';
    let deviceAName = route.query.deviceAName as string || '';
    let deviceBName = route.query.deviceBName as string || '';

    // 如果路由参数中没有设备名称，尝试从保护组名称中提取
    if ((!deviceAName || !deviceBName) && protectionGroupName && protectionGroupName.includes('-')) {
      const deviceNames = protectionGroupName.split('-');
      if (deviceNames.length >= 2) {
        deviceAName = deviceAName || deviceNames[0].trim();
        deviceBName = deviceBName || deviceNames[1].trim();
      }
    }

    console.log('%c从路由参数提取的设备名称:', 'background: #222; color: #bada55; font-size: 16px;', {
      deviceAName,
      deviceBName,
      protectionGroupName
    });

    // 构建查询参数
    const params = {
      b_pair: protectionGroupId.value
    };

    console.log('%c设备对三路由信息查询参数:', 'background: #222; color: #bada55; font-size: 16px;', params);
    console.log('%c使用API: SLY_TRIPLE_PAIR_DEVICE_INFO_QUERY, 版本: V20250428145107500', 'background: #222; color: #bada55; font-size: 16px;');
    console.log('%c城市代码:', 'background: #222; color: #bada55; font-size: 16px;', defaultCityCode.value.toLowerCase());

    // 调用API获取设备对基本信息
    const response = await unifiedApi.callApi(
      'SLY_TRIPLE_PAIR_DEVICE_INFO_QUERY',
      'V20250428145107500',
      params,
      defaultCityCode.value.toLowerCase()
    );

    console.log('%c设备对三路由信息API响应:', 'background: #222; color: #bada55; font-size: 16px;', response);

    if (response && response.data && response.data.length > 0) {
      const pairData = response.data;
      console.log('%c设备对三路由信息数据:', 'background: #222; color: #bada55; font-size: 16px;', pairData);

      // 尝试多种方式提取设备A和设备B的信息
      let deviceA: any = {};
      let deviceB: any = {};

      // 方法1: 使用device_index字段
      if (pairData.some((d: any) => d.device_index !== undefined)) {
        deviceA = pairData.find((d: any) => d.device_index === 1) || {};
        deviceB = pairData.find((d: any) => d.device_index === 2) || {};
        console.log('%c使用device_index字段区分设备:', 'background: #222; color: #bada55; font-size: 16px;');
      }
      // 方法2: 如果有两条记录，假设第一条是设备A，第二条是设备B
      else if (pairData.length >= 2) {
        deviceA = pairData[0];
        deviceB = pairData[1];
        console.log('%c使用数组索引区分设备:', 'background: #222; color: #bada55; font-size: 16px;');
      }
      // 方法3: 如果只有一条记录，使用它作为设备A
      else if (pairData.length === 1) {
        deviceA = pairData[0];
        console.log('%c只有一条设备记录，使用它作为设备A:', 'background: #222; color: #bada55; font-size: 16px;');
      }

      console.log('%c设备A信息:', 'background: #222; color: #bada55; font-size: 16px;', deviceA);
      console.log('%c设备B信息:', 'background: #222; color: #bada55; font-size: 16px;', deviceB);

      // 更新保护组信息，优先使用API返回的数据，然后是路由参数，最后是默认值
      protectionGroupInfo.value = {
        ...protectionGroupInfo.value,
        devicePairId: protectionGroupId.value,
        deviceA: {
          device_id: deviceA.device_id || '',
          device_code: deviceA.device_code || '',
          device_name: deviceA.device_name || deviceAName || '未知',
          device_ip: deviceA.device_ip || '未知',
          device_type: deviceA.eqp_spec || deviceA.device_type || '未知'
        },
        deviceB: {
          device_id: deviceB.device_id || '',
          device_code: deviceB.device_code || '',
          device_name: deviceB.device_name || deviceBName || '未知',
          device_ip: deviceB.device_ip || '未知',
          device_type: deviceB.eqp_spec || deviceB.device_type || '未知'
        },
        area: `${deviceA.city_name || ''} ${deviceA.area_name || route.query.areaName || '未知'}`.trim(),
        discoveryTime: deviceA.create_date ? new Date(deviceA.create_date).toLocaleString() : '未知'
      };

      console.log('%c更新后的保护组信息:', 'background: #222; color: #bada55; font-size: 16px;', protectionGroupInfo.value);
    } else {
      console.warn('%c未找到设备对三路由信息数据，使用路由参数作为备选', 'background: #ff5555; color: white; font-size: 16px;');

      // 即使没有找到数据，也设置一些基本信息
      protectionGroupInfo.value = {
        ...protectionGroupInfo.value,
        devicePairId: protectionGroupId.value,
        deviceA: {
          device_id: '',
          device_code: '',
          device_name: deviceAName || '未知',
          device_ip: '未知',
          device_type: '未知'
        },
        deviceB: {
          device_id: '',
          device_code: '',
          device_name: deviceBName || '未知',
          device_ip: '未知',
          device_type: '未知'
        },
        area: route.query.areaName as string || '未知',
        discoveryTime: '未知'
      };

      console.log('%c使用备选信息更新保护组信息:', 'background: #ff5555; color: white; font-size: 16px;', protectionGroupInfo.value);
      message.warning('未找到设备对三路由信息数据，使用默认值');
    }

    // 尝试从保护组ID中提取更多信息
    if (protectionGroupId.value) {
      // 如果设备名称仍然是未知，尝试从保护组ID中提取
      if (protectionGroupInfo.value.deviceA.device_name === '未知' || protectionGroupInfo.value.deviceB.device_name === '未知') {
        const idParts = protectionGroupId.value.split('-');
        if (idParts.length >= 2) {
          // 假设保护组ID的格式是 "设备A-设备B"
          if (protectionGroupInfo.value.deviceA.device_name === '未知') {
            protectionGroupInfo.value.deviceA.device_name = idParts[0];
          }
          if (protectionGroupInfo.value.deviceB.device_name === '未知' && idParts.length > 1) {
            protectionGroupInfo.value.deviceB.device_name = idParts[idParts.length - 1];
          }
          console.log('%c从保护组ID提取的设备名称:', 'background: #222; color: #bada55; font-size: 16px;', {
            deviceA: protectionGroupInfo.value.deviceA.device_name,
            deviceB: protectionGroupInfo.value.deviceB.device_name
          });
        }
      }
    }
  } catch (error) {
    console.error('加载设备对三路由信息失败:', error);
    console.error('错误详情:', error.message);
    console.error('错误堆栈:', error.stack);
    message.error('加载设备对三路由信息失败: ' + (error.message || '未知错误'));

    // 出错时也设置一些基本信息
    const protectionGroupName = route.query.protectionGroupName as string || '';
    let deviceAName = route.query.deviceAName as string || '';
    let deviceBName = route.query.deviceBName as string || '';

    // 如果路由参数中没有设备名称，尝试从保护组名称中提取
    if ((!deviceAName || !deviceBName) && protectionGroupName && protectionGroupName.includes('-')) {
      const deviceNames = protectionGroupName.split('-');
      if (deviceNames.length >= 2) {
        deviceAName = deviceAName || deviceNames[0].trim();
        deviceBName = deviceBName || deviceNames[1].trim();
      }
    }

    protectionGroupInfo.value = {
      ...protectionGroupInfo.value,
      devicePairId: protectionGroupId.value,
      deviceA: {
        device_id: '',
        device_code: '',
        device_name: deviceAName || '未知',
        device_ip: '未知',
        device_type: '未知'
      },
      deviceB: {
        device_id: '',
        device_code: '',
        device_name: deviceBName || '未知',
        device_ip: '未知',
        device_type: '未知'
      },
      area: route.query.areaName as string || '未知',
      discoveryTime: '未知'
    };

    // 尝试从保护组ID中提取更多信息
    if (protectionGroupId.value) {
      // 如果设备名称仍然是未知，尝试从保护组ID中提取
      if (protectionGroupInfo.value.deviceA.device_name === '未知' || protectionGroupInfo.value.deviceB.device_name === '未知') {
        const idParts = protectionGroupId.value.split('-');
        if (idParts.length >= 2) {
          // 假设保护组ID的格式是 "设备A-设备B"
          if (protectionGroupInfo.value.deviceA.device_name === '未知') {
            protectionGroupInfo.value.deviceA.device_name = idParts[0];
          }
          if (protectionGroupInfo.value.deviceB.device_name === '未知' && idParts.length > 1) {
            protectionGroupInfo.value.deviceB.device_name = idParts[idParts.length - 1];
          }
        }
      }
    }

    console.log('%c出错时使用备选信息更新保护组信息:', 'background: #ff5555; color: white; font-size: 16px;', protectionGroupInfo.value);
  }
};

// 加载单设备信息
const loadSingleDeviceInfo = async () => {
  try {
    // 构建查询参数
    const params = {
      deviceCode: protectionGroupId.value
    };

    console.log('单设备基本信息查询参数:', params);

    // 调用API获取设备基本信息
    const response = await unifiedApi.callApi(
      'SLY_DEVICE_BASE_INFO_QUERY', // 正确的API编码
      'V20250419121012578', // 正确的版本号
      params,
      defaultCityCode.value.toLowerCase()
    );

    console.log('单设备基本信息API响应:', response);

    if (response && response.data && response.data.length > 0) {
      const deviceData = response.data[0]; // 获取第一条记录
      console.log('单设备基本信息数据:', deviceData);

      // 设置默认问题类型
      const problemType = '同管道'; // 默认问题类型，后续可以通过API获取

      // 更新保护组信息
      protectionGroupInfo.value = {
        ...protectionGroupInfo.value,
        deviceName: deviceData.device_name || route.query.protectionGroupName,
        deviceCode: deviceData.device_code || protectionGroupId.value,
        deviceType: deviceData.device_type || 'OLT设备',
        ipAddress: deviceData.device_ip || '未知',
        area: deviceData.area_name || route.query.areaName || '未知',
        problemType: problemType,
        discoveryTime: deviceData.create_time ? new Date(deviceData.create_time).toLocaleString() : '未知'
      };
    } else {
      console.warn('未找到单设备基本信息数据');

      // 即使没有找到数据，也设置一些基本信息
      protectionGroupInfo.value = {
        ...protectionGroupInfo.value,
        deviceName: route.query.protectionGroupName || '未知',
        deviceCode: protectionGroupId.value,
        deviceType: 'OLT设备',
        ipAddress: '未知',
        area: route.query.areaName || '未知',
        problemType: '同管道',
        discoveryTime: '未知'
      };

      message.warning('未找到单设备基本信息数据，使用默认值');
    }
  } catch (error) {
    console.error('加载单设备信息失败:', error);
    message.error('加载单设备信息失败: ' + (error.message || '未知错误'));
    throw error;
  }
};



// 加载历史版本列表
const loadHistoryVersions = async () => {
  try {
    // 构建查询参数
    const params: Record<string, any> = {
      // 添加分页参数
      pageSize: 100,
      currentPage: 1,
      scenarioType: scenarioType.value
    };

    // 根据保护组类型添加特定参数
    if (protectionGroupType.value === 'ringNetwork') {
      // 优先使用路由参数中的b1Code和b2Code
      let b1Code = route.query.b1Code as string;
      let b2Code = route.query.b2Code as string;

      // 如果路由参数中没有b1Code和b2Code，则从protectionGroupId中提取
      if (!b1Code || !b2Code) {
        console.log('路由参数中没有b1Code和b2Code，从protectionGroupId中提取');
        [b1Code, b2Code] = protectionGroupId.value.split('_');
      }

      // 优先使用路由参数中的ringNum
      const ringNum = route.query.ringNum as string || route.query.ringNumber as string;

      // 记录详细日志
      console.log('A环历史版本查询参数 - 解析参数:', {
        protectionGroupId: protectionGroupId.value,
        b1Code: b1Code,
        b2Code: b2Code,
        ringNum: ringNum
      });

      // 设置查询参数
      params.b1Code = b1Code;
      params.b2Code = b2Code;
      params.ringNumber = ringNum;
      params.protectionGroupId = protectionGroupId.value;

      // 记录最终使用的参数
      console.log('A环历史版本最终查询参数:', {
        b1Code: params.b1Code,
        b2Code: params.b2Code,
        ringNumber: params.ringNumber,
        protectionGroupId: params.protectionGroupId
      });
    } else if (protectionGroupType.value === 'dualDeviceDualRoute' || protectionGroupType.value === 'dualDeviceTripleRoute') {
      // 设备对场景使用devicePairId和protectionGroupId两个参数
      params.devicePairId = protectionGroupId.value;
      params.protectionGroupId = protectionGroupId.value;
      console.log('设备对场景查询参数:', { devicePairId: params.devicePairId, protectionGroupId: params.protectionGroupId });
    } else if (protectionGroupType.value === 'singleDeviceDualUplink') {
      params.deviceCode = protectionGroupId.value;
      params.protectionGroupId = protectionGroupId.value;
    } else {
      // 默认情况下也添加protectionGroupId
      params.protectionGroupId = protectionGroupId.value;
    }

    // 添加城市代码
    params.cityCode = defaultCityCode.value.toLowerCase(); // 确保使用小写
    params.areaCode = areaCode.toLowerCase();

    console.log('%c历史版本列表查询参数:', 'background: #222; color: #bada55; font-size: 16px;', JSON.stringify(params, null, 2));
    console.log('%c保护组类型:', 'background: #222; color: #bada55; font-size: 16px;', protectionGroupType.value);
    console.log('%c场景类型:', 'background: #222; color: #bada55; font-size: 16px;', scenarioType.value);
    console.log('%c保护组ID:', 'background: #222; color: #bada55; font-size: 16px;', protectionGroupId.value);

    // 调用API获取历史版本列表
    const response = await unifiedApi.callApi(
      'SLY_MAP_HISTORY_QUERY',
      'V20250516110111700',
      params,
      defaultCityCode.value.toLowerCase()
    );

    console.log('%c历史版本列表API响应:', 'background: #222; color: #bada55; font-size: 16px;', response);

    // 如果没有数据，输出更详细的信息
    if (!response || !response.data || response.data.length === 0) {
      console.log('%c未找到历史版本数据，检查查询参数:', 'background: #ff5555; color: white; font-size: 16px;');
      console.log('%c设备对ID:', 'background: #ff5555; color: white; font-size: 16px;', params.devicePairId);
      console.log('%c保护组ID:', 'background: #ff5555; color: white; font-size: 16px;', params.protectionGroupId);
      console.log('%c场景类型:', 'background: #ff5555; color: white; font-size: 16px;', params.scenarioType);
      console.log('%c城市代码:', 'background: #ff5555; color: white; font-size: 16px;', params.cityCode);
      console.log('%c区域代码:', 'background: #ff5555; color: white; font-size: 16px;', params.areaCode);
    }

    if (response && response.data && response.data.length > 0) {
      // 添加历史版本到版本列表
      const historyVersions = response.data.map((version: any) => ({
        id: version.id,
        name: version.description || `版本 ${version.id}`,
        date: version.create_time,
        creator: version.creator,
        description: version.description || `版本 ${version.id} 快照`
      }));

      console.log('%c找到历史版本数量: ' + response.data.length, 'background: #222; color: #bada55; font-size: 16px;');
      console.log('%c历史版本列表:', 'background: #222; color: #bada55; font-size: 16px;', historyVersions);

      // 确保当前版本始终在列表中
      versionList.value = [
        { id: 'current', name: '当前版本', date: new Date().toISOString(), description: '当前工作版本' },
        ...historyVersions
      ];

      // 如果有历史版本，自动选择最新的一个
      if (historyVersions.length > 0) {
        console.log('%c自动选择最新的历史版本:', 'background: #222; color: #bada55; font-size: 16px;', historyVersions[0].id);
        selectedVersion.value = historyVersions[0].id;

        // 显示加载状态
        historyMapLoading.value = true;

        // 延迟加载历史版本数据，确保地图已初始化
        setTimeout(async () => {
          // 加载该版本的数据
          await loadHistoryVersionData(selectedVersion.value);
          // 加载历史版本光路清单
          await loadHistoryOpticalPaths(selectedVersion.value);
        }, 1000);
      }
    } else {
      console.log('未找到历史版本数据');
    }
  } catch (error) {
    console.error('加载历史版本列表失败:', error);
    message.error('加载历史版本列表失败: ' + (error.message || '未知错误'));
  }
};

// 显示版本右键菜单
const showVersionContextMenu = (event: MouseEvent, version: any) => {
  // 只有历史版本可以右键操作
  if (version.id === 'current') {
    return;
  }

  // 设置右键菜单位置
  contextMenuStyle.value = {
    position: 'fixed',
    top: `${event.clientY}px`,
    left: `${event.clientX}px`,
    display: 'block'
  };

  // 设置当前操作的版本
  contextMenuVersion.value = version;

  // 显示右键菜单
  contextMenuVisible.value = true;
};

// 处理右键菜单可见性变化
const handleContextMenuVisibleChange = (visible: boolean) => {
  if (!visible) {
    contextMenuVisible.value = false;
  }
};

// 编辑版本描述
const editVersionDescription = () => {
  if (!contextMenuVersion.value) {
    return;
  }

  // 设置表单数据
  editDescriptionForm.value = {
    id: contextMenuVersion.value.id,
    description: contextMenuVersion.value.description || ''
  };

  // 显示编辑对话框
  editDescriptionModalVisible.value = true;

  // 隐藏右键菜单
  contextMenuVisible.value = false;
};

// 处理编辑描述确认
const handleEditDescriptionOk = async () => {
  try {
    const versionId = editDescriptionForm.value.id;
    const description = editDescriptionForm.value.description;

    console.log('更新版本描述:', versionId, description);

    // 构建更新参数
    const params = {
      id: versionId,
      description: description
    };

    // 调用API更新版本描述
    const response = await unifiedApi.callApi(
      'SLY_MAP_HISTORY_UPDATE',
      'V20250520092435280',
      params,
      defaultCityCode.value.toLowerCase()
    );

    console.log('更新版本描述API响应:', response);

    if (response && (response.code === '0' || response.success)) {
      message.success('更新版本描述成功');

      // 更新本地版本列表中的描述
      const version = versionList.value.find(v => v.id === versionId);
      if (version) {
        version.description = description;
        version.name = description || `版本 ${versionId}`;
      }

      // 如果当前选中的是被修改的版本，重新加载该版本的数据
      if (selectedVersion.value === versionId) {
        console.log('重新加载修改后的版本数据');
        // 显示加载状态
        historyMapLoading.value = true;

        // 重新加载历史版本数据
        await loadHistoryVersionData(versionId);
        // 重新加载历史版本光路清单
        await loadHistoryOpticalPaths(versionId);
      }
    } else {
      message.error('更新版本描述失败: ' + (response?.message || '未知错误'));
    }

    // 关闭对话框
    editDescriptionModalVisible.value = false;
  } catch (error) {
    console.error('更新版本描述出错:', error);
    message.error('更新版本描述出错: ' + (error.message || '未知错误'));
  }
};

// 处理编辑描述取消
const handleEditDescriptionCancel = () => {
  editDescriptionModalVisible.value = false;
};

// 删除版本
const deleteVersion = async () => {
  if (!contextMenuVersion.value) {
    return;
  }

  const versionId = contextMenuVersion.value.id;
  const versionName = contextMenuVersion.value.name;

  // 隐藏右键菜单
  contextMenuVisible.value = false;

  // 确认删除
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除版本 "${versionName}" 吗？此操作不可恢复。`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        console.log('删除版本:', versionId);

        // 构建删除参数
        const params = {
          id: versionId
        };

        // 调用API删除版本
        const response = await unifiedApi.callApi(
          'SLY_MAP_HISTORY_DELETE',
          'V20250516111033990',
          params,
          defaultCityCode.value.toLowerCase()
        );

        console.log('删除版本API响应:', response);

        if (response && (response.code === '0' || response.success)) {
          message.success('删除版本成功');

          // 从版本列表中移除
          const index = versionList.value.findIndex(v => v.id === versionId);
          if (index !== -1) {
            versionList.value.splice(index, 1);
          }

          // 如果当前选中的是被删除的版本，切换到当前版本
          if (selectedVersion.value === versionId) {
            selectedVersion.value = 'current';
          }
        } else {
          message.error('删除版本失败: ' + (response?.message || '未知错误'));
        }
      } catch (error) {
        console.error('删除版本出错:', error);
        message.error('删除版本出错: ' + (error.message || '未知错误'));
      }
    }
  });
};

// 页面加载时初始化数据
onMounted(async () => {
  console.log('历史版本查看页面加载');
  console.log('路由参数:', route.query);
  console.log('保护组ID:', protectionGroupId.value);
  console.log('保护组类型:', protectionGroupType.value);
  console.log('场景类型:', scenarioType.value);

  // 加载保护组信息
  await loadProtectionGroupInfo();

  // 加载历史版本列表
  await loadHistoryVersions();

  // 使用nextTick确保DOM已经渲染完成
  nextTick(async () => {
    try {
      // 加载当前版本数据
      console.log('加载当前版本数据');
      await loadCurrentVersionData();

      // 等待地图初始化完成
      console.log('等待地图初始化完成');
      await new Promise(resolve => setTimeout(resolve, 500));

      // 如果选择了历史版本，加载历史版本数据
      if (selectedVersion.value !== 'current') {
        console.log('加载历史版本数据');
        await loadHistoryVersionData(selectedVersion.value);
      }
    } catch (error) {
      console.error('初始化数据失败:', error);
      message.error('初始化数据失败');
    }
  });
});
</script>

<style scoped lang="less">
.history-version-container {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .page-title {
      h1 {
        margin: 0;
        font-size: 24px;
      }

      .subtitle {
        color: #666;
        margin-left: 10px;
      }
    }
  }

  .info-card {
    margin-bottom: 20px;
  }

  .version-timeline-card {
    margin-bottom: 20px;

    .version-timeline-content {
      padding: 10px 0;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .timeline-container {
        position: relative;
        padding: 20px 0; // 调整内边距
        margin: 0 40px; // 调整左右边距
        overflow: visible; // 允许标签溢出容器

        .version-rows-container {
          display: flex;
          flex-direction: column;
          gap: 60px; // 行之间的间距

          .version-row {
            position: relative;
            height: 160px; // 每行的高度
            margin: 0 20px; // 行的左右边距

            .timeline-line {
              position: absolute;
              top: 50%;
              left: 0;
              right: 0;
              height: 6px; // 线的高度
              background: linear-gradient(to right, #e6f7ff, #1890ff, #e6f7ff); // 渐变背景
              border-radius: 3px; // 圆角
              transform: translateY(-50%);
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); // 添加阴影
            }
          }
        }

        .timeline-node {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          cursor: pointer;
          transition: all 0.3s;

          .snapshot-icon {
            width: 48px; // 增加尺寸
            height: 48px;
            margin: 0 auto;
            transition: all 0.3s;
            position: relative;
            z-index: 2; // 确保图标在时间线上方

            .cd-icon {
              width: 48px; // 增加尺寸
              height: 48px;
              border-radius: 50%;
              background: linear-gradient(135deg, #e8e8e8, #ffffff);
              border: 2px solid #d9d9d9;
              box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15); // 增强阴影
              position: relative;
              overflow: hidden;
              transition: all 0.3s;

              .cd-inner {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 14px; // 增加尺寸
                height: 14px;
                border-radius: 50%;
                background-color: #fff;
                border: 1px solid #d9d9d9;
              }

              &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
                border-radius: 50%;
              }
            }

            .folder-icon {
              width: 48px; // 增加尺寸
              height: 38px;
              position: relative;

              .folder-back {
                position: absolute;
                bottom: 0;
                width: 48px; // 增加尺寸
                height: 34px;
                background-color: #1890ff;
                border-radius: 4px; // 增加圆角
                box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15); // 增强阴影
              }

              .folder-front {
                position: absolute;
                top: 0;
                left: 0;
                width: 20px; // 增加尺寸
                height: 8px;
                background-color: #1890ff;
                border-top-left-radius: 4px; // 增加圆角
                border-top-right-radius: 4px;
              }
            }
          }

          .timeline-label {
            position: absolute;
            top: 60px; // 增加距离，适应更大的图标
            left: 50%;
            transform: translateX(-50%);
            width: 180px; // 进一步增加宽度
            text-align: center;
            transition: all 0.3s;
            background-color: rgba(255, 255, 255, 0.9); // 添加半透明背景
            padding: 4px 6px; // 减小内边距
            border-radius: 4px; // 添加圆角
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); // 添加阴影
            z-index: 1; // 确保标签在时间线上方
            max-height: 70px; // 限制最大高度
            overflow: hidden; // 超出部分隐藏

            .version-name {
              font-weight: bold;
              font-size: 14px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              color: #333; // 更深的颜色
              margin-bottom: 3px; // 减小间距
              padding: 0 2px; // 添加左右内边距
            }

            .version-date {
              font-size: 12px;
              color: #666; // 更深的颜色
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              margin-bottom: 1px; // 减小间距
              padding: 0 2px; // 添加左右内边距
            }

            .version-creator {
              font-size: 12px;
              color: #666; // 更深的颜色
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              padding: 0 2px; // 添加左右内边距
            }

            .version-description {
              font-size: 12px;
              color: #666;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font-style: italic;
              max-width: 170px; // 增加最大宽度
              margin-top: 1px; // 减小间距
              padding: 0 2px; // 添加左右内边距
            }
          }

          &.active {
            .snapshot-icon {
              transform: scale(1.2); // 更大的缩放

              .cd-icon {
                border-color: #1890ff;
                box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.3), 0 4px 8px rgba(0, 0, 0, 0.2); // 双层阴影
                background: linear-gradient(135deg, #e8f4ff, #f0f8ff);
                animation: pulse 1.5s infinite; // 添加脉冲动画
              }

              .folder-icon {
                .folder-back, .folder-front {
                  background-color: #1890ff;
                  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); // 增强阴影
                }
              }
            }

            .timeline-label {
              color: #1890ff;
              font-weight: bold;
              background-color: #e6f7ff; // 浅蓝色背景
              border: 1px solid #91d5ff; // 蓝色边框
              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2); // 蓝色阴影
              width: 200px; // 活动状态下增加宽度
              z-index: 3; // 确保活动状态的标签在最上层
            }
          }

          @keyframes pulse {
            0% {
              box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
            }
            70% {
              box-shadow: 0 0 0 6px rgba(24, 144, 255, 0);
            }
            100% {
              box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
            }
          }

          &:hover:not(.active):not(.current-version) {
            .snapshot-icon {
              transform: scale(1.1);
            }

            .timeline-label {
              background-color: #f5f5f5; // 悬停时的背景色
              box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15); // 增强阴影
              width: 185px; // 悬停时增加宽度
            }
          }

          &.current-version {
            cursor: default;

            &:hover {
              .snapshot-icon {
                transform: none;
              }
            }

            .folder-icon {
              opacity: 0.9;
              transform: translateY(-2px); // 轻微上移

              .folder-back, .folder-front {
                background-color: #40a9ff; // 更亮的蓝色
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2); // 增强阴影
              }
            }

            .timeline-label {
              background-color: #f0f7ff; // 特殊背景色
              border: 1px dashed #91d5ff; // 虚线边框
              width: 190px; // 当前版本标签增加宽度
              z-index: 2; // 确保当前版本的标签层级高于普通标签
            }
          }
        }

        /* 移除了当前选中版本指示器的样式 */
      }

      .version-comparison-info {
        background-color: #f6f6f6;
        border-radius: 4px;
        padding: 16px;

        .comparison-title {
          font-weight: bold;
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          gap: 8px;
          color: #1890ff;
        }

        .comparison-content {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;

          .comparison-item {
            display: flex;
            align-items: center;
            gap: 8px;

            .comparison-label {
              font-weight: bold;
              color: #666;
            }

            .comparison-value {
              color: #1890ff;
            }
          }
        }
      }
    }
  }

  .map-comparison-card {
    margin-bottom: 20px;

    .map-comparison-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .map-comparison-title {
        font-weight: bold;
        font-size: 16px;
      }

      .map-comparison-actions {
        display: flex;
      }
    }

    .map-comparison-container {
      display: flex;
      flex-direction: row;
      gap: 20px;

      @media (max-width: 1200px) {
        flex-direction: column;
      }

      .map-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        overflow: hidden;

        .map-title {
          padding: 10px;
          background-color: #fafafa;
          border-bottom: 1px solid #f0f0f0;
          font-weight: bold;
        }

        .map-content {
          position: relative;
          height: 500px;

          .version-overlay, .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .version-overlay {
            .overlay-content {
              background-color: white;
              padding: 20px;
              border-radius: 4px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
          }

          .loading-overlay {
            background-color: rgba(255, 255, 255, 0.6);
          }

          .map-loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 1000;

            .loading-text {
              margin-top: 10px;
              color: #666;
            }
          }

          .map-element {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }

  .optical-path-card {
    margin-bottom: 20px;

    .optical-path-container {
      display: flex;
      gap: 20px;

      @media (max-width: 1200px) {
        flex-direction: column;
      }

      .optical-path-list {
        flex: 1;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        padding: 16px;

        .optical-path-title {
          font-weight: bold;
          margin-bottom: 16px;
          text-align: center;
          padding-bottom: 8px;
          border-bottom: 1px solid #f0f0f0;
        }
      }
    }
  }

  .problem-comparison-card {
    margin-bottom: 20px;

    .problem-comparison-container {
      min-height: 200px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

// 右键菜单触发器样式
.context-menu-trigger {
  position: fixed;
  z-index: 1000;
  width: 1px;
  height: 1px;
}
</style>