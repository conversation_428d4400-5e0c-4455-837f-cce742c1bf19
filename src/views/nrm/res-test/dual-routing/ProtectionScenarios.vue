<template>
  <div class="protection-scenarios-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="title-icon">
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M9 12l2 2 4-4" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          网络保护场景
        </h1>
        <p class="page-subtitle">选择需要查看的网络保护场景</p>
      </div>
    </div>

    <!-- 场景卡片 -->
    <div class="scenarios-grid">
      <!-- 单设备双上联 -->
      <div class="scenario-card" @click="navigateToScenario('single-device-dual-uplink')">
        <div class="scenario-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="scenario-svg">
            <rect x="4" y="14" width="16" height="6" rx="2" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 14v-4M8 6l4 4 4-4" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h3 class="scenario-title">单设备双上联</h3>
        <p class="scenario-description">单个设备通过两条链路连接到上层设备的保护场景</p>
        <div class="scenario-stats">
          <div class="stat-item">
            <span class="stat-label">问题设备数</span>
            <span class="stat-value" v-if="!stats.singleDeviceDualUplink.loading">
              {{ stats.singleDeviceDualUplink.devices }}
            </span>
            <a-spin v-else size="small" />
          </div>
          <div class="stat-item">
            <span class="stat-label">风险数</span>
            <span class="stat-value" v-if="!stats.singleDeviceDualUplink.loading">
              {{ stats.singleDeviceDualUplink.risks }}
            </span>
            <a-spin v-else size="small" />
          </div>
        </div>
        <a-button type="primary" class="scenario-button">查看详情</a-button>
      </div>

      <!-- 设备对双路由 -->
      <div class="scenario-card" @click="navigateToScenario('dual-device-dual-route')">
        <div class="scenario-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="scenario-svg">
            <rect x="2" y="14" width="8" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="14" y="14" width="8" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M6 14v-3c0-1.1.9-2 2-2h8c1.1 0 2 .9 2 2v3" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 9V4" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h3 class="scenario-title">设备对双路由</h3>
        <p class="scenario-description">两个设备通过两条独立路由实现冗余保护的场景</p>
        <div class="scenario-stats">
          <div class="stat-item">
            <span class="stat-label">问题设备组数</span>
            <span class="stat-value" v-if="!stats.dualDeviceDualRoute.loading">
              {{ stats.dualDeviceDualRoute.devices }}
            </span>
            <a-spin v-else size="small" />
          </div>
          <div class="stat-item">
            <span class="stat-label">风险数</span>
            <span class="stat-value" v-if="!stats.dualDeviceDualRoute.loading">
              {{ stats.dualDeviceDualRoute.risks }}
            </span>
            <a-spin v-else size="small" />
          </div>
        </div>
        <a-button type="primary" class="scenario-button">查看详情</a-button>
      </div>

      <!-- 设备对三路由 -->
      <div class="scenario-card" @click="navigateToScenario('dual-device-triple-route')">
        <div class="scenario-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="scenario-svg">
            <rect x="2" y="14" width="8" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="14" y="14" width="8" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M6 14v-3c0-1.1.9-2 2-2h8c1.1 0 2 .9 2 2v3" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 9V4M8 6l4-2 4 2" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h3 class="scenario-title">设备对三路由</h3>
        <p class="scenario-description">两个设备通过三条独立路由实现高可靠性保护的场景</p>
        <div class="scenario-stats">
          <div class="stat-item">
            <span class="stat-label">问题设备组数</span>
            <span class="stat-value" v-if="!stats.dualDeviceTripleRoute.loading">
              {{ stats.dualDeviceTripleRoute.devices }}
            </span>
            <a-spin v-else size="small" />
          </div>
          <div class="stat-item">
            <span class="stat-label">风险数</span>
            <span class="stat-value" v-if="!stats.dualDeviceTripleRoute.loading">
              {{ stats.dualDeviceTripleRoute.risks }}
            </span>
            <a-spin v-else size="small" />
          </div>
        </div>
        <a-button type="primary" class="scenario-button">查看详情</a-button>
      </div>

      <!-- 环形组网 -->
      <div class="scenario-card" @click="navigateToScenario('ring-network')">
        <div class="scenario-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="scenario-svg">
            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <circle cx="12" cy="7" r="1" fill="currentColor"/>
            <circle cx="17" cy="12" r="1" fill="currentColor"/>
            <circle cx="12" cy="17" r="1" fill="currentColor"/>
            <circle cx="7" cy="12" r="1" fill="currentColor"/>
          </svg>
        </div>
        <h3 class="scenario-title">环形组网</h3>
        <p class="scenario-description">多个设备形成环形拓扑结构的保护场景</p>
        <div class="scenario-stats">
          <div class="stat-item">
            <span class="stat-label">问题设备组数</span>
            <span class="stat-value" v-if="!stats.ringNetwork.loading">
              {{ stats.ringNetwork.devices }}
            </span>
            <a-spin v-else size="small" />
          </div>
          <div class="stat-item">
            <span class="stat-label">风险数</span>
            <span class="stat-value" v-if="!stats.ringNetwork.loading">
              {{ stats.ringNetwork.risks }}
            </span>
            <a-spin v-else size="small" />
          </div>
        </div>
        <a-button type="primary" class="scenario-button">查看详情</a-button>
      </div>


    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { useUnifiedApi } from '@/hooks/web/useUnifiedApi';
import { useUserStoreWithOut } from '@/store/modules/user';
import * as dualRoutingApi from '@/api/dual-routing';

const router = useRouter();

// 获取用户存储
const userStore = useUserStoreWithOut();
const areaCode = userStore.getAreaCode;

// 如果是江苏，默认查询无锡的数据，否则使用用户所在地市
const defaultCityCode = areaCode === 'js' ? 'WX' : areaCode;

// 初始化统一API调用钩子
const unifiedApi = useUnifiedApi({
  rootPath: '/graph-rest-api',
  defaultCityCode: defaultCityCode
});

// 统计数据
const stats = ref({
  singleDeviceDualUplink: {
    devices: 0,
    risks: 0,
    loading: false
  },
  dualDeviceDualRoute: {
    devices: 0,
    risks: 0,
    loading: false
  },
  dualDeviceTripleRoute: {
    devices: 0,
    risks: 0,
    loading: false
  },
  ringNetwork: {
    devices: 0,
    risks: 0,
    loading: false
  }
});

// 获取单设备双上联统计数据
const fetchSingleDeviceDualUplinkStats = async () => {
  try {
    stats.value.singleDeviceDualUplink.loading = true;

    // 调用与详细列表页面相同的API
    const params = {
      pageSize: 1000, // 获取所有数据用于统计
      currentPage: 1
    };

    const result = await unifiedApi.querySlyDevices(defaultCityCode, params);

    if (result && result.data) {
      const devices = Array.isArray(result.data) ? result.data : [];

      // 统计问题设备数（按设备个数统计）
      const problemDevices = devices.filter(device => device.error && device.error !== '无问题');
      stats.value.singleDeviceDualUplink.devices = problemDevices.length;

      // 统计风险数（所有错误的总数）
      const totalRisks = problemDevices.reduce((sum, device) => {
        return sum + (device.error_count || 1);
      }, 0);
      stats.value.singleDeviceDualUplink.risks = totalRisks;
    }
  } catch (error) {
    console.error('获取单设备双上联统计数据失败:', error);
  } finally {
    stats.value.singleDeviceDualUplink.loading = false;
  }
};

// 获取设备对双路由统计数据
const fetchDualDeviceDualRouteStats = async () => {
  try {
    stats.value.dualDeviceDualRoute.loading = true;

    const params = {
      pageSize: 1000,
      currentPage: 1
    };

    const result = await unifiedApi.queryDevicePairs(defaultCityCode, params);

    if (result && result.data) {
      const devicePairs = Array.isArray(result.data) ? result.data : [];

      // 统计问题设备组数（按设备对统计）
      const problemPairs = devicePairs.filter(pair =>
        pair.error && pair.error !== '无问题'
      );
      stats.value.dualDeviceDualRoute.devices = problemPairs.length;

      // 统计风险数
      const totalRisks = problemPairs.reduce((sum, pair) => {
        return sum + (pair.error_count || 1);
      }, 0);
      stats.value.dualDeviceDualRoute.risks = totalRisks;
    }
  } catch (error) {
    console.error('获取设备对双路由统计数据失败:', error);
  } finally {
    stats.value.dualDeviceDualRoute.loading = false;
  }
};

// 获取设备对三路由统计数据
const fetchDualDeviceTripleRouteStats = async () => {
  try {
    stats.value.dualDeviceTripleRoute.loading = true;

    const params = {
      pageSize: 1000,
      currentPage: 1
    };

    const result = await unifiedApi.queryTripleRoutePairs(defaultCityCode, params);

    if (result && result.data) {
      const triplePairs = Array.isArray(result.data) ? result.data : [];

      // 统计问题设备组数（按设备对统计）
      const problemPairs = triplePairs.filter(pair =>
        pair.error && pair.error !== '无问题'
      );
      stats.value.dualDeviceTripleRoute.devices = problemPairs.length;

      // 统计风险数
      const totalRisks = problemPairs.reduce((sum, pair) => {
        return sum + (pair.error_count || 1);
      }, 0);
      stats.value.dualDeviceTripleRoute.risks = totalRisks;
    }
  } catch (error) {
    console.error('获取设备对三路由统计数据失败:', error);
  } finally {
    stats.value.dualDeviceTripleRoute.loading = false;
  }
};

// 获取环形组网统计数据
const fetchRingNetworkStats = async () => {
  try {
    stats.value.ringNetwork.loading = true;

    const params = {
      pageSize: 1000,
      currentPage: 1,
      errors: ['端口&光路错误', '板卡错误', '同光缆/无穿管/同管道异常'] // 查询所有错误类型
    };

    const result = await dualRoutingApi.queryARingDevices(defaultCityCode, params);

    if (result && result.data) {
      const ringDevices = Array.isArray(result.data) ? result.data : [];

      // 统计问题设备组数（按环设备组统计）
      stats.value.ringNetwork.devices = ringDevices.length;

      // 统计风险数（按错误类型统计）
      let totalRisks = 0;
      ringDevices.forEach(device => {
        // 统计各种错误类型
        if (device.route_error_count) totalRisks += parseInt(device.route_error_count) || 0;
        if (device.device_error_count) totalRisks += parseInt(device.device_error_count) || 0;
        if (device.cable_error_count) totalRisks += parseInt(device.cable_error_count) || 0;
      });
      stats.value.ringNetwork.risks = totalRisks;
    }
  } catch (error) {
    console.error('获取环形组网统计数据失败:', error);
  } finally {
    stats.value.ringNetwork.loading = false;
  }
};

// 获取所有统计数据
const fetchAllStats = async () => {
  console.log('开始获取所有场景统计数据...');

  // 并行获取所有场景的统计数据
  await Promise.all([
    fetchSingleDeviceDualUplinkStats(),
    fetchDualDeviceDualRouteStats(),
    fetchDualDeviceTripleRouteStats(),
    fetchRingNetworkStats()
  ]);

  console.log('所有场景统计数据获取完成:', stats.value);
};

// 导航到对应的场景页面
const navigateToScenario = (scenario: string) => {
  router.push(`/nrm/dualrouting/protection-scenario/${scenario}`);
};

// 在组件挂载时获取数据
onMounted(() => {
  fetchAllStats();
});
</script>

<style lang="less" scoped>
.protection-scenarios-container {
  padding: 32px 24px;
  background: linear-gradient(135deg, #f6f9fc 0%, #f0f5fa 100%);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  width: 100%;
}

.page-header {
  background: white;
  padding: 32px;
  border-radius: 16px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(0, 123, 255, 0) 100%);
    z-index: 0;
  }

  .header-content {
    position: relative;
    z-index: 1;
  }
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  display: flex;
  align-items: center;
}

.title-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  color: #0066ff;
}

.page-subtitle {
  margin: 8px 0 0;
  color: #666;
  font-size: 14px;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-top: 32px;
  width: 100%;
  padding: 16px 0 48px;
}

.scenario-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  cursor: pointer;
  height: 360px;
  justify-content: space-between;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  }
}

.scenario-icon {
  width: 80px;
  height: 80px;
  background: rgba(0, 102, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  margin-top: 20px;
}

.scenario-svg {
  width: 40px;
  height: 40px;
  color: #0066ff;
}

.scenario-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px;
}

.scenario-description {
  font-size: 13px;
  color: #666;
  margin: 0 0 16px;
  line-height: 1.5;
  padding: 0 8px;
  height: 60px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.scenario-stats {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-bottom: 16px;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #0066ff;
}

.scenario-button {
  margin-top: 8px;
  padding: 0 24px;
  height: 36px;
  font-size: 15px;
}
</style>
