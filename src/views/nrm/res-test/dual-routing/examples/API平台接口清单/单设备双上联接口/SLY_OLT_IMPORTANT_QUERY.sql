API编码：SLY_OLT_IMPORTANT_QUERY
API版本：V20250601000000000
API内容：
hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

var queryImportantOlt = @@mybatis(p)<%
<select>
SELECT 
    i.*,
    b.device_name,
    b.device_spec,
    b.create_date
FROM sly_olt_important i
LEFT JOIN sly_device_base_info b ON i.device_code = b.device_code
WHERE 1=1
    <if test="p.cityName != null and p.cityName != ''">
        AND i.city_name = #{p.cityName}::VARCHAR
    </if>
    <if test="p.areaName != null and p.areaName != ''">
        AND i.area_name = #{p.areaName}::VARCHAR
    </if>
    <if test="p.keyword != null and p.keyword != ''">
        AND (
            b.device_name LIKE concat('%', #{p.keyword}, '%')
            OR i.device_code LIKE concat('%', #{p.keyword}, '%')
            OR i.ip LIKE concat('%', #{p.keyword}, '%')
        )
    </if>
    <if test="p.oltSpec != null and p.oltSpec != ''">
        AND i.olt_spec = #{p.oltSpec}::VARCHAR
    </if>
ORDER BY 
    i.create_date DESC
</select>
%>;

var pageQuery = queryImportantOlt(${param});
run pageQuery.setPageInfo({
    "pageSize": #{pageSize},
    "currentPage": #{currentPage}
});

var data = pageQuery.data();
var pageInfo = pageQuery.pageInfo();

return {
    "data": data,
    "pageInfo": pageInfo
};

入参样例：{
    "cityName": "无锡",
    "areaName": "宜兴",
    "keyword": "OLT-5",
    "oltSpec": "500-1000",
    "pageSize": 10,
    "currentPage": 1
}
