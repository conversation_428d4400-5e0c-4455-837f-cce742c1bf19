API编码：SLY_DEVICE_BASE_INFO_QUERY
API版本：V20250419121012578
API内容：
hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

var queryDevices = @@mybatis(p)<%
<select>
WITH error_stats AS (

SELECT city_name, area_name, device_code, device_spec, create_date, error, COUNT(1) AS error_count
FROM sly_dwdm_gl_route_err
GROUP BY city_name, area_name, device_code, device_spec, create_date, error
UNION ALL

SELECT city_name, area_name, device_code, device_spec, create_date, error, COUNT(1)
FROM sly_device_gl_bk_err
GROUP BY city_name, area_name, device_code, device_spec, create_date, error
UNION ALL

SELECT city_name, area_name, device_code, device_spec, create_date, error, COUNT(1)
FROM sly_device_netsource
GROUP BY city_name, area_name, device_code, device_spec, create_date, error
)SELECT
b.*,
e.error,
e.error_count,
i.user_num,
i.olt_spec
FROM sly_device_base_info b
left JOIN error_stats e ON b.device_code = e.device_code
LEFT JOIN sly_olt_important i ON b.device_code = i.device_code
WHERE 1=1
    <if test="p.cityName != null and p.cityName != ''">
        AND b.city_name = #{p.cityName}::VARCHAR
    </if>
    <if test="p.areaName != null and p.areaName != ''">
        AND b.area_name = #{p.areaName}::VARCHAR
    </if>
    <if test="p.deviceSpec != null and p.deviceSpec != ''">
        AND b.device_spec = #{p.deviceSpec}::VARCHAR
    </if>
    <if test="p.keyword != null and p.keyword != ''">
        AND (
            b.device_name LIKE concat('%', #{p.keyword}, '%')
            OR b.device_code LIKE concat('%', #{p.keyword}, '%')
            OR b.ip LIKE concat('%', #{p.keyword}, '%')
        )
    </if>

    <if test="p.deviceSpecs != null  and p.deviceSpecs != ''">
               and  b.device_spec in
            <foreach collection="p.deviceSpecs" item="deviceSpec" open="(" close=")" separator=",">
            #{deviceSpec}
            </foreach>
        </if>

    <if test="p.error_count != null and p.error_count != ''">
        AND error_count > #{p.error_count}
    </if>

    <if test="p.errors != null  and p.errors != ''">
               and  error in
            <foreach collection="p.errors" item="error" open="(" close=")" separator=",">
            #{error}
            </foreach>
        </if>

    <if test="p.isImportantOlt != null and p.isImportantOlt == true">
        AND i.device_code IS NOT NULL
        <if test="p.oltSpec != null and p.oltSpec != ''">
            AND i.olt_spec = #{p.oltSpec}::VARCHAR
        </if>
    </if>

ORDER BY
    b.create_date DESC
</select>
%>;

var pageQuery = queryDevices(${param});
run pageQuery.setPageInfo({
    "pageSize": #{pageSize},
    "currentPage": #{currentPage}
});

var data = pageQuery.data();
var pageInfo = pageQuery.pageInfo();

return {
    "data": data,
    "pageInfo": pageInfo
};

入参样例：{
    "cityName": "无锡",
    "areaName": "宜兴",
    "deviceSpec": "OLT设备",
    "keyword": "OLT-5",
    "isImportantOlt": true,
    "oltSpec": "500-1000",
    "pageSize": 10,
    "currentPage": 1
}
