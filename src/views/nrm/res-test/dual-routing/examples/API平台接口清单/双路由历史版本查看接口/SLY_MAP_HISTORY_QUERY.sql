API编码：SLY_MAP_HISTORY_QUERY
V20250516110111700
API内容：
hint FRAGMENT_SQL_COLUMN_CASE = "lower";
hint FRAGMENT_SQL_OPEN_PACKAGE = "off";
hint FRAGMENT_SQL_QUERY_BY_PAGE = true;
hint FRAGMENT_SQL_QUERY_BY_PAGE_NUMBER_OFFSET = 1;

var queryHistoryVersions = @@mybatis(p)<%
<select>
SELECT
  id,protection_group_id,scenario_type,device_code,device_pair_id,ring_number,city_code,area_code,b1_code,b2_code,description,create_time,creator
FROM sly_map_geojson_version
where 1=1
    <if test="p.id != null">
        AND id = #{p.id}
    </if>

    <if test="p.protectionGroupId != null and p.protectionGroupId != ''">
        AND protection_group_id = #{p.protectionGroupId}
    </if>

    <if test="p.scenarioType != null and p.scenarioType != ''">
        AND scenario_type = #{p.scenarioType}
    </if>

    <if test="p.deviceCode != null and p.deviceCode != ''">
        AND device_code = #{p.deviceCode}
    </if>

    <if test="p.devicePairId != null and p.devicePairId != ''">
        AND device_pair_id = #{p.devicePairId}
    </if>

    <if test="p.ringNumber != null and p.ringNumber != ''">
        AND ring_number = #{p.ringNumber}
    </if>

    <if test="p.cityCode != null and p.cityCode != ''">
        AND city_code = #{p.cityCode}
    </if>

    <if test="p.areaCode != null and p.areaCode != ''">
        AND area_code = #{p.areaCode}
    </if>

    <if test="p.b1Code != null and p.b1Code != ''">
        AND b1_code = #{p.b1Code}
    </if>

    <if test="p.b2Code != null and p.b2Code != ''">
        AND b2_code = #{p.b2Code}
    </if>

    <if test="p.startTime != null">
        AND create_time &gt;	 #{p.startTime}::timestamp
    </if>

    <if test="p.endTime != null">
        AND create_time  &lt;	 #{p.endTime}::timestamp
    </if>


    <if test="p.description != null">
        AND description  like concat('%', #{p.description}, '%')
    </if>



ORDER BY create_time DESC
</select>
%>;

// 使用查询方法
var pageQuery = queryHistoryVersions(${param});

run pageQuery.setPageInfo({
    "pageSize": #{pageSize},
    "currentPage": #{currentPage}
});

var data = pageQuery.data();
var pageInfo = pageQuery.pageInfo();


return {
    "data": data,
    "pageInfo": pageInfo
};
入参样例：{
    // 查询列表
    "protectionGroupId": "PG123457",
    "scenarioType": "dualDeviceDualRoute",
    "deviceCode": "DEV002",
    "devicePairId": "DP123456",
    "ringNumber": "A001",
    "cityCode": "WX",
    "areaCode": "js",
    "b1Code": "B1001",
    "b2Code": "B2001",
    "startTime": "2025-06-01 00:00:00",
    "endTime": "2025-06-30 23:59:59",
    "pageSize": 10,
    "currentPage": 1
}
