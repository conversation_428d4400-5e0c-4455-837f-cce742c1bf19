<template>
  <div class="single-device-dual-uplink-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <h1>{{ pageTitle }}</h1>
        <span class="subtitle">{{ pageSubtitle }}</span>
      </div>
    </div>

    <!-- 场景卡片导航 -->
    <div class="scenario-cards">
      <div class="scenario-card-item active" @click="switchScenario('single-device-dual-uplink')">
        <div class="card-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="card-svg">
            <rect x="4" y="14" width="16" height="6" rx="2" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 14v-4M8 6l4 4 4-4" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="card-text">单设备双上联</div>
        <div class="card-description">管理和监控单设备双上联场景中的问题设备</div>
      </div>

      <div class="scenario-card-item" @click="switchScenario('dual-device-dual-route')">
        <div class="card-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="card-svg">
            <rect x="2" y="14" width="8" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="14" y="14" width="8" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M6 14v-3c0-1.1.9-2 2-2h8c1.1 0 2 .9 2 2v3" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 9V4" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="card-text">设备对双路由</div>
        <div class="card-description">管理和监控设备对双路由场景中的问题设备</div>
      </div>

      <div class="scenario-card-item" @click="switchScenario('dual-device-triple-route')">
        <div class="card-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="card-svg">
            <rect x="2" y="14" width="8" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="14" y="14" width="8" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M6 14v-3c0-1.1.9-2 2-2h8c1.1 0 2 .9 2 2v3" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M12 9V4M8 6l4-2 4 2" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="card-text">设备对三路由</div>
        <div class="card-description">管理和监控设备对三路由场景中的问题设备</div>
      </div>

      <div class="scenario-card-item" @click="switchScenario('ring-network')">
        <div class="card-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="card-svg">
            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <circle cx="12" cy="7" r="1" fill="currentColor"/>
            <circle cx="17" cy="12" r="1" fill="currentColor"/>
            <circle cx="12" cy="17" r="1" fill="currentColor"/>
            <circle cx="7" cy="12" r="1" fill="currentColor"/>
          </svg>
        </div>
        <div class="card-text">环形组网</div>
        <div class="card-description">管理和监控环形组网场景中的问题设备</div>
      </div>


    </div>



    <!-- 场景说明和逻辑图 -->
    <div class="scenario-overview">
      <div class="scenario-description-card">
        <div class="card-header">
          <h2>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="header-icon">
              <path d="M12 9V5M12 19v-4M9 12H5M19 12h-4M7.05 7.05l2.83 2.83M14.12 14.12l2.83 2.83M14.12 9.88l2.83-2.83M7.05 16.95l2.83-2.83" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            场景说明
          </h2>
          <!-- <a-tag color="blue">单设备双上联</a-tag> -->
        </div>
        <p>单设备双上联场景是指单个设备通过两条独立的链路连接到上层设备的网络保护场景。这种配置可以提供冗余保护，当一条链路发生故障时，流量可以通过另一条链路继续传输，从而提高网络的可靠性和可用性。</p>

        <div class="criteria-list">
          <div class="criteria-header">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="criteria-icon">
              <path d="M9 12l2 2 4-4M21 12a9 9 0 11-18 0 9 9 0 0118 0z" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <h3>问题判断标准</h3>
          </div>
          <ul>
            <li><strong>缺端口：</strong>每台设备至少需要有两个上联端口，否则是缺端口</li>
            <li><strong>缺光路：</strong>每个设备至少需要有两条上联光路，否则是缺光路</li>
            <li><strong>异常光路：</strong>需看具体异常信息分析</li>
            <li><strong>同光缆：</strong>设备所有上联光路都经过了同一条光缆段</li>
            <li><strong>无穿管：</strong>光缆段没有对应的管道段</li>
            <li><strong>同管道：</strong>设备的所有上联光路都经过了同一条管道段</li>
            <li><strong>无板卡：</strong>设备的上联光路没找到对应的板卡</li>
            <li><strong>同板卡：</strong>设备所有上联光路都是同一个板卡</li>
          </ul>
        </div>
      </div>

      <div class="scenario-diagram-card">
        <div class="card-header">
          <h2>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="header-icon">
              <path d="M14 3v4a1 1 0 001 1h4M9.5 7v0m0 4v0m0 4v0m5-8v0m0 4v0m0 4v0M17 21H7a2 2 0 01-2-2V5a2 2 0 012-2h7l5 5v11a2 2 0 01-2 2z" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            逻辑示意图
          </h2>

        </div>
        <div class="diagram-container">
          <SingleDeviceDualUplinkPic />
        </div>
      </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-card">
      <div class="filter-header">
        <h3>筛选条件</h3>
        <a-button @click="toggleAdvancedFilter" class="advanced-filter-button">
          {{ showAdvancedFilter ? '收起查询' : '展开查询' }}
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" :style="{ width: '16px', height: '16px', transform: `rotate(${showAdvancedFilter ? 180 : 0}deg)`, transition: 'transform 0.3s' }">
            <path d="M6 9l6 6 6-6" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </a-button>
      </div>

      <!-- 设备类型标签筛选 -->
      <div class="filter-section">
        <div class="filter-header-row">
          <div class="filter-label">设备类型：</div>
          <div class="filter-quick-actions">
            <a-tag class="action-tag" @click="selectAllDeviceTypes">全选</a-tag>
            <a-tag class="action-tag" @click="deselectAllDeviceTypes">全不选</a-tag>
            <a-tag class="action-tag" @click="invertDeviceTypeSelection">反选</a-tag>
          </div>
        </div>
        <div class="filter-tags">
          <a-tag
            v-for="option in deviceTypeOptions"
            :key="option.value"
            :color="isDeviceTypeSelected(option.value) ? '#108ee9' : ''"
            class="filter-tag"
            @click="toggleDeviceTypeFilter(option.value)"
          >
            {{ option.label }}
          </a-tag>
        </div>

        <!-- 大客户OLT选项，仅当选中OLT设备时显示 -->
        <div v-if="isDeviceTypeSelected('OLT设备')" class="important-olt-section">
          <a-checkbox
            v-model:checked="filters.isImportantOlt"
            @change="handleImportantOltChange"
            class="important-olt-checkbox"
          >
            大客户OLT
          </a-checkbox>

          <!-- 客户范围选择，仅当选中大客户OLT时显示 -->
          <a-select
            v-if="filters.isImportantOlt"
            v-model:value="filters.userNumRange"
            placeholder="请选择客户范围"
            style="width: 150px; margin-left: 16px;"
            @change="handleUserNumRangeChange"
          >
            <a-select-option v-for="option in userNumRangeOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </a-select-option>
          </a-select>
        </div>
      </div>

      <!-- 问题类型标签筛选 -->
      <div class="filter-section">
        <div class="filter-header-row">
          <div class="filter-label">问题类型：</div>
          <div class="filter-quick-actions">
            <a-tag class="action-tag" @click="selectAllProblemTypes">全选</a-tag>
            <a-tag class="action-tag" @click="deselectAllProblemTypes">全不选</a-tag>
            <a-tag class="action-tag" @click="invertProblemTypeSelection">反选</a-tag>
          </div>
        </div>
        <div class="filter-tags">
          <a-tag
            v-for="option in problemTypeOptions"
            :key="option.value"
            :color="isProblemTypeSelected(option.value) ? '#108ee9' : ''"
            class="filter-tag"
            @click="toggleProblemTypeFilter(option.value)"
          >
            {{ option.label }}
          </a-tag>
        </div>
      </div>

      <!-- 高级筛选条件 -->
      <div v-show="showAdvancedFilter" class="advanced-filters">
        <a-form layout="horizontal">
          <a-row :gutter="[16, 16]">
            <a-col :span="8">
              <a-form-item label="设备名称">
                <a-input v-model:value="filters.deviceName" placeholder="请输入设备名称（支持模糊查询）" />
              </a-form-item>
            </a-col>

            <a-col :span="8">
              <a-form-item label="设备编码">
                <a-input v-model:value="filters.deviceCode" placeholder="请输入设备编码（支持模糊查询）" />
              </a-form-item>
            </a-col>

            <a-col :span="8">
              <!-- IP地址字段在后台数据中不存在，暂时注释
              <a-form-item label="IP地址">
                <a-input v-model:value="filters.ipAddress" placeholder="请输入IP地址（支持模糊查询）" />
              </a-form-item> -->
            </a-col>

            <a-col :span="8">
              <a-form-item label="城市">
                <a-select
                  v-model:value="filters.cityCode"
                  placeholder="请选择城市"
                  :disabled="areaCode !== 'js'"
                  @change="handleCityChange"
                >
                  <a-select-option v-for="city in cityOptions" :key="city.value" :value="city.value">{{ city.label }}</a-select-option>
                </a-select>
                <template v-if="areaCode !== 'js'">
                  <div class="city-info-text">当前地市：{{ getAreaName(areaCode) }}</div>
                </template>
              </a-form-item>
            </a-col>

            <a-col :span="8">
              <a-form-item label="区域">
                <a-input v-model:value="filters.region" placeholder="请输入区域名称（支持模糊查询）" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="[16, 16]">
            <!-- <a-col :span="8">
              <a-form-item label="管理状态">
                <a-select
                  v-model:value="filters.managementStatus"
                  placeholder="请选择管理状态"
                >
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="normal">正常</a-select-option>
                  <a-select-option value="warning">警告</a-select-option>
                  <a-select-option value="error">错误</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :span="8">
              <a-form-item label="上联端口数">
                <div class="range-input">
                  <a-input-number v-model:value="filters.portCount[0]" placeholder="最小值" style="width: 100px" />
                  <span class="range-separator">-</span>
                  <a-input-number v-model:value="filters.portCount[1]" placeholder="最大值" style="width: 100px" />
                </div>
              </a-form-item>
            </a-col>

            <a-col :span="8">
              <a-form-item label="上联光路数">
                <div class="range-input">
                  <a-input-number v-model:value="filters.opticalPathCount[0]" placeholder="最小值" style="width: 100px" />
                  <span class="range-separator">-</span>
                  <a-input-number v-model:value="filters.opticalPathCount[1]" placeholder="最大值" style="width: 100px" />
                </div>
              </a-form-item>
            </a-col> -->

            <!-- <a-col :span="8">
              <a-form-item label="问题数量">
                <a-input-number v-model:value="filters.errorCount" placeholder="最小问题数量" style="width: 200px" />
              </a-form-item>
            </a-col> -->
          </a-row>
          <div class="filter-actions">
            <a-space>
              <a-button @click="resetFilters">
                <template #icon>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="button-icon">
                    <path d="M3 12a9 9 0 019-9 9.75 9.75 0 017 3l3 3M3 12a9 9 0 009 9 9.75 9.75 0 007-3l-3-3" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M19 10h3v3" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </template>
                重置
              </a-button>
              <a-button type="primary" @click="applyFilters">
                <template #icon>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="button-icon">
                    <circle cx="11" cy="11" r="8" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M21 21l-4.35-4.35" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </template>
                搜索
              </a-button>
            </a-space>
          </div>
        </a-form>
      </div>
    </div>

    <!-- 问题设备列表 -->
    <div class="device-list-card">
      <div class="card-header">
        <h3>问题设备列表</h3>
        <div class="header-right">
          <!-- <a-statistic title="总计" :value="problemDevices.length" style="margin-right: 32px;" /> -->
          <a-button type="primary" @click="batchProcess" :disabled="selectedRowKeys.length === 0">
            批量处理
          </a-button>
        </div>
      </div>

      <div  style="  height: 700px;">



      <a-table
        :columns="columns"
        :data-source="filteredDevices"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :pagination="{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: pagination.showSizeChanger,
          showQuickJumper: pagination.showQuickJumper,
          showTotal: (total: number) => `共 ${total} 条`
        }"
        @change="handleTableChange"
        :scroll="{ x: 1200, y: 600 }"
        :loading="loading"
        class="problem-device-table"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'deviceType'">
            <a-tag>{{ record.deviceType }}</a-tag>
          </template>

          <template v-if="column.dataIndex === 'problemType'">
            <a-tag :color="getProblemTypeColor(record.problemType)">{{ record.problemType }}</a-tag>
          </template>

          <template v-if="column.dataIndex === 'action'">
            <a-button type="link" @click="viewDeviceDetail(record)" size="small">
              <template #icon>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" style="width: 16px; height: 16px;">
                  <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </template>
              详情
            </a-button>
          </template>
        </template>
      </a-table>
    </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import SingleDeviceDualUplinkPic from '@/views/nrm/res-test/dual-routing/relation-graph-topology/component/singleDeviceDualUplinkPic.vue';
import { useUnifiedApi } from '@/hooks/web/useUnifiedApi';
import { message } from 'ant-design-vue';
import { useUserStoreWithOut } from '@/store/modules/user';

const router = useRouter();

// 当前页面标题和描述
const pageTitle = '单设备双上联';
const pageSubtitle = '问题设备管理';

// 切换场景
const switchScenario = (key: string) => {
  // 根据选择的场景导航到对应的路由页面
  switch (key) {
    case 'single-device-dual-uplink':
      router.push('/nrm/dualrouting/protection-scenario/single-device-dual-uplink');
      break;
    case 'dual-device-dual-route':
      router.push('/nrm/dualrouting/protection-scenario/dual-device-dual-route');
      break;
    case 'dual-device-triple-route':
      router.push('/nrm/dualrouting/protection-scenario/dual-device-triple-route');
      break;
    case 'ring-network':
      router.push('/nrm/dualrouting/protection-scenario/ring-network');
      break;

  }

  console.log(`切换到场景: ${key}`);
};

// 获取用户存储
const userStore = useUserStoreWithOut();

// 获取地市代码
const areaCode = userStore.getAreaCode;
console.log('当前地市代码:', areaCode);

// 如果是江苏，默认查询无锡的数据
const defaultCityCode = areaCode === 'js' ? 'WX' : areaCode;

// 地市代码转换为地市名称
const getAreaName = (code: string) => {
  const areaMap = {
    'js': '江苏',
    'nj': '南京',
    'sz': '苏州',
    'wx': '无锡',
    'cz': '常州',
    'zj': '镇江',
    'yz': '扬州',
    'nt': '南通',
    'lyg': '连云港',
    'ha': '淮安',
    'yc': '盐城',
    'sq': '宿迁',
    'xz': '徐州',
    'tz': '泰州'
  };
  return areaMap[code.toLowerCase()] || code;
};

// 初始化统一API调用钩子
const unifiedApi = useUnifiedApi({
  rootPath: '/graph-rest-api', // 指定根路径，与项目中其他API保持一致
  defaultCityCode: defaultCityCode // 设置默认地市代码
});

// 筛选条件
const showAdvancedFilter = ref(false);
const loading = ref(false); // 添加本地的loading状态
// 问题类型选项
const problemTypeOptions = [
  { label: '缺端口', value: '缺端口' },
  { label: '缺光路', value: '缺光路' },
  { label: '异常光路', value: '异常光路' },
  { label: '同光缆', value: '同光缆' },
  { label: '无穿管', value: '无穿管' },
  { label: '同管道', value: '同管道' },
  { label: '无板卡', value: '无板卡' },
  { label: '同板卡', value: '同板卡' }
];

// 默认选中所有问题类型
const defaultProblemTypes = problemTypeOptions.map(option => option.value);

const filters = reactive({
  deviceType: [] as string[],
  problemType: [...defaultProblemTypes], // 默认选中所有问题类型
  region: '',
  deviceName: '',
  deviceCode: '',
  ipAddress: '',
  managementStatus: '',
  portCount: [null, null] as (number | null)[],
  opticalPathCount: [null, null] as (number | null)[],
  errorCount: null as number | null, // 问题数量筛选
  cityCode: defaultCityCode, // 使用从用户存储中获取的地市代码
  isImportantOlt: false, // 是否为大客户OLT
  userNumRange: '' // 客户范围
});

// 城市选项 - 如果是江苏，显示所有地市，否则只显示当前地市
const cityOptions = areaCode === 'js'
  ? [
      { label: '南京', value: 'NJ' },
      { label: '无锡', value: 'WX' },
      { label: '徐州', value: 'XZ' },
      { label: '常州', value: 'CZ' },
      { label: '苏州', value: 'SZ' },
      { label: '南通', value: 'NT' },
      { label: '连云港', value: 'LYG' },
      { label: '淮安', value: 'HA' },
      { label: '盐城', value: 'YC' },
      { label: '扬州', value: 'YZ' },
      { label: '镇江', value: 'ZJ' },
      { label: '泰州', value: 'TZ' },
      { label: '宿迁', value: 'SQ' }
    ]
  : [{ label: getAreaName(areaCode), value: areaCode }];

// 处理城市变更
const handleCityChange = (value: string) => {
  console.log('切换城市:', value);
  filters.cityCode = value;

  // 重置分页到第一页
  pagination.current = 1;

  // 重新获取数据
  fetchDeviceData();
};

// 检查设备类型是否选中
const isDeviceTypeSelected = (value: string) => {
  return filters.deviceType.includes(value);
};

// 切换设备类型筛选
const toggleDeviceTypeFilter = async (value: string) => {
  const index = filters.deviceType.indexOf(value);
  if (index === -1) {
    filters.deviceType.push(value);
  } else {
    filters.deviceType.splice(index, 1);
  }

  // 重置分页到第一页，因为筛选条件变化后应该从第一页开始查询
  pagination.current = 1;
  // 自动触发查询
  await fetchDeviceData();
};

// 检查问题类型是否选中
const isProblemTypeSelected = (value: string) => {
  return filters.problemType.includes(value);
};

// 切换问题类型筛选
const toggleProblemTypeFilter = async (value: string) => {
  const index = filters.problemType.indexOf(value);
  if (index === -1) {
    filters.problemType.push(value);
  } else {
    filters.problemType.splice(index, 1);
  }

  // 重置分页到第一页，因为筛选条件变化后应该从第一页开始查询
  pagination.current = 1;
  // 自动触发查询
  await fetchDeviceData();
};

// 设备类型全选
const selectAllDeviceTypes = async () => {
  filters.deviceType = deviceTypeOptions.map(option => option.value);
  // 重置分页到第一页，因为筛选条件变化后应该从第一页开始查询
  pagination.current = 1;
  // 自动触发查询
  await fetchDeviceData();
};

// 设备类型全不选
const deselectAllDeviceTypes = async () => {
  filters.deviceType = [];
  // 重置分页到第一页，因为筛选条件变化后应该从第一页开始查询
  pagination.current = 1;
  // 自动触发查询
  await fetchDeviceData();
};

// 设备类型反选
const invertDeviceTypeSelection = async () => {
  const allValues = deviceTypeOptions.map(option => option.value);
  filters.deviceType = allValues.filter(value => !filters.deviceType.includes(value));
  // 重置分页到第一页，因为筛选条件变化后应该从第一页开始查询
  pagination.current = 1;
  // 自动触发查询
  await fetchDeviceData();
};

// 问题类型全选
const selectAllProblemTypes = async () => {
  filters.problemType = problemTypeOptions.map(option => option.value);
  // 重置分页到第一页，因为筛选条件变化后应该从第一页开始查询
  pagination.current = 1;
  // 自动触发查询
  await fetchDeviceData();
};

// 问题类型全不选
const deselectAllProblemTypes = async () => {
  filters.problemType = [];
  // 重置分页到第一页，因为筛选条件变化后应该从第一页开始查询
  pagination.current = 1;
  // 自动触发查询
  await fetchDeviceData();
};

// 问题类型反选
const invertProblemTypeSelection = async () => {
  const allValues = problemTypeOptions.map(option => option.value);
  filters.problemType = allValues.filter(value => !filters.problemType.includes(value));
  // 重置分页到第一页，因为筛选条件变化后应该从第一页开始查询
  pagination.current = 1;
  // 自动触发查询
  await fetchDeviceData();
};

// 设备类型选项 - 根据实际数据更新
const deviceTypeOptions = [
  { label: '宽带接入设备', value: '宽带接入设备' },
  { label: '新城', value: '新城' },
  { label: 'DSLAM设备', value: 'DSLAM设备' },
  { label: 'OLT设备', value: 'OLT设备' },
  { label: 'AG设备', value: 'AG设备' },
  { label: 'ER设备', value: 'ER' },
  { label: 'SR设备', value: 'SR' },
  { label: 'DSW设备', value: 'DSW设备' },
  { label: 'IDC交换机', value: 'IDC交换机' }
];

// 客户范围选项
const userNumRangeOptions = [
  { label: '500-1000', value: '500-1000' },
  { label: '1000-2000', value: '1000-2000' },
  { label: '2000-4000', value: '2000-4000' }
];

// 处理大客户OLT复选框变更
const handleImportantOltChange = async (checked: boolean) => {
  filters.isImportantOlt = checked;

  // 如果取消选中，清空客户范围
  if (!checked) {
    filters.userNumRange = '';
  }

  // 重置分页到第一页
  pagination.current = 1;

  // 自动触发查询
  await fetchDeviceData();
};

// 处理客户范围变更
const handleUserNumRangeChange = async (value: string) => {
  filters.userNumRange = value;

  // 重置分页到第一页
  pagination.current = 1;

  // 自动触发查询
  await fetchDeviceData();
};



// 表格列定义
const columns = computed(() => {
  const baseColumns = [
    { title: '设备名称', dataIndex: 'deviceName', key: 'deviceName', width: 160, fixed: 'left' },
    { title: '设备编码', dataIndex: 'deviceCode', key: 'deviceCode', width: 250 },
    { title: '设备类型', dataIndex: 'deviceType', key: 'deviceType', width: 120 },
    // { title: 'IP地址', dataIndex: 'ipAddress', key: 'ipAddress', width: 140 },
    { title: '区域', dataIndex: 'region', key: 'region', width: 100 },
    { title: '问题类型', dataIndex: 'problemType', key: 'problemType', width: 120 },
    { title: '问题数量', dataIndex: 'errorCount', key: 'errorCount', width: 100 }
  ];

  // 如果是大客户OLT，添加用户数量列
  if (filters.isImportantOlt) {
    baseColumns.push({ title: '用户数量', dataIndex: 'userNum', key: 'userNum', width: 100 });
  }

  // 添加剩余的列
  baseColumns.push(
    // { title: '上联端口数', dataIndex: 'portCount', key: 'portCount', width: 100 },
    // { title: '上联光路数', dataIndex: 'opticalPathCount', key: 'opticalPathCount', width: 100 },
    { title: '发现时间', dataIndex: 'discoveryTime', key: 'discoveryTime', width: 160 },
    { title: '操作', dataIndex: 'action', key: 'action', fixed: 'right', width: 100 }
  );

  return baseColumns;
});



// 定义设备数据类型
interface DeviceData {
  key: string;
  deviceName: string;
  deviceCode: string;
  deviceType: string;
  ipAddress: string;
  region: string;
  problemType: string;
  errorCount: number; // 问题数量
  portCount: number;
  opticalPathCount: number;
  discoveryTime: string;
  userNum?: number; // 用户数量，大客户OLT专用
  oltSpec?: string; // OLT规格，大客户OLT专用
}

// 问题设备数据
const problemDevices = ref<DeviceData[]>([]);

// 分页配置
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 处理分页变化
const handlePaginationChange = (page: number, pageSize: number) => {
  console.log('分页变化 - 页码:', page, '每页大小:', pageSize);
  pagination.current = page;
  pagination.pageSize = pageSize;
  fetchDeviceData();
};

// 选中的行
const selectedRowKeys = ref<string[]>([]);

// 处理表格变化
const handleTableChange = (pagination: any, filters: any, sorter: any) => {
  console.log('表格变化 - 分页:', pagination, '筛选:', filters, '排序:', sorter);
  handlePaginationChange(pagination.current, pagination.pageSize);
};

// 切换高级筛选
const toggleAdvancedFilter = () => {
  showAdvancedFilter.value = !showAdvancedFilter.value;
};

// 重置筛选条件
const resetFilters = async () => {
  Object.assign(filters, {
    deviceType: [] as string[],
    problemType: [...defaultProblemTypes], // 默认选中所有问题类型
    region: '',
    deviceName: '',
    deviceCode: '',
    ipAddress: '',
    managementStatus: '',
    portCount: [null, null] as (number | null)[],
    opticalPathCount: [null, null] as (number | null)[],
    errorCount: null as number | null, // 问题数量筛选
    cityCode: defaultCityCode, // 使用默认地市代码
    isImportantOlt: false, // 重置大客户OLT复选框
    userNumRange: '' // 重置客户范围
  });

  // 重置后自动刷新数据
  await fetchDeviceData();
};

// 应用筛选条件
const applyFilters = async () => {
  console.log('应用筛选条件:', filters);
  // 重置分页到第一页，因为筛选条件变化后应该从第一页开始查询
  pagination.current = 1;
  await fetchDeviceData();
};

// 从API获取设备数据
const fetchDeviceData = async () => {
  console.log('当前分页状态:', pagination);
  loading.value = true; // 开始加载前设置loading状态为true
  try {
    // 使用用户选择的地市代码，如果用户没有选择，则使用默认地市代码
    const cityCode = filters.cityCode || defaultCityCode;

    // 构建查询参数
    const params: Record<string, any> = {
      // 设备类型筛选 - 使用deviceSpecs字段，作为数组传递
      deviceSpecs: filters.deviceType.length > 0 ? filters.deviceType : undefined,
      // 单个设备类型筛选 - 如果只选了一个设备类型，使用deviceSpec字段
      deviceSpec: filters.deviceType.length === 1 ? filters.deviceType[0] : undefined,
      // 问题类型筛选 - 使用errors字段，作为数组传递
      errors: filters.problemType.length > 0 ? filters.problemType : undefined,
      // 问题数量筛选
      error_count: filters.errorCount !== null ? filters.errorCount : undefined,
      // 关键字搜索 - 用于设备名称、设备编码和IP的模糊查询
      keyword: (filters.deviceName || filters.deviceCode) ? (filters.deviceName || filters.deviceCode) : undefined,
      // 区域名称
      areaName: filters.region || undefined,
      // 不再使用 cityName 参数，而是通过 shardingcode 传递城市信息
      // 其他参数
      managementStatus: filters.managementStatus || undefined,
      minPortCount: filters.portCount[0] !== null ? filters.portCount[0] : undefined,
      maxPortCount: filters.portCount[1] !== null ? filters.portCount[1] : undefined,
      minOpticalPathCount: filters.opticalPathCount[0] !== null ? filters.opticalPathCount[0] : undefined,
      maxOpticalPathCount: filters.opticalPathCount[1] !== null ? filters.opticalPathCount[1] : undefined,

      scenarioType: 'singleDeviceDualUplink', // 指定场景类型为单设备双上联
      // 添加分页参数 - 确保使用正确的参数名称
      pageSize: pagination.pageSize,
      currentPage: pagination.current,
      // 添加备用参数名称，以防后端使用不同的参数名称
      page: pagination.current,
      pageNum: pagination.current,
      size: pagination.pageSize
    };

    // 如果选择了大客户OLT，添加相关参数
    if (filters.isImportantOlt && filters.deviceType.includes('OLT设备')) {
      params.isImportantOlt = true;
      if (filters.userNumRange) {
        params.oltSpec = filters.userNumRange;
      }
    }

    // 移除undefined的属性
    Object.keys(params).forEach(key => params[key] === undefined && delete params[key]);

    // 打印完整的请求参数
    console.log('发送请求的参数:', params);

    try {
      console.log('发送API请求到:', '/graph-rest-api/api/apiconverge/query');
      console.log('使用的城市代码:', cityCode);
      console.log('构建的shardingCode:', `ds_bc_o3_${cityCode.toLowerCase()}`);

      const result = await unifiedApi.querySlyDevices(cityCode, params);
      console.log('API返回结果:', result);

      // 检查API返回的数据结构
      if (result) {
        // 转换API返回的数据为组件需要的格式
        const transformedData = transformDeviceData(result);

        console.log('转换后的数据:', transformedData);

        // 只有当转换后的数据不为空时才更新
        if (transformedData && transformedData.length > 0) {
          problemDevices.value = transformedData;

          // 打印设备类型信息，便于调试
          const deviceTypes = transformedData.map(item => item.deviceType);
          const uniqueDeviceTypes = [...new Set(deviceTypes)];
          console.log('转换后的设备类型:', uniqueDeviceTypes);

          // 更新分页信息
          if (result.pageInfo) {
            console.log('服务器返回的分页信息:', result.pageInfo);
            // 只更新当前页和总数，不更新每页大小，避免覆盖用户设置
            pagination.current = result.pageInfo.currentPage || pagination.current;
            pagination.total = result.pageInfo.totalCount || 0;
          }

          message.success('数据加载成功');
          return; // 成功获取数据，提前返回
        } else {
          console.warn('API返回的数据为空或转换后为空');
          // 显示空数据提示
          problemDevices.value = [];
          pagination.total = 0;
          message.info('没有找到符合条件的数据');
          return; // 返回空数据，提前返回
        }
      } else {
        console.error('API返回的数据结构不符合预期:', result);
        message.error('获取设备数据失败: 返回的数据结构不符合预期');
      }
    } catch (apiError) {
      console.error('API调用出错:', apiError);
      message.error('API调用出错: ' + (apiError.message || '未知错误'));
    }

    // 如果执行到这里，说明API调用失败或返回的数据无效
    problemDevices.value = [];
    pagination.total = 0;
    message.info('暂无数据，请检查API调用是否正确');
  } catch (error) {
    console.error('获取设备数据出错:', error);
    message.error('获取设备数据出错: ' + (error.message || '未知错误'));

    problemDevices.value = [];
    pagination.total = 0;
    message.info('暂无数据，请检查API调用是否正确');
  } finally {
    // 无论成功还是失败，都将loading状态设置为false
    loading.value = false;
  }
};

// 转换API返回的数据为组件需要的格式
const transformDeviceData = (apiData: any): DeviceData[] => {
  console.log('转换前的数据:', apiData);
  console.log('数据类型:', Array.isArray(apiData) ? 'Array' : typeof apiData);
  console.log('数据长度:', Array.isArray(apiData) ? apiData.length : 0);

  let dataArray: any[] = [];

  // 如果数据不是数组，尝试转换
  if (!Array.isArray(apiData)) {
    console.warn('返回的数据不是数组，尝试转换...');
    try {
      if (apiData && typeof apiData === 'object' && 'data' in apiData) {
        console.log('从对象中提取data属性');
        const extractedData = (apiData as any).data;
        console.log('提取后的数据:', extractedData);
        console.log('提取后的数据类型:', Array.isArray(extractedData) ? 'Array' : typeof extractedData);

        if (Array.isArray(extractedData)) {
          dataArray = extractedData;
        }
      }
    } catch (error) {
      console.error('转换数据时出错:', error);
      return [];
    }
  } else {
    dataArray = apiData;
  }

  // 添加设备类型映射函数，确保与筛选条件中的设备类型一致
  const mapDeviceType = (type: string) => {
    // 如果类型为空，返回空字符串
    if (!type) return '';

    // 检查类型是否已经在选项中
    const isInOptions = deviceTypeOptions.some(option => option.value === type);
    if (isInOptions) return type;

    // 如果不在选项中，尝试映射
    const typeMap: Record<string, string> = {
      '新城交换机': '新城',
      // 可以根据需要添加更多映射
    };

    return typeMap[type] || type; // 如果没有映射，返回原始类型
  };

  // 如果API返回的error字段不为空，则使用该值
  if (dataArray.some((item: any) => item.error)) {
    return dataArray.map((item: any, index: number) => ({
      key: item.device_id || String(index + 1),
      deviceName: item.device_name || '',
      deviceCode: item.device_code || '',
      deviceType: mapDeviceType(item.device_spec || ''),
      ipAddress: item.ip || '',
      region: item.area_name || item.city_name || '',
      problemType: item.error || '', // 使用API返回的error字段
      errorCount: item.error_count || 0, // 使用API返回的error_count字段
      portCount: 0, // 暂时设置为0，后续可以根据实际数据调整
      opticalPathCount: 0, // 暂时设置为0，后续可以根据实际数据调整
      discoveryTime: item.create_date ? new Date(item.create_date).toLocaleString() : new Date().toLocaleString(),
      userNum: parseInt(item.user_num) || 0, // 用户数量
      oltSpec: item.olt_spec || '' // OLT规格
    }));
  }

  // 如果API返回的error字段为空，则使用默认值“无问题”
  return dataArray.map((item: any, index: number) => ({
    key: item.device_id || String(index + 1),
    deviceName: item.device_name || '',
    deviceCode: item.device_code || '',
    deviceType: mapDeviceType(item.device_spec || ''),
    ipAddress: item.ip || '',
    region: item.area_name || item.city_name || '',
    problemType: '无问题', // 暂时设置为“无问题”
    errorCount: 0, // 暂时设置为0，后续可以根据实际数据调整
    portCount: 0, // 暂时设置为0，后续可以根据实际数据调整
    opticalPathCount: 0, // 暂时设置为0，后续可以根据实际数据调整
    discoveryTime: item.create_date ? new Date(item.create_date).toLocaleString() : new Date().toLocaleString(),
    userNum: parseInt(item.user_num) || 0, // 用户数量
    oltSpec: item.olt_spec || '' // OLT规格
  }));
};



// 直接使用查询结果，不再进行前端筛选
const filteredDevices = computed(() => problemDevices.value);

// 选择行变化
const onSelectChange = (keys: string[]) => {
  selectedRowKeys.value = keys;
};

// 批量处理
const batchProcess = () => {
  console.log('批量处理选中的设备:', selectedRowKeys.value);
};

// 查看设备问题详情 - 打开新页面展示问题详情清单
const viewDeviceDetail = (record: any) => {
  console.log('查看设备问题详情:', record);

  // 获取当前城市代码，使用用户选择的地市代码
  const cityCode = filters.cityCode || defaultCityCode;

  // 将设备数据作为查询参数传递给详情页面
  router.push({
    path: '/nrm/dualrouting/device-err-detail',
    query: {
      deviceId: record.key,
      deviceCode: record.deviceCode, // 添加设备编码
      deviceName: record.deviceName,
      deviceType: record.deviceType,
      problemType: record.problemType,
      scenarioType: 'singleDeviceDualUplink',
      cityCode: cityCode // 添加城市代码
    }
  });
};



// 获取问题类型颜色
const getProblemTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    '缺端口': 'red',
    '缺光路': 'red',
    '异常光路': 'orange',
    '同光缆': 'orange',
    '无穿管': 'blue',
    '同管道': 'orange',
    '无板卡': 'red',
    '同板卡': 'orange'
  };
  return colorMap[type] || 'default';
};



// 组件挂载后初始化
onMounted(async () => {
  // 初始化操作
  // 加载初始数据
  await fetchDeviceData();
});
</script>

<style lang="less" scoped>
.city-info-text {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
}
.single-device-dual-uplink-container {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  display: flex;
  align-items: baseline;
}

.page-title h1 {
  font-size: 20px;
  font-weight: 500;
  margin: 0;
  color: #1f1f1f;
}

.page-title .subtitle {
  font-size: 14px;
  color: #666;
  margin-left: 12px;
}

.scenario-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 32px;
  padding: 24px 16px;
  background: linear-gradient(to right, #f9f9f9, #ffffff, #f9f9f9);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.scenario-card-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  width: 22%;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1;
  }

  &.active {
    border-color: #0066ff;
    background-color: #f0f7ff;

    &::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-top: 8px solid #0066ff;
    }

    .card-icon {
      background-color: #0066ff;
    }

    .card-text {
      color: #0066ff;
      font-weight: 600;
    }

    .card-description {
      color: #0066ff;
    }
  }
}

.card-icon {
  width: 48px;
  height: 48px;
  background-color: #f0f2f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.card-svg {
  width: 22px;
  height: 22px;
  color: #333;
}

.card-text {
  font-size: 15px;
  color: #333;
  text-align: center;
  font-weight: 600;
  margin-bottom: 8px;
  white-space: nowrap;
}

.card-description {
  font-size: 12px;
  color: #666;
  text-align: center;
  line-height: 1.4;
  max-width: 90%;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 34px;
}



.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
}

.title-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  color: #0066ff;
}

.page-subtitle {
  margin: 8px 0 0;
  color: #666;
  font-size: 14px;
}

.scenario-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.scenario-description-card,
.scenario-diagram-card,
.filter-card,
.device-list-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.device-list-card {
  display: flex;
  flex-direction: column;
}

.problem-device-table {
  flex: 1;
  height: 500px;
}

:deep(.ant-table-wrapper) {
  height: 100%;
}

:deep(.ant-spin-nested-loading) {
  height: 100%;
}

:deep(.ant-spin-container) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.ant-table) {
  flex: 1;
  overflow: hidden;
}

:deep(.ant-table-container) {
  height: 100%;
}

:deep(.ant-table-body) {
  overflow: auto !important;
}

:deep(.ant-table-placeholder) {
  height: 100%;
}

:deep(.ant-empty-normal) {
  margin: 64px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.card-header h2 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #333;
  display: flex;
  align-items: center;
}

.header-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  color: #0066ff;
}

.criteria-list {
  margin-top: 16px;
  background: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
}

.criteria-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.criteria-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: #0066ff;
}

.criteria-list h3 {
  font-size: 15px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.criteria-list ul {
  margin: 0;
  padding-left: 20px;
}

.criteria-list li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.diagram-container {
  height: 400px;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  background-color: #fafafa;
}

.filter-card {
  margin-bottom: 24px;
}

.important-olt-section {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed #e8e8e8;
  display: flex;
  align-items: center;
}

.important-olt-checkbox {
  font-weight: 500;
  color: #333;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.filter-header h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.advanced-filter-button {
  display: flex;
  align-items: center;
  background-color: #f0f7ff;
  border-color: #d6e8ff;
  color: #0066ff;
}

.filter-section {
  margin-bottom: 16px;
}

.filter-header-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-right: 12px;
}

.filter-quick-actions {
  display: flex;
  gap: 8px;
}

.action-tag {
  cursor: pointer;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  color: #666;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
  transition: all 0.3s;
}

.action-tag:hover {
  color: #1890ff;
  border-color: #1890ff;
  background-color: #e6f7ff;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-tag {
  cursor: pointer;
  user-select: none;
  font-size: 13px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.filter-tag:hover {
  transform: translateY(-2px);
}

.advanced-filters {
  margin-top: 16px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin-bottom: 16px;
}

.range-input {
  display: flex;
  align-items: center;
}

.range-separator {
  margin: 0 8px;
  color: #999;
}

.filter-actions {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px dashed #e8e8e8;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-header h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
}

.button-icon {
  width: 16px;
  height: 16px;
}

.city-info-text {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
}
</style>
