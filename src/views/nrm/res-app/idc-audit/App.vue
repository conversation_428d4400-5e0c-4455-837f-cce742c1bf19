<template>
  <div class="app-container">
    <div class="main-container">
      <!-- 左边模块 -->
      <div class="chat-container">
        <!-- 左右回复模块 -->
        <div class="messages-container" ref="messagesContainer">
          <div class="message-empty" v-if="messages.length === 1">
            <img src="@/assets/images/idcAudit/left-robot1.png" style="width: 48px; height: 48px;" />
            <span>我是IDC设备日志稽核智能助手，很高兴见到你！</span>
          </div>
          <div class="messages-list" v-else-if="messages.length > 1">
            <!-- @view-report="isReportOpen = true" -->
            <chat-message v-for="(message, index) in messages" :key="index" :role="message.role"
              :thinkMsg="message.thinkMsg"
              :think="message.think"
              :content="message.content" :cacheJson="message.cacheJson" :askLoading='message.askLoading'
              @view-report="handleViewReport(index,$event)" :is-active="activeIndex === index"
            />
          </div>
        </div>
        <!-- Removed agent-container as the dropdown will be moved to the input area -->
        <!-- input输入模块 -->
        <div class="input-container">
          <div class="input-wrapper">
            <textarea
              ref="inputElement"
              v-model="inputMsg"
              @keydown.enter.prevent="handleSend"
              @focus="handleInputFocus"
              @blur="handleInputBlur"
              @click="handleInputClick"
              @input="updateSuggestionsVisibility"
              placeholder="输入您的查询，例如：请帮忙查看全省IDC设备违规操作风险"
              class="message-input"
            />

            <!-- 场景问题建议列表 -->
            <div
              v-if="showSuggestions"
              class="suggestions-container"
              :style="getSuggestionsPosition()"
            >
              <div class="suggestions-header">常用场景问题：</div>
              <div
                v-for="(question, index) in scenarioQuestions"
                :key="index"
                class="suggestion-item"
                @click="selectScenarioQuestion(question)"
              >
                {{ question }}
              </div>
            </div>

            <div class="input-controls">
              <a-select v-model:value="agentType" class="agent-type-select">
                <a-select-option value="port">IDC综合稽核</a-select-option>
                <a-select-option value="portReport">IDC风险操作稽核</a-select-option>
              </a-select>

              <button @click="handleSend" class="send-button" v-if="asking == 0">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                       stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="m22 2-7 20-4-9-9-4Z" />
                    <path d="M22 2 11 13" />
                  </svg>
              </button>

              <button @click="stopAsk" class="send-button" v-if="asking == 1">
                  <svg xmlns="http://www.w3.org/2000/svg"
                       width="24"
                       height="24"
                       viewBox="0 0 24 24"
                       fill="none"
                       stroke="currentColor"
                       stroke-width="2">
                    <!-- 中间停止方块 -->
                    <rect x="6" y="6" width="12" height="12" fill="white" rx="1"/>
                  </svg>
              </button>
            </div>

          </div>
        </div>
      </div>
      <!-- <report-panel :report="currentReport" @close="isReportOpen = false" /> -->
      <report-panel v-if="isReportOpen" :report="currentReport" @close="isReportOpen = false" />
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted, onBeforeUnmount, computed } from 'vue';
import ChatMessage from './components/ChatMessage.vue';
import ReportPanel from './components/ReportPanel.vue';
import { generateAuditReport } from './utils/auditReport';

import { useInfo, usePageQuery } from '@/hooks/web/useRestAPI';
import { defHttp } from '@/utils/http/axios';
import { useGlobSetting } from '@/hooks/setting';

const activeIndex = ref(-1);

const agentType = ref('port');

const asking = ref(0); // 表示是在等回复

// 响应式状态
const messages = ref([
  {
    role: 'assistant',
    content: '您好，我是IDC设备日志稽核智能助手。请问您需要什么帮助？',
    thinkMsg: ''
  },
]);
const inputMsg = ref('');   //输入的信息
const isReportOpen = ref(false);
// const currentReport = ref(null);
const currentReport = ref({});
const messagesContainer = ref(null);

// 场景问题相关状态
const showSuggestions = ref(false);
const inputFocused = ref(false);
const inputElement = ref(null);

// 预设的场景问题
const scenarioQuestions = [
  '请分析近6个月总操作数，复核前后违规操作比例，并绘制折线图显示违规操作比例的趋势。',
  '请分析近1个月的违规操作，显示复核前后的稽核结果、风险类型、操作人、操作人所在部门.',
  '请分析近3个月每个人的总操作数、复核前后违规操作比例。',
  '请分析近3个月每个部门的总操作数，复核前后违规操作比例。',
];

//查询接口使用
const custViewInfoService = useInfo({
  rootPath: '/graph-rest-api',
  info: {},
});
const { info } = custViewInfoService;

let reportObj = ref({});

// 生命周期钩子
onMounted(() => {
  scrollToBottom();
});

// 组件卸载前
onBeforeUnmount(() => {

});

function handleViewReport(index,objJson) {
  activeIndex.value = index;
  openReport(objJson);
}

function openReport(objJson) {
  console.log(111, objJson);
  queryOperationAuditCount(objJson);   //展示右侧使用
  queryOperationAuditDetail(objJson);   //展示右侧使用2
}

// 查询左侧回复消息
async function idcAsk(userQuery) {
  let secondMark = new Date().getTime();
  info.value = {
    content: userQuery
  };

  messages.value.push({
    role: 'assistant',
    content: '',
    askLoading: true,
    secondMark: secondMark,
    cacheJson: null
  });


  // custViewInfoService.getInfo   custViewInfoService.doCreateNew

  try {
    let res = await custViewInfoService.doCreateNew(
      `/api/idc/ask`,
    );

    if (res.code === '0') {
      currentReport.value = { ...reportObj.value };
      res['地市'] = res['地市'] ? res['地市'] : [];
      // res['地市'] =  [];
      idcAuditStatistics(res, secondMark);   //展示左侧使用
      queryOperationAuditCount(res);   //展示右侧使用
      queryOperationAuditDetail(res);   //展示右侧使用2
    } else {
      // 替换加载进度条内容
      for (let index = 0; index < messages.value.length; index++) {
        const element = messages.value[index];
        if (element.secondMark === secondMark) {
          element.askLoading = false
          element.content = res.msg
        }
      }
      //
      // messages.value.push({
      //   role: 'assistant',
      //   content: res.msg,
      //   askLoading: false,
      // });
      // 添加助手消息后再次滚动到底部
      nextTick(scrollToBottom);
    }
  } catch (e) {
    for (let index = 0; index < messages.value.length; index++) {
      const element = messages.value[index];
      if (element.secondMark === secondMark) {
        // messages.value.splice(index, 1)
        element.content = '请求失败';
        element.askLoading = false;
      }
    }
  } finally {
    for (let index = 0; index < messages.value.length; index++) {
      const element = messages.value[index];
      if (element.secondMark === secondMark) {
        element.askLoading = false;
      }
    }
  }

};




// 调用websocket接口
async function idcAskWebsocket(userQuery) {
  let secondMark = new Date().getTime();
  info.value = {
    content: userQuery
  };
  messages.value.push({
    role: 'assistant',
    content: '',
    think: false,
    thinkMsg: '',
    askLoading: true,
    secondMark: secondMark,
    askParam: null ,// 用户请求的参数解析V3
    cacheJson: {} // 解析的参数保存
  });
  nextTick(() => {
    scrollToBottom()
  })


  try {
    if (!isConnected.value) {
      initWebSocket()
    } else {
      sendMessage(info.value.content)
    }


  } catch (e) {
    console.log(e)
    finishCurrentMsg('请求失败')

  }

};


/*
function idcAsk_test(userQuery) {
  let res = {
    data: {
      "截至时间": "2025-03-18",
      "code": "0",
      "开始时间": "2025-03-18",
      "地市": ""
    }
  };
  currentReport.value = { ...reportObj.value };
  idcAuditStatistics(res.data);   //展示左侧使用
  queryOperationAuditCount(res.data);   //展示右侧使用
  queryOperationAuditDetail(res.data);   //展示右侧使用2
};
*/

// 查询左侧统计信息
async function idcAuditStatistics(jsonObject, id) {
  info.value = jsonObject;
  // custViewInfoService.getInfo   custViewInfoService.doCreateNew
  let res = await custViewInfoService.doCreateNew(
    `/api/idc/idcAuditStatistics`,
  );


  // messages.value.forEach
  for (let index = 0; index < messages.value.length; index++) {
    const element = messages.value[index];
    if (element.secondMark === id) {
      element.askLoading = false
      element.content = res.result
      element.cacheJson = jsonObject
    }
  }

  // messages.value.push({
  //   role: 'assistant',
  //   // content: reportObj.summary,
  //   // content: '扣税的速度很快',
  //   content: res.result,
  //   cacheJson: jsonObject,
  //   askLoading: false,
  // });
  // 添加助手消息后再次滚动到底部
  nextTick(scrollToBottom);

  activeIndex.value = messages.value.length -1 ;    //高亮最新的消息，也就是最下面的消息

}

function constructStatisticInfo(res) {

}

// 查询右侧总体
async function queryOperationAuditCount(jsonObject) {
  info.value = jsonObject;
  // custViewInfoService.getInfo   custViewInfoService.doCreateNew
  let res = await custViewInfoService.doCreateNew(
    `/api/audit/queryOperationAuditCount`,
  );
  console.log('右侧1', res);
  // 设置报告数据
  currentReport.value.overall = res.data;
  currentReport.value.jsonObject = jsonObject;
  isReportOpen.value = true;
}

// 查询右侧总体
async function queryOperationAuditDetail(jsonObject) {
  info.value = jsonObject;
  // custViewInfoService.getInfo   custViewInfoService.doCreateNew
  let res = await custViewInfoService.doCreateNew(
    `/api/audit/queryOperationAuditDetail`,
  );
  console.log('右侧2', res);
  // 设置报告数据
  currentReport.value.detailArr = res.data;
  isReportOpen.value = true;
}

const stopAsk = () => {
  sendMessage('<end>') // 后端接收该内容同时也关闭通道
  finishCurrentMsg('用户请求取消')
  closeWebSocket()
}

// 发送消息
const handleSend = async () => {
  if (!inputMsg.value.trim()) return;
  if(asking.value == 1) return ; // 正在问答,无需处理

  // 添加用户消息
  messages.value.push({ role: 'user', content: inputMsg.value,thinkMsg: '',think:false });
  const userQuery = inputMsg.value;
  inputMsg.value = '';

  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  });
  idcAskWebsocket(userQuery);

};

// 滚动到底部
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

// 处理输入框焦点
const handleInputFocus = () => {
  inputFocused.value = true;
  updateSuggestionsVisibility();
};

// 处理鼠标点击事件
const handleInputClick = () => {
  inputFocused.value = true;
  updateSuggestionsVisibility();
};

const handleInputBlur = () => {
  // 延迟隐藏建议，以便用户可以点击建议项
  setTimeout(() => {
    inputFocused.value = false;
    updateSuggestionsVisibility();
  }, 200);
};

// 更新建议显示状态
const updateSuggestionsVisibility = () => {
  showSuggestions.value = inputFocused.value && inputMsg.value.trim() === '';
};

// 计算建议框位置
const getSuggestionsPosition = () => {
  if (!inputElement.value) {
    return { left: '0px', top: '0px' };
  }

  const rect = inputElement.value.getBoundingClientRect();

  // 2个字符的宽度大约是16px（假设字体大小为14px）
  const leftOffset = 16;

  // 计算位置，确保在视口内
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  const suggestionsWidth = 400; // 建议框的最小宽度

  let left = rect.left + leftOffset;
  let top = rect.bottom + 5;

  // 如果建议框会超出右边界，调整位置
  if (left + suggestionsWidth > viewportWidth) {
    left = viewportWidth - suggestionsWidth - 20; // 留20px边距
  }

  // 如果建议框会超出下边界，显示在输入框上方
  if (top + 200 > viewportHeight) { // 假设建议框高度约200px
    top = rect.top - 205; // 在输入框上方显示
  }

  // 确保不会超出左边界
  if (left < 10) {
    left = 10;
  }

  // 确保不会超出上边界
  if (top < 10) {
    top = rect.bottom + 5; // 还是显示在下方
  }

  const position = {
    left: left + 'px',
    top: top + 'px'
  };
  return position;
};

// 选择场景问题
const selectScenarioQuestion = (question) => {
  inputMsg.value = question;
  showSuggestions.value = false;
  inputFocused.value = false;
};


/***********************以下为websocket调用部分*********************/

const ws = ref(null)
const connectionState = ref(0) // 0: 断开, 1: 连接中, 2: 已连接

const currentAskMsg = ref({ role: 'assistant', content: null,askLoading:false,question:null})
// WebSocket 状态计算属性
const connectionStatus = computed(() => {
  return {
    0: { type: 'danger', text: '已断开' },
    1: { type: 'warning', text: '连接中...' },
    2: { type: 'success', text: '已连接' }
  }[connectionState.value]
})

const showReconnect = computed(() => {
  return connectionState.value === 0
})

const isConnected = computed(() => {
  return connectionState.value === 2
})

// 获取环境变量中的WebSocket URL
const getWsUrl = () => {
  const { wsIdcUrl } = useGlobSetting();
  console.log('WebSocket URL from settings:', wsIdcUrl);
  return wsIdcUrl || '127.0.0.1:1084'; // 默认值为生产环境地址
}

// 初始化 WebSocket 连接
const initWebSocket = () => {
  connectionState.value = 1
  const wsHost = getWsUrl();
  console.log('wsHost', wsHost);
  let url = `ws://${wsHost}/ws-idc`
  if (agentType.value == 'port') {
    url = `ws://${wsHost}/ws-idc-port` // 端口稽核分析
  }
  console.log('WebSocket connecting to:', url);
  ws.value = new WebSocket(url)
  // 连接成功
  ws.value.onopen = () => {
    connectionState.value = 2
    console.log('连接成功')
    if (info.value.content) {
      sendMessage(info.value.content) // 如果有待发送内容，需要进行发送
    }
  }

  // 接收消息
  ws.value.onmessage = (event) => {
    try {
      // console.log('接收...',event.data )
      let contentObj = JSON.parse(event.data) // 约定json格式{content：内容，askParam：解析的参数}
      let content = contentObj.content
      // 拿到终止消息
      if (content && content.trim().indexOf('<end>') >=0) {
        finishCurrentMsg('')
        // 结束才有内容，控制在结束才给按钮点击查看详情
        if (contentObj.askParam) {
          receiveAskParam(contentObj.askParam)
        }
        return
      }
      // console.log(content)
      // 下一条消息
      if (content && content.trim().indexOf('<next>') >=0) {
        messages.value.push({role: 'assistant', content: '', thinkMsg: '', askLoading: true, think:true});
        return
      }
      if (content && content.trim().indexOf('<think>') >=0) {
        // 开始think
        startThink()
        return
      } else if (content && content.trim().indexOf('</think>') >= 0) {
        // 完成think
        endThink()
        return
      }
      receiveCurrentMsg(content)

    } catch (e) {
      finishCurrentMsg('消息解析失败')
      console.error('消息解析失败:', e)
    }
  }

  // 连接关闭
  ws.value.onclose = (event) => {
    connectionState.value = 0
    if (!event.wasClean) {
      console.log(`连接异常断开，原因: ${event.reason}`)
    }
  }

  // 错误处理
  ws.value.onerror = (error) => {
    console.error('WebSocket 错误:', error)
    finishCurrentMsg('服务端发生错误')

  }
}

// 发送消息
const sendMessage = (userMessage) => {
  if (!userMessage.trim() || !isConnected.value) return
  asking.value = 1 // 开始进入问答
  if (ws.value.readyState === WebSocket.OPEN) {
    ws.value.send(userMessage)
  } else {
    finishCurrentMsg('连接未就绪，请稍后重试')
    console.log('连接未就绪，请稍后重试')
  }
}

const closeWebSocket = () => {
  asking.value = 0 // 标记问答终止
  connectionState.value = 0
  ws.value.close(1000, '用户主动关闭连接')
}

const finishCurrentMsg = (content) => {

  messages.value[messages.value.length - 1].askLoading = false
  messages.value[messages.value.length - 1].think = false
  messages.value[messages.value.length - 1].content +=content
  asking.value = 0 ;// 停止问答，可以继续下次问答
  // 兼容内容，如果是```结尾需要去掉，否则会有空行
  let tail = "```"
  // debugger
  if (messages.value[messages.value.length - 1].content &&
      messages.value[messages.value.length - 1].content.endsWith(tail)) {
    messages.value[messages.value.length - 1].content  =
      messages.value[messages.value.length - 1].content.slice(0, -tail.length);
  }
  if (!messages.value[messages.value.length - 1].content) {
    messages.value[messages.value.length - 1].content = '服务端响应异常，请稍后重试'
  }

  closeWebSocket() // 关闭
  nextTick(() => {
    scrollToBottom()
  })
}

/**
 * 用户问的内容解析结果json
 * @param param
 */
const receiveAskParam = (param) => {
  try {
    if (param) {
      messages.value[messages.value.length - 1].cacheJson = JSON.parse(param)
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 接收到的消息正文
 * @param content
 */
const receiveCurrentMsg = (content) => {
  messages.value[messages.value.length - 1].askLoading = false
  if (messages.value[messages.value.length - 1].think) {
    messages.value[messages.value.length - 1].thinkMsg += content
  } else {
    messages.value[messages.value.length - 1].content += content
  }

  messages.value[messages.value.length - 1].content =
    messages.value[messages.value.length - 1].content.replace('```markdown','').replace('[答案]','')
  if (messages.value[messages.value.length - 1].content.indexOf('<answer>') >= 0) {
    endThink()
  }

  if (messages.value[messages.value.length - 1].content.indexOf('<action>') >= 0) {
    startThink()
  }


  nextTick(() => {
    scrollToBottom()
  })
}

const startThink = ()=>{
  messages.value[messages.value.length - 1].content  = ''
  messages.value[messages.value.length - 1].think = true
}
const endThink = ()=>{
  messages.value[messages.value.length - 1].think = false
}
// 组件生命周期
onBeforeUnmount(() => {
  if (ws.value) {
    ws.value.close()
  }
})
</script>


<style scoped lang="less">
.app-container {
  display: flex;
  // height: 100vh;
  background-color: #f9fafb;

  flex-grow: 1;
  height: 0;
  min-height: 0;

}

/* .app-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
} */

/* .app-header {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: #ffffff;
}

.app-header h1 {
  font-size: 1.25rem;
  font-weight: bold;
} */


.main-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.chat-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  background: linear-gradient(to bottom, #f5f9ff, #fafbff);
}

.messages-container {
  flex: 1;
  padding: 1rem;
  overflow: auto;
}
.agent-container{
  padding: 1rem;
  overflow: auto;
}

.message-empty {
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
  margin-top: -10px;

  span {
    font-size: 24px;
    font-weight: bold;
    color: #000000;
    margin-left: 10px;
  }
}

.messages-list {
  // max-width: 48rem;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.input-container {
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
  background-color: #ffffff;
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  // gap: 0.5rem;
  // max-width: 48rem;
  margin: 0 auto;

  height: 120px;
  position: relative;
}

.input-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: absolute;
  bottom: 0.8rem;
  width: 100%;
}

.agent-type-select {
  width: 180px;
  margin-left: 0.5rem;
}

.suggestions-container {
  position: fixed;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  min-width: 400px;
  max-width: 600px;
}

.suggestions-header {
  padding: 0.75rem 1rem 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #f3f4f6;
}

.suggestion-item {
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
  font-size: 0.875rem;
  line-height: 1.4;
  color: #4b5563;
}

.suggestion-item:hover {
  background-color: #f9fafb;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.message-input {
  flex: 1;
  // padding: 0.5rem 0.75rem;
  // border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  outline: none;

  padding: 0.5rem 0.5rem 0.5rem 0.5rem;
  line-height: 1.2rem;
  border: 2px solid #4B88FA;
}

.message-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;

  padding: 0.5rem 1rem;
  border-radius: 1rem;
  background: linear-gradient(133deg, #44BCFF 22.34%, #4B82F9 91.16%);
  margin-right: 1rem;
}

.send-button:hover {
  background-color: #1d4ed8;
}

.send-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}
</style>
