<template>
  <PageWrapper>



    <div class="search-box" >
      <img src="../../../../assets/images/errorSearch.png" />
      <a-space class="search-form">
        <a-flex justify="space-around" align="center">
          
        <a-select style="width: 5vw" 
        :disabled="cityUnchangable"
        v-model:value="city" maxTagCount="13" @change="() => {
            queryComplaint();

            pantoCity(MapContainer, city);

            ClearDraw();

            changeExternalForceLayerShow();
          }
          ">
          <a-select-option value="yz">扬州</a-select-option>
          <a-select-option value="nj">南京</a-select-option>
          <a-select-option value="wx">无锡</a-select-option>
          <a-select-option value="sz">苏州</a-select-option>
          <a-select-option value="cz">常州</a-select-option>
          <a-select-option value="tz">泰州</a-select-option>
          <a-select-option value="ha">淮安</a-select-option>
          <a-select-option value="nt">南通</a-select-option>
          <a-select-option value="lyg">连云港</a-select-option>
          <a-select-option value="sq">宿迁</a-select-option>
          <a-select-option value="yc">盐城</a-select-option>
          <a-select-option value="xz">徐州</a-select-option>
          <a-select-option value="zj">镇江</a-select-option>
        </a-select>
        <span style="margin-left: 20px">报障接入号:</span>
        <a-input :disabled="mainLoading" v-model:value="accs_nbr_nos" placeholder="报障接入号" allow-clear
          style="width: 50vw" />
        <a-button type="primary" :icon="h(SearchOutlined)" class="float-right-button" @click="() => {
            mainFunction(accs_nbr_nos, accs_nbr_nos_normal, city);
          }
          " :loading="mainLoading">查询</a-button>
        <!-- <a-button class="other-button" type="dashed" @click="showDrawer" style="margin-left: 1vw;">其他下载</a-button> -->

      </a-flex>
      </a-space>
    </div>
    <div class="optical-box">
    <div class="title-header">宽带申告汇聚 <span style="cursor: pointer;float: right;"
        @click="() => { complaintFlag = !complaintFlag }">
        <DownOutlined v-if="complaintFlag" />
        <RightOutlined v-else />
      </span></div>

      <div v-if="complaintFlag"  id="宽带申告查询">
        
        <a-row>
          <a-col span="6">

            <div style="overflow-y: auto;">
              <a-card style="height: 650px;" title="汇聚结果">
                
                <a-tabs  centered>
                  <a-tab-pane key="1" >

                    <template #tab>
                      <span>地区汇聚&申告数</span>
                      <a-tooltip placement="top">
                        <template #title>
                          <p>申告数超过10个会显示红色，5个以上显示橙色，5个以下显示绿色</p>
                        </template>
                        <QuestionCircleTwoTone style="margin-left: 5px" />

                      </a-tooltip>
                    </template>

                  
                    <a-list 
                    style="height: 400px;overflow-y: auto;"
                    item-layout="horizontal" 
                    :data-source="ComplaintListData" :pagination="{
                      position: 'bottom',
                      hideOnSinglePage: true
                      }" size="small"
                                      :loading="queryComplaintloading || mainLoading">

                      <template #renderItem="{ item }">


                        <a-list-item>

                          <a-list-item-meta>
                            <template #title>

                     

                              <a-checkbox 
                              @change="()=>{


                                  console.log('item checked',item.checked);


                              }"

                              v-model:checked="item.checked">



                              <a 
                              :style="{ color: linkisActive === item[0] ? 'red' : 'blue' }"  

                              @click.prevent="() => {
                                // 筛选申告表
                                filterComplaintTableData(item);

                                // 修改输入框内容

                                complatinToAccsnbrno();

                                //开展群障定位

                                mainFunction(accs_nbr_nos, accs_nbr_nos_normal, city);

                                //开展申告上图

                              }">{{ item[0] }}

                                &nbsp;&nbsp;&nbsp;
                                <a-badge :count="item[1]"
                                  :number-style="{ backgroundColor: item[1] > 10 ? 'red' : item[1] >= 5 ? 'orange' : '#52c41a' }" />

                              </a>

                            </a-checkbox>
                            </template>

                          </a-list-item-meta>
                        </a-list-item>
                      </template>
                    
                    </a-list>

                    <a-flex justify="center">

                      <a-button @click="()=>{
                      console.log('ComplaintListData',ComplaintListData);
                      //todo
                      // 筛选申告表
                      filterMutipleComplaintData(ComplaintListData);
                      // 修改输入框内容
                      complatinToAccsnbrno();
                      //开展群障定位
                      mainFunction(accs_nbr_nos, accs_nbr_nos_normal, city);
                      //开展申告上图
                      }"  >合并查询</a-button>
                    </a-flex>
                  
                  </a-tab-pane>
                  <!-- todo: -->
                  <!-- <a-tab-pane key="2" tab="OLT汇聚申告">

      
                    <a-list 
                    style="height: 530px;overflow-y: auto;"
                    item-layout="horizontal" :data-source="mngIpAddrAggregated" :pagination="{
                      position: 'bottom',
                      pageSize: 30,
                      hideOnSinglePage: true
                      }" size="small"
                                      :loading="queryComplaintloading || mainLoading">

                      <template #renderItem="{ item }">


                        <a-list-item>



                          <a-list-item-meta>
                            <template #title>
                              <a @click.prevent="() => {

                                filterComplaintyAggreatedAccsnbr(item);
                            
                                accs_nbr_nos = item.accs_nbr_nos.join(',');
                                //开展群障定位

                                mainFunction(accs_nbr_nos, accs_nbr_nos_normal, city);

                                //开展申告上图

                              }">{{ item.mng_ip_addr }}

                                &nbsp;&nbsp;&nbsp;
                                <a-badge :count="item.count"
                                  :number-style="{ backgroundColor: item.count > 10 ? 'red' : item.count  >= 5 ? 'orange' : '#52c41a' }" />

                              </a>
                            </template>

                          </a-list-item-meta>
                        </a-list-item>
                      </template>
                    
                    </a-list>


                  </a-tab-pane>
                  <a-tab-pane key="3" tab="机房汇聚申告">

                    <a-list 
                    style="height: 530px;overflow-y: auto;"
                    item-layout="horizontal" :data-source="roomNameAggregated" :pagination="{
                      position: 'bottom',
                      pageSize: 30,
                      hideOnSinglePage: true
                      }" size="small"
                                      :loading="queryComplaintloading || mainLoading">

                      <template #renderItem="{ item }">


                        <a-list-item>



                          <a-list-item-meta>
                            <template #title>
                              <a @click.prevent="() => {

                                filterComplaintyAggreatedAccsnbr(item);

                                //汇聚接入号的结果作为输入
                                accs_nbr_nos = item.accs_nbr_nos.join(',');


                                //开展群障定位

                                mainFunction(accs_nbr_nos, accs_nbr_nos_normal, city);

                                //开展申告上图

                              }">{{ item.room_name }}

                                &nbsp;&nbsp;&nbsp;
                                <a-badge :count="item.count"
                                  :number-style="{ backgroundColor: item.count > 10 ? 'red' : item.count  >= 5 ? 'orange' : '#52c41a' }" />

                              </a>
                            </template>

                          </a-list-item-meta>
                        </a-list-item>
                      </template>
                    
                    </a-list>


                  </a-tab-pane> -->
                </a-tabs>


              </a-card>
            </div>

          </a-col>
          <a-col span="18">

            <a-card title="申告详单" style="height: 650px;">

              <a-flex justify="space-between" align="center" style="margin-bottom: 1vh">
                申告时间：
                <a-range-picker
                  style="width: 400px"
                  show-time
                  format="YYYY/MM/DD HH:mm:ss"
                  :presets="rangePresets"
                  @change="onRangeChange"
                  @ok="queryComplaint"
                  v-model:value="complaintDateRanger"
                  :allowClear="false"
                />
                <span v-show="QueryMode==='realtime'">
                  <a-tooltip placement="top" >
                    <template #title>
                      <p>实时模式下，因接口限制，最多查询跨度7天的申告</p>
                    </template>
                    <QuestionCircleTwoTone style="margin-left: 5px" />
                  </a-tooltip>
                </span>


                <!-- 实时模式才能自动刷新 -->
               
                  <a-button @click="queryComplaint" style="margin-left: 1vw" type="primary">查询宽带申告清单</a-button>
                  &nbsp;&nbsp;
                <div v-show="QueryMode=='realtime'">
                  自动刷新页面：
                  <a-switch v-model:checked="autoRefreshSwitch" checked-children="开" un-checked-children="关" 
                  @change="()=>{
                    if(autoRefreshSwitch){
  
                      startAutoRefresh();
                    }else{
  
                      closeAutoRefresh();
                    }
  
                  }"
                  />
                  <a-input-number id="refreshIntervalMinute" style="width: 5vw; " v-model:value="refreshIntervalMinute" :min="2" :max="100" 
                  @change="startAutoRefresh"
                  v-show="autoRefreshSwitch"
                  addon-after="分钟"
                  />
                
                </div>
               <a-button v-show="QueryMode=='cache'" @click="refreshCache()" :loading="refreshCacheloading">
                刷新缓存
                </a-button>

                <span v-show="QueryMode==='cache'">
                  <a-tooltip placement="top" >
                    <template #title>
                      <p>缓存模式下，因接口限制，最多刷新跨度7天的申告，查询时间则不受限制</p>
                    </template>
                    <QuestionCircleTwoTone style="margin-left: 5px" />
                  </a-tooltip>
                </span>
                <a-radio-group v-model:value="QueryMode" button-style="solid" >
                  <a-radio-button value="cache">缓存模式  
                    <a-tooltip placement="top">
                      <template #title>
                        <p>缓存模式采用5分钟一次缓存到本地的作为数据源，速度较快</p>
                        <p>如果数据没更新，可以尝试手动刷新缓存</p>

                      </template>
                      <QuestionCircleTwoTone style="margin-left: 5px" />
                    </a-tooltip>
                </a-radio-button>
                  <a-radio-button value="realtime">实时模式
                    <a-tooltip placement="top">
                      <template #title>
                        <p>实时模式实时调用百川接口作为数据源，查询申告较慢，且容易受到并发限制</p>
                      </template>
                      <QuestionCircleTwoTone style="margin-left: 5px" />
                    </a-tooltip>
                  </a-radio-button>
                </a-radio-group>
              </a-flex>


              <a-tabs v-model:activeKey="activeKeyComplaint">

                <a-tab-pane key="1" tab="详单"  force-render>
                  <a-table size="small"  :pagination="{  hideOnSinglePage: true }" :columns="complainColumns"
                    :scroll="{ x: 800, y: 350 }" :data-source="complainTableData" rowKey="id" :indentSize="0"
                    class="table-class" :loading="queryComplaintloading">
                    <template #bodyCell="{ text, column, record }">
                      <template v-if="column.key === '详情'">
                        <a @click.prevent="showmodal(record, '宽带申告详情')">详情</a>
                      </template>

                      <span v-if="searchText && searchedColumn === column.dataIndex">
                        <template v-for="(fragment, i) in text
                          .toString()
                          .split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i'))">
                          <mark v-if="fragment.toLowerCase() === searchText.toLowerCase()" :key="i" class="highlight">
                            {{ fragment }}
                          </mark>
                          <template v-else>{{ fragment }}</template>
                        </template>
                      </span>

                    </template>
                    <template #customFilterDropdown="{
                      setSelectedKeys,
                      selectedKeys: keytosearch,
                      confirm,
                      clearFilters,
                      column,
                    }">
                      <div style="padding: 8px">
                        <a-input ref="searchInput" :placeholder="`搜索 ${column.title}`" :value="keytosearch"
                          style="width: 188px; margin-bottom: 8px; display: block"
                          @change="(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])"
                          @press-enter="handleTableSearch(keytosearch, confirm, column.dataIndex)" />
                        <a-button type="primary" size="small" style="width: 90px; margin-right: 8px"
                          @click="handleTableSearch(keytosearch, confirm, column.dataIndex)">
                          <template #icon>
                            <SearchOutlined />
                          </template>
                          搜索
                        </a-button>
                        <a-button size="small" style="width: 90px" @click="handleReset(clearFilters)">
                          重置
                        </a-button>
                      </div>
                    </template>
                    <template #customFilterIcon="{ filtered }">
                      <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
                    </template>


                  </a-table>
             
                </a-tab-pane>
                <a-tab-pane key="2" tab="申告趋势" force-render>
                  <ColumnChart :data="complainTableData"  style="width: 100%;height:400px;"/>

                </a-tab-pane>

              </a-tabs>
             
              <a-modal v-model:open="showmodalopen" :title="modalName" :footer="null" width="1200px">
                <a-descriptions size="small" :column="3" layout="vertical" bordered>
                  <a-descriptions-item v-for="(value, key) in modalinfo" :key="key !== 'user_link_phone' && key !== 'user_link_people' ? key : ''" :label="key !== 'user_link_phone' && key !== 'user_link_people' ? key : ''"
                    :span="key === '工单标题' || key === '告警信息' ? 6 : key === '障碍来源' ? 2 : 1">
                    
                    <span v-if="key !== 'user_link_phone' && key !== 'user_link_people'">{{ value }}</span>


                  </a-descriptions-item>



                </a-descriptions>
              </a-modal>
            </a-card>



          </a-col>
        </a-row>


      </div>
    </div>
   
    <!-- 接入号查光缆段 -->
    <div class="optical-box">
      <div class="title-header">共光缆段&共设备分析 <span style="cursor: pointer;float: right;"
          @click="() => { opticalFlag = !opticalFlag }">
          <DownOutlined v-if="opticalFlag" />
          <RightOutlined v-else />
        </span></div>
      <div v-if="opticalFlag" class="tabs">
        <div :class="['tabs-item', opticalCable == item.id ? 'tabs-item-active' : '']"
          v-for="(item, index) in opticalCableTabs" :key="index" @click="opticalTabsClick(item.id)">{{ item.title }}
        </div>
      </div>
      <div v-if="opticalFlag" style="padding: 16px">
        <div v-if="opticalCable == 1">
          <a-table bordered size="small" class="table-class" :pagination="{   hideOnSinglePage: true }"
            :columns="cbl_sectcolumns" :data-source="CblList" rowKey="code" :indentSize="0" :row-selection="{
              selectedRowKeys: selectedCblSectRowKeys,
              onChange: onCblSectSelectChange,
            }" :loading="getCblloading" :scroll="{ x: 1000, y: 480 }"
            :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'code'">
                <div v-if="Array.isArray(record.code)">
                  <span v-for="(item, index) in record.code" :key="index">
                    <a @click.prevent="handleClick(item)">{{ item }}<br /> </a>
                  </span>
                </div>

                <div v-else-if="!Array.isArray(record.code)">
                  <a @click.prevent="handleClick(record.code )">
                    {{ record.code }}
                  </a>
                </div>
              </template>
              <template v-else-if="column.key === 'doubt_degree'">
                <span>
                  <a-tag :color="record.doubt_degree === '智能推荐' ? 'red' :record.doubt_degree === '高' 
                      ? 'gold'
                      : record.doubt_degree === '中'
                        ? 'blue'
                        : 'green'
                    ">
                    {{ record.doubt_degree }}
                  </a-tag>
                </span>
              </template>
            </template>
          </a-table>
          <a-modal v-model:visible="visible" title="发起公共障碍" :disabled="!hasSelectedCblSect"
            :confirm-loading="makeCauseInfoLoading" @ok="handleOk(causeName, city, devType)" width="40vw">

            <a-alert :message="makecauseDesc" type="warning" show-icon  style="margin-bottom: 1vh;">
              <template #icon><smile-outlined /></template>
            </a-alert>

            <div >
              <a-card>
                请输入障碍名称： <p/>
                 <a-input style="width: 100%" v-model:value="causeName" />

                <p/>
                请输入备注信息：<p/> <a-textarea style="width: 100%" v-model:value="remark2" show-count :maxlength="100" /><p/>

                语音解释内容：
                <p/> <a-textarea style="width: 100%" value="尊敬的用户，您好！您所在地区因突发故障正在抢修当中，期间暂时影响到您的正常使用，请您谅解，并请稍后再试。" 
                show-count :maxlength="100" 
                disabled
                /><p/>


              </a-card>

            </div>

          
            <!-- <a-alert
                        message="Warning"
                        :description="makecauseDesc"
                        type="warning"
                        show-icon
                      /> -->
          </a-modal>
            
          <div>
 
            <a-flex align="center" justify="start" gap="middle" style="margin-top: 1vh;">
              <a-button type="primary" 
                :disabled="!hasSelectedCblSect" @click="() => {
                    showModal('cbl');
                  }
                  ">一键发起公共障碍</a-button>

         
              <a-button :disabled="!hasSelectedCblSect" @click="DownloadSelectedCblSectAccsNos"
                :loading="downloading">下载选中光缆段的关联接入号</a-button>
            

              <span style="margin-left: 1vw">
                <template v-if="hasSelectedCblSect">
                  {{ `选中了 ${selectedCblSectRowKeys.length} 条记录` }}
                </template>
              </span>
            </a-flex>
            
          </div>
        </div>
        <div v-if="opticalCable == 2">

          

          <a-table bordered :loading="getResourceloading" size="small" :pagination="{   hideOnSinglePage: true }"
            :columns="olt_columns" :data-source="oltList" rowKey="olt_ip" class="table-class"
            :scroll="{ x: 1000, y: 480 }" :disabled="!hasSelectedolt"
            :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)">

            <template #bodyCell="{ column,record }">
              <template v-if="column.dataIndex === 'actions'">
                <a-button type="link"  @click="() => {
                  

                  console.log('展示pon口有哪些',record);
                  //TODO:把record里面的ip取出来以后，把poncode用逗号链接起来

                  const olt_ip = record.olt_ip;
                  const pon_code_array = record.olt_subtable.map(item => item.pon_code);
                  const pon_code_str = pon_code_array.join(',');
                   go(`/nrm/res-app/faultpositioning/check-pon?olt_ip=${olt_ip}&pon_code_str=${pon_code_str}&city=${city}`);

                  }
                  " block>
                  批量PON检测
                </a-button>
              </template>
            </template>

            <template #expandedRowRender="{ record }">
              <a-table size="small" :columns="subtable_coloums" :pagination="{   hideOnSinglePage: true }"
                :data-source="record.olt_subtable" rowKey="pon_id" class="table-class" :scroll="{ x: 1000, y: 480 }"
                :row-selection="{
                  selectedRowKeys: selectedponRowKeys,
                  onChange: onponSelectChange,
                }">
                <template #bodyCell="{ record: subtable_record, column }">
                  <template v-if="column.dataIndex === 'actions'">
                    <a-button type="link" :loading="checkPonInfoServiceLoading" @click="() => {
                        ExecutePonCheck(subtable_record, city);
                      }
                      " block>
                      快速PON口检测
                    </a-button>
                  </template>
                </template>
              </a-table>
            </template>
          </a-table>




          <a-button style="margin-top: 1vh;" type="primary" :disabled="!hasSelectedpon"  @click="() => {
              showModal('pon');
            }
            ">一键发起公共障碍</a-button>
        </div>
        <div v-if="opticalCable == 3">
          <a-table bordered :loading="mainLoading" size="small" :pagination="{   hideOnSinglePage: true }"
            :columns="frstobd_columns" :data-source="frstobdList" rowKey="obd_id" class="table-class"
            :scroll="{ x: 1000, y: 480 }" :row-selection="{
              selectedRowKeys: selectedfrstobdRowKeys,
              onChange: onfrstobdSelectChange,
            }" :disabled="!hasSelectedfrstobd"
            :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)">
            <template #expandedRowRender="{ record }">
              <a-table size="small" :columns="obd_subtable_coloums"
                :pagination="{   hideOnSinglePage: true }" :data-source="record.frstobd_subtable"
                :scroll="{ x: 1000, y: 480 }" class="table-class" />
            </template>
          </a-table>

          <a-button style="margin-top: 1vh;" type="primary"  :disabled="!hasSelectedfrstobd"
            @click="() => {
                showModal('frstobd');
              }
              ">一键发起公共障碍</a-button>

          <!-- <a-button type="primary"
                    :disabled="!hasSelectedfrstobd"
                    @click="showModal">一键拦截</a-button>
                    <a-modal
                      v-model:visible="obdvisible"
                      title="一级OBD拦截"
                      :disabled="!hasSelectedfrstobd"
                      :confirm-loading="makeCauseInfoLoading"
                      @ok="handleOk(causeName,city,'obd',selectedfrstobdRowKeys)"
                      width="1000px"
                    >
                      <div>
                        <span>请输入障碍名称:  </span>
                        <a-input style="width: 50%;" v-model:value="causeName" placeholder="大面积障碍名称" />
                      </div>
                    </a-modal> -->
        </div>
        <div v-if="opticalCable == 4">
          <a-table bordered :loading="mainLoading" size="small" :pagination="{   hideOnSinglePage: true }"
            :columns="scndobd_columns" :data-source="scndobdList" rowKey="obd_id" class="table-class"
            :scroll="{ x: 1000, y: 480 }" :row-selection="{
              selectedRowKeys: selectedscndobdRowKeys,
              onChange: onscndobdSelectChange,
            }" :disabled="!hasSelectedscndobd"
            :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)">
            <template #expandedRowRender="{ record }">
              <a-table size="small" :columns="obd_subtable_coloums"
                :pagination="{   hideOnSinglePage: true }" :data-source="record.scndobd_subtable" />
            </template>
          </a-table>

          <a-button style="margin-top: 1vh;" type="primary" :disabled="!hasSelectedscndobd" 
            @click="() => {
                showModal('scndobd');
              }
              ">一键发起公共障碍</a-button>

          <!-- <a-button type="primary"
                          :disabled="!hasSelectedscndobd"
                          @click="showModal">一键拦截</a-button>
                          <a-modal
                            v-model:visible="visible"
                            title="二级OBD拦截"
                            :disabled="!hasSelectedscndobd"
                            :confirm-loading="makeCauseInfoLoading"
                            @ok="handleOk(causeName,city,'obd',selectedscndobdRowKeys)"
                            width="1000px"
                          >
                            <div>
                              <span>请输入障碍名称:  </span>
                              <a-input style="width: 50%;" v-model:value="causeName" placeholder="大面积障碍名称" />
                            </div>
                          </a-modal> -->
        </div>
        <div v-if="opticalCable == 5">
          <a-table :loading="mainLoading" size="small" bordered class="table-class"
            :pagination="{   hideOnSinglePage: true }" :columns="bseSectColumns"
            :data-source="aggregatedData" rowkey="bse_sect_no" :scroll="{ x: 1000, y: 480 }" :row-selection="{
              selectedRowKeys: selectedbseSectRowKeys,
              onChange: onbseSectSelectChange,
            }" :disabled="!hasSelectedbseSect"
            :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)">
            <template #expandedRowRender="{ record }">
              <a-table size="small" :columns="bseSect_subtable_Columns"
                :pagination="{   hideOnSinglePage: true }" rowkey="cbl_sect_no" class="table-class"
                :data-source="record.subtable" />
            </template>
          </a-table>

          <a-alert type="error" v-show="aggregatedData != null" :message="bseSectAlertMessage" banner closable />

          <!-- <div>
                        <a-button type="primary"
                        :disabled="!hasSelectedbseSect"
                        @click="()=>{
                          showModal('bse');
                        }">一键拦截</a-button>

                      </div> -->
        </div>
        <div v-if="opticalCable == 6">
          <div class="card-container" style="border-radius: 0%" ref="relateanalyse">
            <div style="width: 100%;">

              <a-flex gap="small" justify="flex-start" align="flex-start">

                <!-- <a-tooltip placement="top">
                  <template #title>
                    <p>根据支撑段或者光缆段找到重要政企用户纤芯，提醒线维优先修复</p>
                  </template>
                  <QuestionCircleTwoTone style="margin-left: 5px" />

                </a-tooltip> -->
                <a-input-group compact style="margin-left: 5px; margin-bottom: 5px; align-content: center">
                  <a-flex align="center">
                  光缆段编码:
                  <a-input-search v-model:value="relateAnalyseCblSectNos" style="width: 700px" placeholder="请输入光缆段"
                    allow-clear enter-button="查询" @search="() => {
                        if (!relateAnalyseCblSectNos || relateAnalyseCblSectNos.trim() === '') {
                          showalert('请输入需要查询的光缆段编码，用逗号或者空格分隔！');
                          return;
                        }
                        const relateAnalyseCblSectNos_list = splitItemsBySeparators(relateAnalyseCblSectNos);
                        ExecuteGetCblSectEffect(relateAnalyseCblSectNos_list, city);
                      }
                      " :loading="getCblSectEffectInfoServiceLoading" type="primary" />
                  </a-flex>
                </a-input-group>
                <!-- <a-input-group compact>

                  支撑段编码:

                  <a-input-search v-model:value="relateAnalyseBseSectNos" style="width: 700px" placeholder="请输入支撑段"
                    allow-clear enter-button="查询" @search="() => {
                        if (!relateAnalyseBseSectNos || relateAnalyseBseSectNos.trim() === '') {
                          showalert('请输入需要查询的支撑段编码，用逗号或者空格分隔！');
                          return;
                        }
                        const relateAnalyseBseSectNos_list =
                          splitItemsBySeparators(relateAnalyseBseSectNos);
                        ExecuteGetPipeEffect(relateAnalyseBseSectNos_list, city);
                      }
                      " :loading="getPipeEffectInfoServiceLoading" type="primary" />

                </a-input-group>
                -->

              </a-flex>

            </div>
          </div>

          <a-table size="small" bordered :loading="mainLoading || getPipeEffectInfoServiceLoading || getCblSectEffectInfoServiceLoading" :pagination="{ hideOnSinglePage: true }"
            :dataSource="relatedEnterpriseData" :columns="relatedEnterpriseColumns" class="table-class"
            :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
            :scroll="{ x: 1000, y: 400 }">
            <template #bodyCell="{ column, record }">


              <template v-if="column.key === 'lineNo'">
                <span>
                  <a-tag color="red">
                    {{ record.lineNo }}
                  </a-tag>
                </span>
              </template>
            </template>
            <!-- <template #bodyCell="{ record,column }">
                        <template v-if="column.dataIndex === 'actions'">
                          <a-button  type="link"  :loading="checkPonInfoServiceLoading" @click="{
                            ExecutePonCheck(record,city);
                          }" block>
                            PON口检测
                          </a-button>
                        </template>
                      </template> -->
          </a-table>
        </div>

        <div v-if="opticalCable == 7">

          <h1 >根据OLT的ip关联障碍单</h1>
          <a-table
            :columns="order_columns"
            :dataSource="tablefaultOrderOlt"
            bordered
            :loading="queryfaultordernowunionhisloading"
            size="small"
            class="ant-table-striped"
             :scroll="{ x: 1000, y: 480 }"
            :pagination="{ hideOnSinglePage: true }"
            :row-class-name="(_record, index) => {
              // 获取派单时间
              const orderTime = new Date(_record.派单时间);
              // 获取当前时间的日期部分（00:00:00）
              const todayStart = new Date();
              todayStart.setHours(0, 0, 0, 0);
              // 如果派单时间不是今天00点以后，则返回'dark-grey'类名
              return orderTime < todayStart ? 'historyOrder' : (index % 2 === 1 ? 'table-striped' : null);
            }"
          >
            <template #bodyCell="{ record, column }">
              <div>
                {{ column.dataIndex ? record[column.dataIndex] : '' }}
              </div>
            </template>
          </a-table>
          <h1 >根据分光器的编码关联障碍单</h1>
          <a-table
            :columns="order_columns"
            :dataSource="tablefaultOrderObd"
            bordered
            :loading="queryFaultOrderByTitleloading"
            size="small"
            class="table-class"
             :scroll="{ x: 1000, y: 480 }"
            :pagination="{ hideOnSinglePage: true }"
            :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)">
          >
            <template #bodyCell="{ record, column }">
              <div>
                {{ column.dataIndex ? record[column.dataIndex] : '' }}
              </div>
            </template>
          </a-table>

        </div>
      </div>
    </div>
    <div class="optical-box">
      <div class="title-header">gis地图和逻辑图分析
        <span style="cursor: pointer;float: right;" @click="() => { gisFlag = !gisFlag }">
          <DownOutlined v-if="gisFlag" />
          <RightOutlined v-else />
        </span>
      </div>
      
      <div class="gis-box" v-show="gisFlag" style="padding: 16px">
       
        <div v-show="gisCable == 1">
          <!-- <a-card title="gis地图定位" class="flex-card" :style="{ width: '100%' }"> -->
          <div style="display: none">
            <div ref="popupHtmlelement">
              <!-- <a-button type="primary" @click="test1">popupcontent</a-button> -->

              <a-table size="small" :pagination="{   hideOnSinglePage: true }" :columns="popuptableColumns"
                :data-source="popuptableData" rowKey="code" bordered class="table-class" :scroll="{ x: 600, y: 300 }" />

              <div>
                <a-row type="flex" justify="space-around" align="middle" style="margin-top: 1vh">
                  <a-col :span="16">
                    <span style="
                              text-align: center;
                              font-size: medium;
                              vertical-align: middle;
                              justify-content: center;
                              margin-left: 1vw;
                            ">支撑段编码：{{ popupcontent.bse_sect_no }}</span>
                  </a-col>
                  <a-col :span="8">
                    <!-- <a-button @click="() => {
                        TableCollapseactiveKey = ['1'];
                        // activeKey = 'relateanalyse';
                        relatedEnterpriseData = [];
                        relateAnalyseBseSectNos = popupcontent.bse_sect_no;
                        const relateAnalyseBseSectNos_list =
                          splitItemsBySeparators(relateAnalyseBseSectNos);
                        // activeKey = 'relateanalyse';
                        ExecuteGetPipeEffect(relateAnalyseBseSectNos_list, city);
                      }
                      " :loading="getPipeEffectInfoServiceLoading" size="small"
                      style="margin-right: 1vw">影响政企用户查询</a-button> -->
                  </a-col>
                </a-row>

                <!-- 显示支撑段影响的接入号清单 -->
                <!-- <div>
                        支撑段影响的接入号清单：
                        <span v-if="popupcontent.accsNosArray.length > 0">
                          {{ popupcontent.accsNosArray.join(', ') }}
                        </span>
                        <span v-else>无</span>
                      </div>   -->
              </div>

              <!-- <a-button
                              style="margin-left: 1vw"
                              @click="
                              () => {

                                relateAnalyseBseSectNos = popupcontent.bse_sect_no;
                                const relateAnalyseBseSectNos_list  = splitItemsBySeparators(relateAnalyseBseSectNos);
                                ExecuteDrawBseSectImpactArea(relateAnalyseBseSectNos_list,city);

                              }
                              "
                              :loading="getPipeEffectInfoServiceLoading"
                              type="primary"
                                >绘制支撑段影响范围</a-button> -->
            </div>
          </div>

          <div >
            
            <a-row style="position: relative;">
              
              <div class="fixed-order1">
                <div class="tabs" v-if="gisFlag">
                  <div :class="['tabs-item', gisCable == item.id ? 'tabs-item-active' : '']" v-for="(item, index) in gisCableTabs"
                    :key="index" @click="gisTabsClick(item.id)">
                    <span>
                      <global-outlined v-if="item.id == 1" />
                      <partition-outlined v-else />
                    </span>
                    &nbsp;{{ item.title }}
                  </div>
                </div>
              </div>

              <div class="fixed-order">
                
                <div style="width: 100%; height: 100%">
                  <div class="fixed-header"><img src="../../../../assets/images/glorder.png" alt="">&nbsp;光缆段清单 <span
                      style="cursor: pointer;float: right;color:#000;" @click="() => { orderFlag = !orderFlag }">
                      <DownOutlined v-if="orderFlag" />
                      <RightOutlined v-else />
                    </span></div>
                  <div class="fixed-table" v-if="orderFlag">
                    <a-table :loading="mainLoading" size="small" class="table-class" bordered :pagination="false"
                      :columns="cbl_sectcolumns_simplify" :data-source="CblList" rowKey="code" :indentSize="0"
                      :scroll="{ x: 400, y: 400 }" :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)
                        ">
                      <template #bodyCell="{ column, record }">
                        <template v-if="column.key === 'code'">
                          <div v-if="Array.isArray(record.code)">
                            <span v-for="(item, index) in record.code" :key="index">
                              <a @click.prevent="handleClick(item)">{{ item }}<br /> </a>
                            </span>
                          </div>

                          <div v-else-if="!Array.isArray(record.code)">
                            <a @click.prevent="handleClick(record.code )">
                              {{ record.code }}
                            </a>
                          </div>
                        </template>
                        <template v-else-if="column.key === 'doubt_degree'">
                          <span>
                            <a-tag :color="record.doubt_degree === '智能推荐' ? 'red' :record.doubt_degree === '高' 
                                ? 'gold'
                                : record.doubt_degree === '中'
                                  ? 'blue'
                                  : 'green'
                              ">
                              {{ record.doubt_degree }}
                            </a-tag>
                          </span>
                        </template>
                      </template>
                    </a-table>
                  </div>

                </div>
              </div>

              <!-- <a-switch v-model:checked="ExternalForceLayerchecked" @change="() => {
                changeExternalForceLayerShow();
              }"> </a-switch> 外力点图层 -->
              <a-col span="24">

               

                <!-- <div id="mapRef" style="width: 100%; height: 80vh; border: black 1px"></div> -->
                <div class="w-full max-w-600 gradient-transparency" >

                <div id="mapRef" style="width: 100%; height: 700px; border: black 1px"
                  class="w-full max-w-600  bg-white-500 gradient-transparency z-0"></div>
                </div>
                <a-button 
                :loading="mapprintloading"
                @click="manualPrint()" style="position: absolute ;bottom: 4vh;right:2vw;z-index:600;">
                  地图下载
                </a-button>
              </a-col>
            </a-row>
          </div>
        </div>
        <div v-if="gisCable == 2">
          <div >
            
            <a-row style="position: relative;">
                
              <div class="fixed-order1">
                <div class="tabs" v-if="gisFlag">
                  <div :class="['tabs-item', gisCable == item.id ? 'tabs-item-active' : '']" v-for="(item, index) in gisCableTabs"
                    :key="index" @click="gisTabsClick(item.id)">
                    <span>
                      <global-outlined v-if="item.id == 1" />
                      <partition-outlined v-else />
                    </span>
                    &nbsp;{{ item.title }}
                  </div>
                </div>
              </div>

              <a-col :span="24">
          <div ref="container" class="card-container" style="width: 100%; height: 700px; background-color: white">
            
            <!-- <a-card   title="逻辑图谱分析" class="flex-card" :style="{ width: '100%' }"> -->
            <svg ref="svgRef">&nbsp;</svg>

            <!-- </a-card> -->
          </div>
        </a-col>
          
        </a-row>
      </div>
        </div>
      </div>
    </div>
    <!-- 抽屉 -->
    <a-drawer v-model:visible="Drawervisible" class="custom-class" size="large" style="color: red" title="导出其他光缆段的接入号"
      placement="right" @after-visible-change="afterDrawerVisibleChange">
      <!-- 光缆段查其他的接入号 -->
      <div class="card-container" style="border-radius: 0%">
        <div style="width: 100%">
          <a-typography-title>光缆段查影响业务</a-typography-title>
          <a-input-group compact style="margin-left: 5px; margin-bottom: 5px">
            <a-input v-model:value="cbl_sect_nos" style="width: 50%" allow-clear />


            <a-button style="margin-left: 1vw" @click="() => {
                const cbl_sect_no_list = splitItemsBySeparators(cbl_sect_nos);

                downloadOtherCblSectAccsNos(cbl_sect_no_list, city);
              }
              " :loading="downloading">下载</a-button>
          </a-input-group>
        </div>


      </div>

      <!-- 支撑段关联的重要政企客户清单查询 -->
      <div class="card-container" style="border-radius: 0%">
        <div style="width: 100%">
          <a-typography-title>支撑段查影响业务</a-typography-title>
          <a-input-group compact style="margin-left: 5px; margin-bottom: 5px">
            <a-input v-model:value="bse_sect_nos" style="width: 50%" allow-clear />

            <a-button style="margin-left: 1vw" @click="() => {
                if (!bse_sect_nos || bse_sect_nos.trim() === '') {
                  showalert('请输入支撑段清单，用逗号或者空格分隔！');
                  return;
                }

                const bse_sect_no_list = splitItemsBySeparators(bse_sect_nos);

                DownloadEnterpriseCustomerByBseSectNos(bse_sect_no_list, city);
              }
              " :loading="downloading">下载</a-button>
          </a-input-group>
        </div>
      </div>
    </a-drawer>
    <!-- <a-card style="width: 100%; margin-top: 20px">
      <a-space direction="vertical">
        <a-collapse
          v-model:activeKey="TableCollapseactiveKey"
          collapsible="header"
          style="background: rgb(255, 255, 255)"
        >
          <a-collapse-panel key="1" header="共光缆段&共设备分析">

          </a-collapse-panel>
        </a-collapse>
        <a-collapse
          v-model:activeKey="GraphCollapseactiveKey"
          collapsible="header"
          style="background: rgb(255, 255, 255)"
        >
          <a-collapse-panel key="1" header="gis地图和逻辑图分析">
            <a-tabs
              class="tabs-class"
              v-model:activeKey="GraphAndMapActiveKey"
              @change="
                (key: any) => {
                  if (key == 'logic_graph') {
                    nextTick(() => {
                      drawGraph();
                    });
                  }
                }
              "
            >
              <a-tab-pane key="gis_map">
                <template #tab>
                  <span>
                    <global-outlined />
                    gis地图定位
                  </span>
                </template>

              </a-tab-pane>

              <a-tab-pane key="logic_graph" force-render>
                <template #tab>
                  <span>
                    <partition-outlined />
                    逻辑图谱分析
                  </span>
                </template>
               
              </a-tab-pane>
            </a-tabs>
          </a-collapse-panel>
        </a-collapse>
      </a-space>
    </a-card> -->

    <br />

 
  </PageWrapper>
</template>


<script lang="ts" setup>
//==================================================导入相关组件和查询接口========================================================================
import { useUserStoreWithOut } from '@/store/modules/user';
import { notification } from 'ant-design-vue';
import { PageWrapper } from '@/components/Page';
import {
  DownOutlined, RightOutlined, QuestionCircleTwoTone, SoundTwoTone, SwapOutlined ,
  SearchOutlined,
  GlobalOutlined,
  PartitionOutlined,
  CloseCircleOutlined,
  CheckCircleOutlined,
  SmileOutlined,
} from '@ant-design/icons-vue';
import { ref, onMounted, nextTick, computed, reactive, h, defineOptions } from 'vue';
import { log } from '@antv/g2plot/lib/utils';

import { useInfo } from '@/hooks/web/useRestAPI';
import useGis from '@/hooks/gis/useGis';
import { useInput } from '@/hooks/input/useInput';
import dayjs, { Dayjs } from 'dayjs';
import { useRouter ,useRoute} from 'vue-router';

import useCableFaultGraph from '@/hooks/graph/useCableFaultGraph';
import type { FlexProps } from 'ant-design-vue';
import { AI as Ai } from '@/utils';
// import { center } from '@antv/x6/lib/registry/node-anchor/bbox';
import ColumnChart from './chart/ColumnChart.vue';  
import { any, string } from 'vue-types';

import { useGo } from '@/hooks/web/usePage';


const go = useGo();



// const props = defineProps({

//   accs_nbr_nos: {  
//     type: String,  
//     default: '141451034,141342977,140950246,148913448,148350913,140604949,140396252',  
//   }

// });

const route = useRoute();
const accs_nbr_nos = ref(''); 
const city = ref<string>('yz');

if(route.query.accs_nbr_nos){

  console.log('带参数查询route.query.accs_nbr_nos',route.query.accs_nbr_nos);
  
  accs_nbr_nos.value= route.query.accs_nbr_nos;

}else{

  accs_nbr_nos.value =  '141451034,141342977,140950246,148913448,148350913,140604949,140396252';
}

if(route.query.city){
  city.value= route.query.city;
  console.log('初始赋值city',route.query.city);

  console.log('city.value',city.value);

  
}else{

  city.value='yz';
}


console.log('初始赋值accs_nbr_nos',accs_nbr_nos.value);








const QueryMode = ref('realtime');
const complaintDateRanger = ref([dayjs().add(-6, 'h'), dayjs()]);
const onRangeChange = (dates: RangeValue, dateStrings: string[]) => {
  if (dates) {
    console.log('From: ', dates[0], ', to: ', dates[1]);
    console.log('From: ', dateStrings[0], ', to: ', dateStrings[1]);
  } else {
    console.log('Clear');
  }

  queryComplaint();
};
type RangeValue = [Dayjs, Dayjs];


const rangePresets = ref([
{ label: '最近1小时', value: [dayjs().add(-1, 'h'), dayjs()] },
{ label: '最近2小时', value: [dayjs().add(-2, 'h'), dayjs()] },
{ label: '最近6小时', value: [dayjs().add(-6, 'h'), dayjs()] },
{ label: '最近12小时', value: [dayjs().add(-12, 'h'), dayjs()] },
{ label: '最近24小时', value: [dayjs().add(-24, 'h'), dayjs()] },
{ label: '最近3天', value: [dayjs().add(-3, 'd'), dayjs()] },
{ label: '最近7天', value: [dayjs().add(-7, 'd'), dayjs()] },
{ label: '预订时间', value: [dayjs('2024-10-05 15:00:00'), dayjs('2024-10-05 18:00:00')] }


]);
defineOptions({
  name: 'groupfault',
});
// =======
const showalert = (info) => {
  alert(info);
};
// >>>>>>> 9bffaa835009223fc1f1545af23564320d8a18de:src/views/nrm/res-app/groupfault/index.vue

//弹窗相关的对象，一个是弹窗的div，一个弹窗里面的表格的data
const popupHtmlelement = ref();
const popupcontent = ref<any>({
  cable_no: '12345', // 光缆编码
  accsNosArray: ['pqc1', 'pqc2', 'pqc3'], // 接入号数组
});
const opticalCableTabs = ref([
  { id: 1, title: '光缆段分组分析' },
  { id: 2, title: 'OLT关联分析' },
  { id: 3, title: '一级OBD关联分析' },
  { id: 4, title: '二级OBD关联分析' },
  // { id: 5, title: '管道段关联分析' },
  { id: 6, title: '政企用户纤芯查询（抢修专用）' },
  { id: 7, title: '关联综调障碍单' },

]);
const gisCableTabs = ref([
  { id: 1, title: 'GIS地图定位' },
  { id: 2, title: '逻辑图谱分析' },
])
const opticalCable = ref(1);
const gisCable = ref(1);
const popuptableData = ref([
  {
    cbl_sect_no: '12345',
    accs_nos: ['pqc1', 'pqc2', 'pqc3'],
  },
  {
    cbl_sect_no: '12345',
    accs_nos: ['pqc1', 'pqc2', 'pqc3'],
  },
]);

//入参初始化
// const router = useRouter();

// 
//入参赋值

const accs_nbr_nos_normal = ref('');
const cbl_sect_nos = ref<string>('ASQ.CYJYA/ZGG27/09(GT001-ODF001）');
const bse_sect_nos = ref<string>('ASQ-GDD-NL00/#8201-NL00/#6001-92482');
const relateAnalyseBseSectNos = ref<string>('ASQ-GDD-NL00/#9843-NL00/#9844-76119');
const relateAnalyseCblSectNos = ref<string>('ASQ.MQXQJ/ZGG16/03(GT001-GJ003)');

const popUpGroup = { popuptableData, popupcontent, popupHtmlelement };

//导入gis
let {
  // drawMap,
  showCoordinates,
  getAk,
  getRandomColorRGB,
  drawDuplicatedBseSectFacility,
  drawEquipments,
  drawEquipmentsMapLine,
  drawBseSectFacility,
  pantoCity,
  addPulseIcon
} = useGis(popUpGroup);

const { splitItemsBySeparators, flattenCableSegmentNumbers } = useInput();

//==================================================查询接口相关变量和方法========================================================================
//接口服务变量
const getCblinfoService = useInfo({
  rootPath: '/graph-rest-api',
});
const getAccsNosinfoService = useInfo({
  rootPath: '/graph-rest-api',
});
const getResourceinfoService = useInfo({
  rootPath: '/graph-rest-api',
});
const DrawBseSectinfoService = useInfo({
  rootPath: '/graph-rest-api',
});
const DownloadSelectedCblSectAccsNosinfoService = useInfo({
  rootPath: '/graph-rest-api',
  responseType: 'blob',
});

const DownloadEnterpriseCustomerByBseSectNosInfoService = useInfo({
  rootPath: '/graph-rest-api',
});

const getPipeEffectInfoService = useInfo({
  rootPath: '/graph-rest-api',
});
const getCblSectEffectInfoService = useInfo({
  rootPath: '/graph-rest-api',
});
const checkPonInfoService = useInfo({
  rootPath: '/graph-rest-api',
});
const bseEqptoBseSectInfoService = useInfo({
  rootPath: '/graph-rest-api',
});

const makeCauseInfoService = useInfo({
  rootPath: '/graph-rest-api',
});

const groupFaultLogInfoService = useInfo({
  rootPath: '/graph-rest-api',
});

const UserAreainfoService = useInfo({
  rootPath: '/graph-rest-api',
});
const { info: UserAreainfo, loading: UserArealoading } = UserAreainfoService;


const complaintFlag = ref(true)

const opticalFlag = ref(true)
const gisFlag = ref(true)
const orderFlag = ref(true)
const { info: getCblinfo,loading: getCblloading } = getCblinfoService;
const { info: getResourceinfo,getResourceloading } = getResourceinfoService;
const { info: getAccsNosinfo, loading: getAccsNosloading } = getAccsNosinfoService;
const { info: drawBseSectinfo } = DrawBseSectinfoService;
const { info: DownloadSelectedCblSectAccsNosinfo } = DownloadSelectedCblSectAccsNosinfoService;
const { info: DownloadEnterpriseCustomerByBseSectNosInfo } =
  DownloadEnterpriseCustomerByBseSectNosInfoService;
const { info: getPipeEffectInfo, loading: getPipeEffectInfoServiceLoading } =
  getPipeEffectInfoService;
const { info: getCblSectEffectInfo, loading: getCblSectEffectInfoServiceLoading } =
  getCblSectEffectInfoService;
const { info: checkPonInfo, loading: checkPonInfoServiceLoading } = checkPonInfoService;
const { info: bseEqptoBseSectInfo } = bseEqptoBseSectInfoService;
const { info: makeCauseInfo, loading: makeCauseInfoLoading } = makeCauseInfoService;
const { info: groupFaultLogInfo } = groupFaultLogInfoService;
const mainLoading = ref<boolean>(false);

//接口返回结果变量
const getCblinfoResult = ref();
const getResourceinfoResult = ref();
const getAccsNosinfoResult = ref();
const ponCheckinfoResult = ref();
const drawBseSectinfoResult = ref();
const bseEqptoBseSectInfoResult = ref();
const makeCauseInfoResult = ref();
const groupFaultLogInfoResult = ref();

groupFaultLogInfo;
const bseSectData: any = ref([]);
const aggregatedData: any = ref(null);
const furthestBseSectId = ref('some-bse-sect-id');

const highlightCbl: any = ref([]);
let maxBseSectIds;
let intersectionArray;

let cblSectLinermap;
let bseSectLinermap; //支撑段的ID映射的图层set
// let accsNoLinermap;

let map;

const bseSectAlertMessage = computed(() => {
  if (!aggregatedData.value) {
    return '数据尚未加载'; // 数组为空，返回加载提示
  } else {
    const furthestSect = drawBseSectinfoResult.value.bse_sect_list.find(
      (sect) => sect.bse_sect_id === furthestBseSectId.value,
    );
    const furthestSectNo = furthestSect?.bse_sect_no || '未知编号';

    if (aggregatedData.value.length == 1) {
      const sectCount = aggregatedData.value[0]?.bse_sect_no?.length || 0;
      const accCount =
        aggregatedData.value[0]?.subtable?.reduce(
          (total, { accs_nos }) => total + accs_nos?.length,
          0,
        ) || 0;

      return `一共有${sectCount}个管道段影响了${accCount}个接入号，其中距离用户最近的是${furthestSectNo}`;
    } else {
      let messages: any = [];

      messages.push(`一共有${aggregatedData.value.length}组管道段`);

      aggregatedData.value.forEach((data, index) => {
        const sectCount = data?.bse_sect_no?.length || 0;
        const accCount =
          data?.subtable?.reduce((total, { accs_nos }) => total + (accs_nos?.length || 0), 0) || 0;
        console.log(accCount);

        // 生成每组数据的消息
        messages.push(`第${index + 1}组管道段中一共有${sectCount}个管道段`);
      });
      // 将所有消息连接成一个字符串，并在最后添加关于最近管道段的信息
      return messages.join('。\n') + `。其中所有管道段里面距离用户最近的是${furthestSectNo}`;
    }
  }
});

let MapContainer;

//表格的data变量
const CblList = ref([]);
const oltList = ref([]);
const frstobdList = ref([]);
const scndobdList = ref([]);
//接口方法
const relatedEnterpriseData = ref();

//获取同光缆段
const ExecuteGetCblinfo = async (accs_nbr_nos, city, isGroup: boolean) => {
  getCblinfo.value = { access_code: accs_nbr_nos, city: city, isGroup: isGroup };
  getCblinfoResult.value = await getCblinfoService.doCreateNew('/api/accs_nbr_no/obs_location_v2');

  if (JSON.stringify(getCblinfoResult.value.allAccessCodeFiberCableSegments) === '{}') {

    notification.open({
      message: `该接入号未查到业务信息`,
      duration: 2,
      icon: () => h(CloseCircleOutlined, { style: 'color: red' }),
    });
    return;
  }
  treeData.value = getCblinfoResult.value.topo;

  if (isGroup) {
    //如果查的是报障接入号，isGroup传的是true：
    CblList.value = getCblinfoResult.value.segment;

    // if (Array.isArray(CblList.value) && CblList.value.length > 0) {
    //     // 使用map()方法提取每个对象的code属性
    //     highlightCbl.value = CblList.value.map(item => item.code);
    // }

    const sameAccesscodeSegments = getCblinfoResult.value.sameAccesscodeSegments;
    console.log(sameAccesscodeSegments);
    const allAccessCodeFiberCableSegments = getCblinfoResult.value.allAccessCodeFiberCableSegments;




    console.log('segment', CblList.value);

    // 遍历CblList中的每个分组
    CblList.value.forEach((group) => {
      // 为每个分组初始化一个children数组

      let children = [];
      console.log('children', children);
      group.children = [];
      if (!group.access_code_normal) {
        group.access_code_normal = []; // 初始化数组
      }

      // 遍历分组的access_code数组
      group.access_code.forEach((accessCode) => {
        // 检查allAccessCodeFiberCableSegments中是否有对应的接入号
        if (Object.prototype.hasOwnProperty.call(allAccessCodeFiberCableSegments, accessCode)) {
          // 如果有，将对应的值添加到children数组中

          const fiberCables = allAccessCodeFiberCableSegments[accessCode];
          fiberCables.forEach((fiberCable) => {
            // 创建新的child对象
            let newChild = {
              code: fiberCable.code,
              name: fiberCable.name,
              access_code: [accessCode], // 初始包含要添加的access_code
              access_code_normal: [],
            };

            // 查找所有具有相同code的现有child对象
            let existingChildrenWithSameCode = group.children.filter(
              (child) => child.code === newChild.code,
            );

            if (existingChildrenWithSameCode.length > 0) {
              // 如果找到具有相同code的child对象，则合并access_code数组
              let mergedAccessCodes = new Set();

              // 添加现有child的access_code到集合中
              existingChildrenWithSameCode.forEach((child) => {
                child.access_code.forEach((accessCode) => mergedAccessCodes.add(accessCode));
                if (!child.access_code_normal) {
                  child.access_code_normal = []; // 初始化数组
                }
              });

              // 添加新的access_code到集合中
              newChild.access_code.forEach((accessCode) => mergedAccessCodes.add(accessCode));

              // 更新第一个找到的child的access_code数组
              existingChildrenWithSameCode[0].access_code = Array.from(mergedAccessCodes);

              // 如果需要，可以删除重复的child对象，只保留一个
              if (existingChildrenWithSameCode.length > 1) {
                group.children = group.children.filter((child) => {
                  if (child.code !== newChild.code) return true; // 保留code不同的child
                  return (
                    group.children.indexOf(child) === existingChildrenWithSameCode[0].indexOf(child)
                  ); // 只保留第一个找到的具有相同code的child
                });
              }

              // 注意：在这个逻辑中，我们没有将newChild推送到children数组中，
              // 因为我们已经更新了现有的具有相同code的child的access_code数组。
            } else {
              // 如果没有找到具有相同code的child对象，则将newChild推送到children数组中
              group.children.push(newChild);
            }
          });
        }
      });
      function mergeCodesWithSameAccessCode(children) {
        const map = new Map();
        const namemap = new Map();

        // 遍历children数组，按access_code分组code
        children.forEach((child) => {
          const accessCode = child.access_code.join(','); // 将access_code数组转换为字符串作为键
          if (!map.has(accessCode)) {
            map.set(accessCode, []);
            namemap.set(accessCode, []);
          }
          map.get(accessCode).push(child.code); // 将code添加到对应的数组中
          namemap.get(accessCode).push(child.name); // 将code添加到对应的数组中
        });

        // 将分组的code合并回新的children数组
        const mergedChildren: any = [];
        map.forEach((codes, accessCodeString) => {
          const accessCodeArray = accessCodeString.split(','); // 将键转换回数组
          const newchildtoadd = {
            code: codes, // code现在是一个数组
            access_code: accessCodeArray, // access_code也是一个数组
            name: namemap.get(accessCodeString),
            access_code_normal: [],
          };

          mergedChildren.push(newchildtoadd);
        });

        return mergedChildren;
      }
      console.log('合并前:', group.children);
      // group.children = mergeCodesWithSameAccessCode(group.children);
      console.log('合并后:', group.children);
      group.doubt_degree = '离用户最近';

      function rankDoubtDegree(group) {
        // 创建一个副本，以免修改原始数据
        const childrenCopy = JSON.parse(JSON.stringify(group.children));

        // 为每个child添加length属性，但不修改原始对象
        const childrenWithLength = childrenCopy.map((child, index) => ({
          ...child, // 展开原始对象以保留所有属性
          accessCodeLength: child.access_code ? child.access_code.length : 0,
          originalIndex: index, // 可选，如果需要在后续处理中引用原始索引
        }));

        // 根据accessCodeLength降序排序，长度相同时保持顺序
        childrenWithLength.sort((a, b) => {
          if (a.accessCodeLength === b.accessCodeLength) {
            return a.originalIndex - b.originalIndex;
          }
          return b.accessCodeLength - a.accessCodeLength;
        });

        // 分配doubt_degree
        const doubtDegrees = ['高', '中', '低'];
        let currentDegreeIndex = 0;
        let lastLength = -1;
        childrenWithLength.forEach((child, index) => {
          const length = child.accessCodeLength;
          if (length !== lastLength) {
            currentDegreeIndex = index === 0 ? 0 : currentDegreeIndex + 1;
            lastLength = length;
          }
          // 保证degree索引不会超出范围
          const doubtDegree = doubtDegrees[Math.min(currentDegreeIndex, doubtDegrees.length - 1)];
          // 直接修改当前对象的doubt_degree属性
          child.doubt_degree = doubtDegree;

          if (doubtDegree == '高') {
            highlightCbl.value.push(child.code);
          }
        });

        // 用修改后的对象数组替换原始group的children属性
        group.children = childrenWithLength;

        return group;
      }

      const updatedGroup = rankDoubtDegree(group);
      console.log(updatedGroup);
    });

    // 1. 遍历树形结构并收集child对象
    const allChildren: any = [];
    const collectChildren = function (items) {
      items.forEach((item) => {
        if (item.children) {
          allChildren.push(...item.children);
        }
      });
    };
    collectChildren(CblList.value);

    console.log('@@@@allChildren', allChildren);

    // 2. 排序
    allChildren.sort((a, b) => {
      // 假设doubt_degree可以是"高"、"中"、"低"
      const degrees = { 高: 3, 中: 2, 低: 1 };
      return degrees[b.doubt_degree] - degrees[a.doubt_degree];
    });

    console.log('@@@@aftersorted', allChildren);

    CblList.value = allChildren;


    // //直接对高怀疑度的光缆段进行政企用户查询：
    // if(Array.isArray(CblList.value)){
    //   const cbltogetEnterpriseData = CblList.value[0].code.join();

    //   console.log('cbltogetEnterpriseData',cbltogetEnterpriseData);
    //   relateAnalyseCblSectNos.value = cbltogetEnterpriseData;
    //   const relateAnalyseCblSectNos_list = splitItemsBySeparators(cbltogetEnterpriseData);
    //   ExecuteGetCblSectEffect(relateAnalyseCblSectNos_list, city);

    // }

    //黄总演示用的部分代码
    // const relateAnalyseCblSectNos_list = splitItemsBySeparators(relateAnalyseCblSectNos.value);
    // ExecuteGetCblSectEffect(relateAnalyseCblSectNos_list, city);




    //对第一个结果进行智能推荐打标：
    function processArray(array) {  
        const firstElement = array[0];  
  
        if (firstElement.code.length === 1) {  
            // 如果code只有一条记录，修改doubt_degree为"智能推荐"  
            firstElement.doubt_degree = "智能推荐";  
        } else {  
            // 如果有多条记录，创建新对象并插入数组，同时从原始数组中删除该记录  
            const newElement = {  
                code: [firstElement.code[0]],  
                access_code: [...firstElement.access_code], // 使用展开运算符复制数组  
                name: [firstElement.name[0]],  
                access_code_normal: [...firstElement.access_code_normal], // 使用展开运算符复制数组（即使它是空的）  
                accessCodeLength: firstElement.accessCodeLength,  
                originalIndex: firstElement.originalIndex,  
                doubt_degree: "智能推荐"  
            };  
            array.unshift(newElement);  

            // relateAnalyseCblSectNos.value= newElement.code[0];//智能推荐的光缆段赋值给抢修输入框里面
          
            // 从原始数组中删除被抽取的记录（这里假设我们只删除code数组中的第一个元素）  
            firstElement.code.shift(); // 删除第一个元素  
            firstElement.name.shift(); // 同时删除name数组中的第一个元素以保持一致性  
  
            // 如果删除后code数组为空，你可能还想处理其他逻辑，比如删除整个对象  
            if (firstElement.code.length === 0) {  
                // 可选：如果code数组为空，删除整个对象  
                array.shift(); // 删除第一个元素（即现在的空对象）  
            }  
        }  
    }  
  
    //对第一个结果进行智能推荐打标：
    function processArrayNew(array) {  
        const firstElement = array[0];  
        firstElement.doubt_degree = "智能推荐";  

        // if (firstElement.code.length === 1) {  
        //     // 如果code只有一条记录，修改doubt_degree为"智能推荐"  
        //     firstElement.doubt_degree = "智能推荐";  
        // } 
    }  
  
    // 对其第一个进行智能推荐标签打标
    // processArray(CblList.value);  

    processArrayNew(CblList.value);  


  } else {
    //如果查的是正常接入号，isGroup传的是false：

    //TODO:把CblList的数据加上正常接入号的数量

    const allAccessCodeFiberCableSegments = getCblinfoResult.value.allAccessCodeFiberCableSegments;

    const appendAccessCodes = function (allAccessCodeFiberCableSegmentCode, cblList) {
      // 遍历每个接入号及其对应的光缆段对象
      for (const accessCode in allAccessCodeFiberCableSegmentCode) {
        const fiberCableSegment = allAccessCodeFiberCableSegmentCode[accessCode];

        const segmentCode = fiberCableSegment.map((fiberCable) => fiberCable.code);

        cblList.value.forEach((cblItem) => {
          if (!cblItem.access_code_normal) {
            cblItem.access_code_normal = []; // 初始化数组
          }
          if (segmentCode.includes(cblItem.code)) {
            cblItem.access_code_normal.push(accessCode);
          }
        });

        // 遍历CblList.value数组中的每个元素
        cblList.value.forEach((cblItem) => {
          // 检查children数组是否存在
          if (cblItem.children && Array.isArray(cblItem.children)) {
            // 遍历children数组中的每个child对象
            cblItem.children.forEach((child) => {
              if (!child.access_code_normal) {
                child.access_code_normal = []; // 初始化数组
              }
              // 检查child对象是否有一个code数组属性
              if (child.code && Array.isArray(child.code)) {
                // 检查光缆段编码是否存在于child的code数组中
                if (child.code.some((item) => segmentCode.includes(item))) {
                  // 如果存在，则添加access_code_normal数组（如果不存在），并推送接入号

                  // 避免重复添加相同的接入号
                  if (!child.access_code_normal.includes(accessCode)) {
                    child.access_code_normal.push(accessCode); // 添加接入号到数组中
                  }
                }
              }
            });
          }
        });
      }

      return cblList;
    };

    appendAccessCodes(allAccessCodeFiberCableSegments, CblList);
  }
};

let templiner = null;

const handleClick = (cblcode) => {

  console.log('点击了cblcode',cblcode);
  

  changeBseSectColorFromIds(templiner, cblSectLinermap, 'blue', 0.5);

  if (intersectionArray.length == 0) {
    changeBseSectColorFromIds(maxBseSectIds, bseSectLinermap, 'orange', 0.5);
  } else {
    changeBseSectColorFromIds(intersectionArray, bseSectLinermap, 'orange', 0.5);
  }

  const ischanged = changeBseSectColorFromIds(cblcode, cblSectLinermap, 'red', 0.5);
  if (!ischanged) {
    notification.open({
      message: `${cblcode}该光缆段或支撑段无gis坐标，所以地图上未绘制出来`,
      duration: 2,
      icon: () => h(CloseCircleOutlined, { style: 'color: red' }),
    });
  } else {
    templiner = cblcode;

    const LinerSet = cblSectLinermap.get(cblcode);

    let arr = Array.from(LinerSet);

    let midIndex = Math.floor(arr.length / 2);
    let middleElement: any = arr[midIndex];

    map.flyTo(middleElement.getCenter(), 18, {});
  }
};


//OLT障碍单表
const tablefaultOrderOlt = ref<any>([]);

const queryfaultordernowInfoService = useInfo({
  rootPath: '/graph-rest-api',
});
const { info: queryfaultordernowInfo, loading: queryfaultordernowunionhisloading } = queryfaultordernowInfoService;

//查询在途网络障碍单    
async function queryfaultordernowunionhis(ip_addr) {

  queryfaultordernowInfo.value = {
    params: {
      ip_addr: ip_addr
    }

  };
  
  //TODO：根据OLT的IP，查询历史障碍单有哪些

  console.log('queryfaultordernowInfo', queryfaultordernowInfo.value);

  queryfaultordernowInfo.value = await queryfaultordernowInfoService.doCreateNew(
    '/api/faulthologram/queryfaultordernowunionhis',
  );



  // console.log('查询这些OLT的IP对应的障碍单', queryfaultordernowInfo.value);


  //这里把查询的结果插入到清单
  return queryfaultordernowInfo.value ;

  
}


//分光器障碍单表
const tablefaultOrderObd = ref<any>([]);

const queryFaultOrderByTitleInfoService = useInfo({
  rootPath: '/graph-rest-api',
});
const { info: queryFaultOrderByTitleInfo, loading: queryFaultOrderByTitleloading } = queryFaultOrderByTitleInfoService;

//查询申告发现的分光器障碍单 
async function queryFaultOrderByTitle(dev_code) {

  queryFaultOrderByTitleInfo.value = {
    params: {
      dev_code: dev_code
    }

  };
  
  queryFaultOrderByTitleInfo.value = await queryFaultOrderByTitleInfoService.doCreateNew(
    '/api/faulthologram/queryFaultOrderByTitle',
  );

  //这里把查询的结果插入到清单
  return queryFaultOrderByTitleInfo.value ;

  
}





//获取同资源信息，olt、一级obd、二级obd
const ExecuteGetResourceinfo = async (accs_nbr_nos, city) => {

  //先把障碍单列表清空
  tablefaultOrderOlt.value = [];
  //获取资源设备信息

  getResourceinfo.value = { accs_nbr_nos: accs_nbr_nos, city: city };

  getResourceinfoResult.value = await getResourceinfoService.doCreateNew(
    '/api/accs_nbr_no/groupfault',
  );
  oltList.value = getResourceinfoResult.value.oltList;
  frstobdList.value = getResourceinfoResult.value.frstobdList;
  scndobdList.value = getResourceinfoResult.value.scndobdList;

  console.log('oltList',oltList.value);
  console.log('frstobdList',frstobdList.value);
  console.log('scndobdList',scndobdList.value);
  

  //绘制设备到地图上
  if(oltList.value){
    drawEquipments(oltList.value, MapContainer);
    //绘制设备对应关系的虚线

    if(getResourceinfoResult.value){
      drawEquipmentsMapLine(getResourceinfoResult.value, MapContainer);

    }
  }


  console.log('frstobdList', frstobdList.value);
  //画设备OLT、OBD
  if (frstobdList.value !== undefined) {
    frstobdList.value.forEach((frstobd) => {
      frstobd.obd_level = 1;
    });
    drawEquipments(frstobdList.value, MapContainer);
  }
  console.log('scndobdList', scndobdList.value);

  if (scndobdList.value !== undefined) {
    scndobdList.value.forEach((scndobd) => {
      scndobd.obd_level = 2;
    });
    drawEquipments(scndobdList.value, MapContainer);
  }

  //如果这里的olt不为空，就去找一下现在的障碍单，有没有这个障碍：

  






  // console.log('oltList',oltList.value);
  // if(Array.isArray(oltList.value)){

  //   for (let index = 0; index < oltList.value.length; index++) {
  //     const olt = oltList.value[index];
      
  //     const olt_ip = olt.olt_ip;

  //     const oltipfaultorder = await queryfaultordernowunionhis(olt_ip);
  //     //
  //     tablefaultOrderOlt.value.push(...oltipfaultorder.value);


  //     tablefaultOrderOlt.value.sort((a, b) => {
  //       return dayjs(b['派单时间']).diff(dayjs(a['派单时间']));
  //     });


  //     console.log('oltipfaultorder障碍单列表',tablefaultOrderOlt.value);
      
  //   }
  // }
  

  return {
    oltList,
    frstobdList,
    scndobdList,
  };
};


async function getNocFaultOrder() {

  if (Array.isArray(oltList.value)) {

    for (let index = 0; index < oltList.value.length; index++) {
      const olt = oltList.value[index];

      const olt_ip = olt.olt_ip;

      const oltipfaultorder = await queryfaultordernowunionhis(olt_ip);
      //
      tablefaultOrderOlt.value.push(...oltipfaultorder.value);




      console.log('oltipfaultorder障碍单列表', tablefaultOrderOlt.value);

    }

    tablefaultOrderOlt.value.sort((a, b) => {
      return dayjs(b['派单时间']).diff(dayjs(a['派单时间']));
    });
  }


  if (Array.isArray(frstobdList.value)) {

    for (let index = 0; index < frstobdList.value.length; index++) {
      const frstobd = frstobdList.value[index];

      const frstobd_code = frstobd.frst_obd_no;

      const frstobdfaultorder = await queryFaultOrderByTitle(frstobd_code);
      //
      tablefaultOrderObd.value.push(...frstobdfaultorder.value);
      console.log('tablefaultOrderObd', tablefaultOrderObd.value);

    }
    tablefaultOrderObd.value.sort((a, b) => {
      return dayjs(b['派单时间']).diff(dayjs(a['派单时间']));

    });
  }


  if (Array.isArray(scndobdList.value)) {

    for (let index = 0; index < scndobdList.value.length; index++) {
      const secondObd = scndobdList.value[index];

      const secondObd_code = secondObd.scnd_obd_no;

      const secondObdfaultorder = await queryFaultOrderByTitle(secondObd_code);
      //
      tablefaultOrderObd.value.push(...secondObdfaultorder.value);

    }
    tablefaultOrderObd.value.sort((a, b) => {
      return dayjs(b['派单时间']).diff(dayjs(a['派单时间']));

    });
  }


  console.log('oltList.value', oltList.value);


}

//障碍单列
const order_columns = [
  {
    title: '工单号',
    dataIndex: '工单号',
  },
  {
    title: '设备名称',
    dataIndex: '设备名称',
    width: 200
  },
  {
    title: '工单标题',
    dataIndex: '工单标题',
    width: 400
  },
  {
    title: '主处理人',
    dataIndex: '主处理人',
  },
  {
    title: '主处理岗',
    dataIndex: '主处理岗',
  },
  {
    title: '告警信息',
    dataIndex: '告警信息',
    width: 600
  },
  // {
  //   title: '故障位置',
  //   dataIndex: '故障位置',
  // },
  {
    title: '接单时间',
    dataIndex: '接单时间',
  },
  {
    title: '派单时间',
    dataIndex: '派单时间',
  },
  {
    title: '工单状态',
    dataIndex: '工单状态',
  },
  {
    title: '障碍来源',
    dataIndex: '障碍来源',
  },
  // {
  //   title: '工单历时(秒)',
  //   dataIndex: '工单历时',
  // },
  // {
  //   title: '工单剩余历时(秒)',
  //   dataIndex: '工单剩余历时',
  // },
];

//获取同资源信息，olt、一级obd、二级obd
const ExecuteSaveQuery = async (accs_nbr_nos, city) => {
  //获取资源设备信息
  getResourceinfo.value = { accs_nbr_nos: accs_nbr_nos, city: city };

  getResourceinfoResult.value = await getResourceinfoService.doCreateNew(
    '/api/accs_nbr_no/groupfault',
  );
  oltList.value = getResourceinfoResult.value.oltList;
  frstobdList.value = getResourceinfoResult.value.frstobdList;
  scndobdList.value = getResourceinfoResult.value.scndobdList;

  return {
    oltList,
    frstobdList,
    scndobdList,
  };
};

//根据光缆段获取接入号
const ExecuteGetAccsNosinfo = async (CblSectNos, city) => {
  getAccsNosinfo.value = { segments: CblSectNos, city: city };
  getAccsNosinfoResult.value = await getAccsNosinfoService.doCreateNew(
    '/api/accs_nbr_no/getaccesscode',
  );
  return getAccsNosinfoResult;
};

//根据接入号画支撑段，整体的逻辑思路如下：
// 1、先根据接入号查支撑段，需要循环遍历查询给的接入号的值，用接口V2可以实现
// 2、查到了以后先封装数据，再进行数据的排序sort,最后把排序sort结果的linelist作为一个对象包起来。
// 2、直接把上面生成的linelist画出来，作为底下的线图层
// 3、根据对象里面所有的支撑段出现的次数，添加支撑段的颜色
// 4、画出重复的支撑段，作为上面的图层
// 5、需要添加对于这个接入号画出来的线的箭头方向，还有就是要加标记说明颜色对应的接入号的编号

const ExecuteGroupFaultLog = async (accs_nbr_nos) => {
  groupFaultLogInfo.value = {
    accs_nbr_nos_list: accs_nbr_nos.join(),
    operator: operator,
    city: city.value,
  };

  groupFaultLogInfoResult.value = await groupFaultLogInfoService.doCreateNew(
    '/api/accs_nbr_no/groupfaultlog',
  );

  console.log('日志保存结果：', groupFaultLogInfoResult.value === 1 ? '保存成功' : '保存失败');
};

//更新日志里面的保存的位置
const ExecuteUpdateGroupFaultLog = async (accs_nbr_nos) => {
  if(!accs_nbr_nos){

    notification.open({
      message: `接入号清单为空，请在最上面的接入号清单中输入障碍接入号`,
      duration: 2,
      icon: () => h(CloseCircleOutlined, { style: 'color: red' }),
    });
    
  }else{
    // if(groupfaultposition.value.length == 0){
    //     notification.open({
    //       message: `请先标记障碍位置`,
    //       duration: 2,
    //       icon: () => h(CloseCircleOutlined, { style: 'color: red' }),
    //     });

    //   }
      // else{
        const accs_nbr_nos_list = splitItemsBySeparators(accs_nbr_nos);

        groupFaultLogInfo.value = {
          accs_nbr_nos_list: accs_nbr_nos_list.join(),
          operator: operator,
          city: city.value,
          position: JSON.stringify(groupfaultposition.value)
        };

        groupFaultLogInfoResult.value = await groupFaultLogInfoService.doCreateNew(
          '/api/accs_nbr_no/groupfaultlog',
        );

        console.log('障碍点更新结果：', groupFaultLogInfoResult.value === 1 ? '保存成功' : '保存失败');



        if(groupFaultLogInfoResult.value === 1){

        notification.open({
          message: `保存障碍位置成功！`,
          duration: 2,
          icon: () => h(SmileOutlined, { style: 'color: green' }),
        });

        }
      // }

  }

 
};


const ExecutegetGroupFaultLog = async (accs_nbr_nos) => {
  groupFaultLogInfo.value = {
    accs_nbr_nos_list: accs_nbr_nos.join(),
    city: city.value,
  };

  groupFaultLogInfoResult.value = await groupFaultLogInfoService.doCreateNew(
    '/api/accs_nbr_no/query_tb_groupfault_log_postion',
  );

  console.log('日志查询结果', groupFaultLogInfoResult.value);

  let filteredArray = groupFaultLogInfoResult.value.value.filter(item => 'position' in item);  

  if(filteredArray.length!=0){
    console.log('groupFaultLogInfoResult.value.value',groupFaultLogInfoResult.value.value[0].position);
    //将历史障碍点标记在图上
    

    const point = JSON.parse(filteredArray[0].position);
    //依次把保存的点都打印出来
    // point.forEach(element => {
    //   const groupfaultpoint = [element.lat,element.lng]
    //   addPulseIcon(groupfaultpoint,MapContainer);





    // });



    //插入记录
    groupfaultposition.value=point;
    groupfaultposition.value

    groupfaultposition.value.forEach(element => {
    const marker = new Ai.Point(element, { icon: pulseIcon });
    marker.on('click', function () {
      console.log('marker._latlng',marker._latlng);
      map.removeLayer(marker);
      removeMarkerByLatLng(groupfaultposition.value,marker._latlng);
    });
    MapContainer.overLayer.addLayer(marker);
    });
  }
};







//根据接入号获取支撑段
const ExecuteDrawBseSect = async (accs_nbr_nos, city) => {
  const accs_nbr_nosArray = accs_nbr_nos;

  drawBseSectinfo.value = { accs_nbr_nos: accs_nbr_nosArray, city: city };
  drawBseSectinfoResult.value = await DrawBseSectinfoService.doCreateNew(
    '/api/accs_nbr_no/groupfaultv2',
  );

  const tempResult = drawBseSectinfoResult.value;
  //转换字段

  console.log('管道段查询结果drawBseSectinfoResult.value', tempResult);

  if(tempResult.bse_sect_list.length == 0 ){

    notification.open({
      message: `未查到管线信息，请联系运维处理宽表数据`,
      duration: 2,
      icon: () => h(SwapOutlined, { style: 'color: red' }),
    });



    return ; 

  }
  const encapsulatedBseSectList = encapsulateAndAggregateBseSectList(tempResult);
  console.log('1整合聚类支撑段encapsulatedBseSectList', encapsulatedBseSectList);

  function encapsulateAndAggregateBseSectList(originalBseSectList) {
    // 使用 reduce 方法按 accs_nbr_no 聚合 bseSect 项
    const aggregatedBseSects = originalBseSectList.bse_sect_list.reduce((acc, bseSect) => {
      const accsNbrNo = bseSect.accs_nbr_no; // 假设每个 bseSect 都有一个 accs_nbr_no 属性
      if (!acc[accsNbrNo]) {
        acc[accsNbrNo] = []; // 如果还没有这个 accs_nbr_no 的数组，就创建一个
      }
      // 将当前 bseSect 添加到对应的 accs_nbr_no 数组中
      acc[accsNbrNo].push({
        accs_nbr_no: bseSect.accs_nbr_no,
        cable_id: bseSect.cbl_id,
        cable_no: bseSect.cable_no,
        cable_name: bseSect.cable_name,
        cable_spec_id: bseSect.cable_spec_id,
        cbl_sect_id: bseSect.cbl_sect_id,
        cbl_sect_no: bseSect.cbl_sect_no,
        cbl_sect_name: bseSect.cbl_sect_name,
        cbl_sect_spec_id: bseSect.cbl_sect_spec_id,
        bse_sect_id: bseSect.bse_sect_id,
        bse_sect_no: bseSect.bse_sect_no,
        bse_sect_name: bseSect.bse_sect_name,
        bse_sect_spec_id: bseSect.bse_sect_spec_id,
        a_bse_eqp_id: bseSect.a_bse_eqp_id,
        a_bse_eqp_no: bseSect.a_bse_eqp_no,
        a_bse_eqp_name: bseSect.a_bse_eqp_name,
        a_bse_eqp_spec_id: bseSect.a_bse_eqp_spec_id,
        a_bse_eqp_x: bseSect.aposx, // 映射aposx到a_bse_eqp_x
        a_bse_eqp_y: bseSect.aposy, // 映射aposy到a_bse_eqp_y
        z_bse_eqp_id: bseSect.z_bse_eqp_id,
        z_bse_eqp_no: bseSect.z_bse_eqp_no,
        z_bse_eqp_name: bseSect.z_bse_eqp_name,
        z_bse_eqp_spec_id: bseSect.z_bse_eqp_spec_id,
        z_bse_eqp_x: bseSect.zposx, // 映射zposx到z_bse_eqp_x
        z_bse_eqp_y: bseSect.zposy, // 映射zposy到z_bse_eqp_y
      });
      return acc;
    }, {});

    // 将聚合后的对象转换为数组形式，即 bseLineLists，并且合并所有 bseLineList 到一个数组
    const bseLineLists: any = [];
    const accnoMapbseLine = Object.keys(aggregatedBseSects).map((key) => {
      const bseLineList = aggregatedBseSects[key];
      bseLineLists.push(bseLineList); // 合并所有 bseLineList 到 allBseLineList
      return {
        accs_nbr_no: key,
        bseLineList, // 对应的 bseSect 数组
      };
    });

    // 返回聚合后的数组和合并后的 bseLineList 数组
    return {
      accnoMapbseLine, // 每个 accs_nbr_no 对应的 bseLineList 数组
      bseLineLists, // 合并后的所有 bseLineList 数组
    };
  }
  const accnoMapbseLine = encapsulatedBseSectList.accnoMapbseLine;
  console.log('accnoMapbseLine', accnoMapbseLine);
  const rawbseLineLists = encapsulatedBseSectList.bseLineLists;

  // const result = encapsulateAndAggregateBseSectList(originalBseSectList);
  // console.log(result.bseLineLists); // 输出按 accs_nbr_no 聚合的数组
  // console.log(result.allBseLineList); // 输出合并后的所有 bseLineList 数组

  const bseLineLists: any = [];
  //筛选有效坐标和支撑段
  rawbseLineLists.forEach((rawbseLineList) => {
    const { bseLineList, inValidBseSectList, singlePointBaseSectList } =
      sortBseSectsV2(rawbseLineList);

    console.log(bseLineList, inValidBseSectList, singlePointBaseSectList);

    function sortBseSectsV2(bseSectList) {
      // 初始化结果数组
      const bseLineList: any[] = [];
      const bseLine: any[] = [];

      const inValidBseSectList: any[] = [];
      const singlePointBaseSectList: any[] = [];

      // 辅助函数：检查坐标是否有效
      function isValidCoordinate(coord) {
        return coord !== null && coord !== undefined && coord !== '';
      }

      // 辅助函数：检查bseSect的a端和z端坐标是否有效
      function hasValidCoordinates(bseSect) {
        const aCoordsValid =
          isValidCoordinate(bseSect.a_bse_eqp_x) && isValidCoordinate(bseSect.a_bse_eqp_y);
        const zCoordsValid =
          isValidCoordinate(bseSect.z_bse_eqp_x) && isValidCoordinate(bseSect.z_bse_eqp_y);
        return aCoordsValid && zCoordsValid;
      }

      // 辅助函数：检查bseSect是否只有一个有效的坐标端点
      function hasSingleValidCoordinate(bseSect) {
        const aCoordsValid =
          isValidCoordinate(bseSect.a_bse_eqp_x) && isValidCoordinate(bseSect.a_bse_eqp_y);
        const zCoordsValid =
          isValidCoordinate(bseSect.z_bse_eqp_x) && isValidCoordinate(bseSect.z_bse_eqp_y);
        return (aCoordsValid && !zCoordsValid) || (!aCoordsValid && zCoordsValid);
      }

      // 遍历bseSectList
      for (const bseSect of bseSectList) {
        // 检查坐标有效性
        if (!hasValidCoordinates(bseSect)) {
          // 两端坐标都无效
          inValidBseSectList.push(bseSect);
        } else if (hasSingleValidCoordinate(bseSect)) {
          // 只有一个坐标有效
          singlePointBaseSectList.push(bseSect);
        } else {
          //console.log('bseLine里面push了一个bseSect',bseSect);
          bseLine.push(bseSect);
          //console.log('bseLine里面push了一个bseSect',bseSect);
        }
      }
      bseLineList.push(bseLine);

      // 返回结果
      return {
        bseLineList,
        inValidBseSectList,
        singlePointBaseSectList,
      };
    }

    // const { bseLineList:bseLineList2,...others  } = sortBseSectsV2(rawbseLineList);
    // console.log('bseLineList2',bseLineList2);

    //drawBseLine(bseLineList, MapContainer);
    bseLineLists.push(bseLineList);
  });

  //设置支撑段的颜色和粗细

  // console.log('1、完成sortBseSects之后的bseLineLists',bseLineLists)
  // const updatedData = addColorToBseSects(bseLineLists);

  const bseSectInfluenceAccsnosMap = new Map(); //支撑段影响的接入号   --画线的粗细用的
  const bseSectInfluenceCblSectsMap = new Map(); //支撑段影响的光缆段  --展示支撑段的弹窗里面的，涉及到的光缆段以及影响的接入号表格用的
  // 根据光缆段查支撑段的清单
  const cblSectBseSectMap = new Map();
  const BseSectcblSectMap = new Map();

  const accsNosBseSectMap = new Map();

  const BseSectAZBseEqpMap = new Map();
  const BseSectAZBsePosMap = new Map();

  //归纳影响范围
  bseLineLists.forEach((bseLineList) => {
    bseLineList.forEach((bseLine) => {
      for (let i = 0; i < bseLine.length; i++) {
        const bseSectId = bseLine[i].bse_sect_id;
        const cblSectNo = bseLine[i].cbl_sect_no;
        const newAccsNo = bseLine[i].accs_nbr_no;

        const a_bse_eqp_id = bseLine[i].a_bse_eqp_id;
        const a_bse_eqp_x = bseLine[i].a_bse_eqp_x;
        const a_bse_eqp_y = bseLine[i].a_bse_eqp_y;
        const a_pos = { x: a_bse_eqp_x, y: a_bse_eqp_y };

        const z_bse_eqp_id = bseLine[i].z_bse_eqp_id;
        const z_bse_eqp_x = bseLine[i].z_bse_eqp_x;
        const z_bse_eqp_y = bseLine[i].z_bse_eqp_y;
        const z_pos = { x: z_bse_eqp_x, y: z_bse_eqp_y };

        const pos = { a: a_pos, z: z_pos };

        const az_bse_eqp = { a_bse_eqp_id, z_bse_eqp_id };

        BseSectAZBseEqpMap.set(bseSectId, az_bse_eqp);
        BseSectAZBsePosMap.set(bseSectId, pos);

        //归纳支撑段影响的接入号的清单的set
        if (!bseSectInfluenceAccsnosMap.get(bseSectId)) {
          const accs_noSet = new Set();
          accs_noSet.add(newAccsNo);
          bseSectInfluenceAccsnosMap.set(bseSectId, accs_noSet);
        } else {
          bseSectInfluenceAccsnosMap.get(bseSectId).add(newAccsNo);
        }

        //归纳支撑段影响的光缆段，以及光缆段影响的接入号的清单
        let cblSectInfluenceAccsnosMap = bseSectInfluenceCblSectsMap.get(bseSectId);
        if (!cblSectInfluenceAccsnosMap) {
          cblSectInfluenceAccsnosMap = new Map();
          bseSectInfluenceCblSectsMap.set(bseSectId, cblSectInfluenceAccsnosMap);
        }
        let accsNosSet = cblSectInfluenceAccsnosMap.get(cblSectNo);
        if (!accsNosSet) {
          accsNosSet = new Set();
          cblSectInfluenceAccsnosMap.set(cblSectNo, accsNosSet);
        }
        accsNosSet.add(newAccsNo);

        //归纳光缆段和支撑端清单的映射关系
        if (!cblSectBseSectMap.get(cblSectNo)) {
          const bseSectSet = new Set();
          bseSectSet.add(bseSectId);
          cblSectBseSectMap.set(cblSectNo, bseSectSet);
        } else {
          cblSectBseSectMap.get(cblSectNo).add(bseSectId);
        }

        //归纳支撑端影响哪些光缆段清单的映射关系
        if (!BseSectcblSectMap.get(bseSectId)) {
          const cblSectSet = new Set();
          cblSectSet.add(cblSectNo);
          BseSectcblSectMap.set(bseSectId, cblSectSet);
        } else {
          BseSectcblSectMap.get(bseSectId).add(cblSectNo);
        }
        //归纳接入号和支撑段的映射关系  --反向排除掉一批接入号对应的光缆段
        if (!accsNosBseSectMap.get(newAccsNo)) {
          const bseSectSet = new Set();
          bseSectSet.add(bseSectId);
          accsNosBseSectMap.set(newAccsNo, bseSectSet);
        } else {
          accsNosBseSectMap.get(newAccsNo).add(bseSectId);
        }
      }
    });
  });

  console.log('光缆段对应哪些支撑段cblSectBseSectMap', cblSectBseSectMap);

  function findMaxSetBseSectIds(bseSectInfluenceAccsnosMap) {
    let maxSize = 0;
    let maxBseSectIds: any = [];

    // 遍历 Map，找到最大 Set 的大小
    for (let [bseSectId, accs_noSet] of bseSectInfluenceAccsnosMap) {
      const size = accs_noSet.size;
      if (size > maxSize) {
        maxSize = size;
        maxBseSectIds = [bseSectId];
      } else if (size === maxSize) {
        maxBseSectIds.push(bseSectId);
      }
    }

    return maxBseSectIds;
  }

  const baseLineInfoGroup = {
    bseLineLists: bseLineLists,
    bseSectInfluenceAccsnosMap,
    bseSectInfluenceCblSectsMap,
    cblSectBseSectMap,
    BseSectcblSectMap,
    accsNosBseSectMap,
  };

 




  //画重复支撑段
  const {
    cblSectLinerMap,
    bseSectLinerMap, //支撑段的ID映射的图层set
    accsNoLinerMap, //接入号映射的图层set
  } = drawDuplicatedBseSectFacility(baseLineInfoGroup, MapContainer);


  
  
  console.log('MapContainer.overLayer.toGeoJSON()',MapContainer.overLayer.toGeoJSON());
  

  cblSectLinermap = cblSectLinerMap;
  bseSectLinermap = bseSectLinerMap;
  const accsNoLinermap = accsNoLinerMap;
  console.log(accsNoLinermap);

  maxBseSectIds = findMaxSetBseSectIds(bseSectInfluenceAccsnosMap);

  //取出对应的AZ端数组：
  const maxSetAZbseEqp: any = [];
  const maxSetAZPos: any = [];

  // 遍历maxBseSectIds数组，根据BseSectId获取基本信息并转存
  maxBseSectIds.forEach((bseSectId) => {
    // 在tempResult.bse_sect_list中查找匹配的元素
    let matchingSect = tempResult.bse_sect_list.find((sect) => sect.bse_sect_id === bseSectId);
    if (matchingSect) {
      // 提取所需字段并创建新对象
      let sectData: any = {
        bse_sect_name: matchingSect.bse_sect_name,
        bse_sect_no: matchingSect.bse_sect_no,
        bse_sect_id: bseSectId,
        a_bse_eqp_name: matchingSect.a_bse_eqp_name,
        a_bse_eqp_no: matchingSect.a_bse_eqp_no,
        z_bse_eqp_name: matchingSect.z_bse_eqp_name,
        z_bse_eqp_no: matchingSect.z_bse_eqp_no,
        subtable: [], // 初始化children数组，稍后将填充
      };

      // 在bseSectInfluenceCblSectsMap中查找对应的cblAccsnomap
      const cblAccsnomap = bseSectInfluenceCblSectsMap.get(bseSectId);
      if (cblAccsnomap.size > 0) {
        for (let [key, value] of cblAccsnomap.entries()) {
          const tempobject = {
            cbl_sect_no: key,
            accs_nos: [...value],
          };
          sectData.subtable.push(tempobject);
        }
      }

      // 将新对象添加到结果数组中
      bseSectData.value.push(sectData);
    }
  });

  function aggregateBySubtable(bseSectData) {
    const map = new Map(); // 用于根据subtable分组

    // 遍历数组并分组
    bseSectData.value.forEach((sectData) => {
      const subtableKey = JSON.stringify(sectData.subtable); // 使用JSON.stringify作为subtable的键
      if (!map.has(subtableKey)) {
        // 如果是新组，则直接添加
        map.set(subtableKey, { ...sectData });
      } else {
        // 如果组已存在，则聚合属性（除了subtable）
        const group = map.get(subtableKey);
        for (const key in sectData) {
          if (key !== 'subtable' && Object.prototype.hasOwnProperty.call(sectData, key)) {
            if (!Array.isArray(group[key])) {
              // 如果属性还不是数组，则将其转换为数组并添加原始值
              group[key] = [group[key]];
            }
            // 添加新值到数组
            group[key].push(sectData[key]);
          }
        }
      }
    });

    // 从map中提取聚合后的数据并返回新数组
    return Array.from(map.values());
  }

  aggregatedData.value = aggregateBySubtable(bseSectData);

  maxBseSectIds.forEach((BseSectId) => {
    const az_bse_eqp = BseSectAZBseEqpMap.get(BseSectId);
    const temp = { BseSectId: BseSectId, az_bse_eqp: az_bse_eqp };
    maxSetAZbseEqp.push(temp);
    // console.log('最多元素的 Set 对应的 maxSetAZbseEqp 数组:', maxSetAZbseEqp);

    const az_pos = BseSectAZBsePosMap.get(BseSectId);
    const temp2 = { BseSectId: BseSectId, az_pos: az_pos };
    maxSetAZPos.push(temp2);
  });
  // console.log('最多元素的 Set 对应的 maxSetAZPos 数组:', maxSetAZPos);

  // 调用函数并输出结果
  // const sortedSegments = connectSegments(maxSetAZPos);

  //TODO支撑段找到的图层
  //changeBseSectColorFromIds(maxBseSectIds,bseSectLinerMap,'orange',1.0);

  let highLightCblBseSectSet = new Set();

  console.log('@@@@@@@@@@highlightCbl', highlightCbl);
  highlightCbl.value.forEach((code) => {
    const currentSet = cblSectBseSectMap.get(code);
    if (currentSet) {
      currentSet.forEach((item) => {
        highLightCblBseSectSet.add(item);
      });
    }
  });
  console.log('@@@@@@@@highLightCblBseSectSet', highLightCblBseSectSet);

  //合并高亮光缆段和支撑段的支撑段ID，并且取交集，之后改色
  intersectionArray = maxBseSectIds.filter((item) => highLightCblBseSectSet.has(item));

  console.log('@@@@@@@@高亮光缆段和支撑段的交集ID', intersectionArray);

  console.log('@@@@@@@@maxBseSectIds', maxBseSectIds);

  if (intersectionArray.length == 0) {
    changeBseSectColorFromIds(maxBseSectIds, bseSectLinerMap, 'orange', 0.5);
  } else {
    changeBseSectColorFromIds(intersectionArray, bseSectLinerMap, 'orange', 0.5);
  }

  console.log('intersectionArray', intersectionArray);

  const bseSectList = drawBseSectinfoResult.value.bse_sect_list;

  const allIds = intersectionArray.reduce((accumulator, intersectId) => {
    const matchingSect = bseSectList.find((sect) => sect.bse_sect_id === intersectId);
    if (matchingSect) {
      accumulator.add(matchingSect.a_bse_eqp_id);
      accumulator.add(matchingSect.z_bse_eqp_id);
    }
    return accumulator;
  }, new Set());

  const uniqueIdsArray = Array.from(allIds);
  console.log('uniqueIdsArray', uniqueIdsArray);

  bseEqptoBseSectInfo.value = { bse_eqp_ids: uniqueIdsArray, city: city };
  console.log('检查入参bseEqptoBseSectInfo', bseEqptoBseSectInfo.value);

  bseEqptoBseSectInfoResult.value = await bseEqptoBseSectInfoService.doCreateNew(
    '/api/accs_nbr_no/bseeqptobsesect',
  );

  console.log('bseEqptoBseSectInfoResult', bseEqptoBseSectInfoResult.value);

  // 去重处理
  const uniqueBseSectList = uniqueByBseSectId(bseEqptoBseSectInfoResult.value.bse_sect_list);
  const modifiedBseSectList = renameKeysInBseSectList(uniqueBseSectList);
  console.log(modifiedBseSectList);

  // console.log('检查这段为啥为空modifiedBseSectList',modifiedBseSectList);
  // drawBseSectFacility(modifiedBseSectList,'brown', MapContainer);
  // console.log('绘制管道段影响范围');

  // // 针对高亮光缆段，找到其bseSectIDs
  // for (const code of highlightCbl) {
  //   const currentSet = cblSectBseSectMap.get(code);
  //   if (!currentSet) {
  //     console.log(`这个光缆段没有找到对应的支撑段 ${code}`);
  //     continue;
  //   }  else{
  //     const cblLinerSet = cblSectLinerMap.get(code);
  //     console.log('高亮光缆段的集合set',cblLinerSet);

  //     // 输出交集结果
  //     //TODO:光缆段找到的图层
  //     if (cblLinerSet && cblLinerSet.size > 0) {
  //       cblLinerSet.forEach(lineLayer => {
  //         lineLayer.setStyle({ color: 'yellow', opacity: 0.8 });
  //       });
  //     }
  //   }
  // }



  function calculateDistance(pos1, pos2) {
    // 计算两点之间的欧几里得距离
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    return Math.sqrt(dx * dx + dy * dy);
  }


  function findClosestBseSectId(maxSetAZPos, frstobdList) {
    // 创建一个Map来存储BseSectId和对应的最短距离
    const distanceMap = new Map();

    // 遍历maxSetAZPos数组
    maxSetAZPos.forEach((item) => {
      let minDistance = Infinity; // 初始化最小距离为无穷大
      let midPoint = {
        x: (parseFloat(item.az_pos.a.x) + parseFloat(item.az_pos.z.x)) / 2,
        y: (parseFloat(item.az_pos.a.y) + parseFloat(item.az_pos.z.y)) / 2,
      }; // 计算az端坐标的中点

      // 遍历oltList.value数组，找到与当前中点最近的点
 
      frstobdList.value.forEach((obd) => {
        const distance = calculateDistance(midPoint, {
          x: parseFloat(obd.posx),
          y: parseFloat(obd.posy),
        });

        if (distance < minDistance) {
          minDistance = distance; // 更新最小距离
        }
      });

      // 将BseSectId和对应的最小距离存入Map中
      distanceMap.set(item.BseSectId, minDistance);
    });

    // 将Map转换为一个数组，然后根据距离进行排序
    const sortedDistances = Array.from(distanceMap.entries()).sort((a, b) => a[1] - b[1]);

    // 返回距离最远的BseSectId
    return sortedDistances[0][0];
  }
  if(frstobdList.value){
    const tempBseSectId = findClosestBseSectId(maxSetAZPos, frstobdList);
    furthestBseSectId.value = tempBseSectId;

  }

  // function keepFirstAndLast(array) {
  //     if (array.length <= 2) {
  //         // 如果数组长度小于等于2，直接返回数组，因为没有中间元素可以移除
  //         return array;
  //     }
  //     // 否则，返回一个新数组，只包含第一个和最后一个元素
  //     return [array[0], array[array.length - 1]];
  // }
  // //下面是贪心算法排序的bseLine的起点端点找到距离一级分光器最近距离的函数
  // const newArray = keepFirstAndLast(sortedMaxSetAZPos);
  // console.log('newArray',newArray);

  // const tanxinfurthestBseSectId = findClosestBseSectId(newArray, frstobdList);
  /*
    function findAZMidpoint(maxSetAZPosArray, targetBseSectId) {
      // 在数组中查找具有指定 BseSectId 的元素
      const element = maxSetAZPosArray.find((el) => el.BseSectId === targetBseSectId);

      if (element) {
        // 提取 a 和 z 的坐标
        const coordA = element.az_pos.a;
        const coordZ = element.az_pos.z;

        // 计算中间点
        const midpoint = [
          (parseFloat(coordA.y) + parseFloat(coordZ.y)) / 2,
          (parseFloat(coordA.x) + parseFloat(coordZ.x)) / 2,
        ];
        return midpoint;
      } else {
        // 如果没有找到匹配的 BseSectId，返回 null 或错误消息
        console.error(`未找到 BseSectId 为 ${targetBseSectId} 的坐标`);
        return null;
      }
    }

     */

  // 距离用户最近的光缆段标红色
  // changeBseSectColorFromIds(tempBseSectId,bseSectLinerMap,'red',1.0);

  // const midpoint = findAZMidpoint(maxSetAZPos, tempBseSectId);
  // if (midpoint) {
  //   console.log('a和z的中间点坐标是:', midpoint);
  //   addPulseIcon(midpoint,MapContainer);
  // }
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const ExecuteDrawBseSectImpactArea = async (input, city) => {
  getPipeEffectInfo.value = { codes: input, city: city };
  // DownloadSelectedCblSectAccsNosinfo.value = { segments: cbl_sect_nos.value, city: city.value };

  getPipeEffectInfo.value = await getPipeEffectInfoService.doCreateNew(
    '/api/accs_nbr_no/pipe_effect',
  );

  //提取管道段里面的不同的光缆段下不同纤芯的接入号，每个纤芯提取一个接入号，用于绘制影响范围
  function extractUniqueAccessCodes(getPipeEffectInfo) {
    const result: any = [];

    if (!getPipeEffectInfo.value || !getPipeEffectInfo.value.pipeSegmentList) {
      return result; // 如果数据不存在或pipeSegmentList为空，直接返回空数组
    }

    getPipeEffectInfo.value.pipeSegmentList.forEach((pipeSegment) => {
      if (pipeSegment.cable_segment_list) {
        pipeSegment.cable_segment_list.forEach((cableSegment) => {
          if (cableSegment.cfs_list) {
            const uniqueLineNos = new Set();
            cableSegment.cfs_list.forEach((cfsItem) => {
              if (!uniqueLineNos.has(cfsItem.line_no)) {
                // 如果line_no是唯一的，则添加access_code到结果数组中
                uniqueLineNos.add(cfsItem.line_no);
                result.push(cfsItem.access_code);
              }
            });
          }
        });
      }
    });

    return result;
  }
  console.log(ExecuteDrawBseSectImpactArea);
  const access_codes = extractUniqueAccessCodes(getPipeEffectInfo);

  console.log('提取管道段下面的光缆段的不同纤芯的接入号清单access_codes', access_codes);

  //TODO下面开始绘制这批接入号的影响范围
  const accs_nbr_nosArray = access_codes;
  drawBseSectinfo.value = { accs_nbr_nos: accs_nbr_nosArray, city: city };
  drawBseSectinfoResult.value = await DrawBseSectinfoService.doCreateNew(
    '/api/accs_nbr_no/groupfaultv2',
  );

  // 假设 drawBseSectinfoResult.value.bse_sect_list 是你的原始数组
  let originalBseSectList = drawBseSectinfoResult.value.bse_sect_list;
  const color = getRandomColorRGB();

  // 去重处理
  const uniqueBseSectList = uniqueByBseSectId(originalBseSectList);
  const modifiedBseSectList = renameKeysInBseSectList(uniqueBseSectList);
  drawBseSectFacility(modifiedBseSectList, color, MapContainer);
  console.log('绘制管道段影响范围');
};
//管道段去重方法
function uniqueByBseSectId(bseSectList) {
  if (!Array.isArray(bseSectList)) {
    return bseSectList; // 如果不是数组，则直接返回
  }

  // 创建一个Set用于存储已经出现的bse_sect_id
  const seenBseSectIds = new Set();

  // 使用filter方法创建一个新数组，只包含唯一的bse_sect_id
  const uniqueList = bseSectList.filter((item) => {
    if (!seenBseSectIds.has(item.bse_sect_id)) {
      // 如果bse_sect_id还未出现，将其添加到Set中并返回true（保留此元素）
      seenBseSectIds.add(item.bse_sect_id);
      return true;
    }
    // 如果bse_sect_id已经出现，返回false（过滤掉此元素）
    return false;
  });

  return uniqueList;
}
//管道段字段重命名
function renameKeysInBseSectList(bseSectList) {
  if (!Array.isArray(bseSectList)) {
    return bseSectList; // 如果不是数组，直接返回原数据
  }

  return bseSectList.map((item) => {
    // 创建一个新对象，并将修改后的键值对添加到该对象中
    return {
      ...item, // 首先复制所有原始键值对
      // 然后重命名特定的键
      a_bse_eqp_x: item.aposx,
      a_bse_eqp_y: item.aposy,
      z_bse_eqp_x: item.zposx,
      z_bse_eqp_y: item.zposy,
    };
  });
}

function changeBseSectColorFromIds(bseSectIds, bseSectLinerMap, color, opacity) {
  let ischanged = false;
  if (!Array.isArray(bseSectIds)) {
    bseSectIds = [bseSectIds];
  }
  function isSet(obj) {
    return obj instanceof Set;
  }
  for (const bseSectId of bseSectIds) {
    const linelayer = bseSectLinerMap.get(bseSectId);

    if (isSet(linelayer)) {
      for (const line of linelayer) {
        line.setStyle({ color: color, opacity: opacity });
      }
      ischanged = true;
    } else {
      if (linelayer) {
        linelayer.setStyle({ color: color, opacity: opacity });
        ischanged = true;
      } else {
      }
    }
  }
  return ischanged;
}

//下载选中的光缆段接口
const DownloadSelectedCblSectAccsNos = async () => {
  downloading.value = true;

  const flattenedArray1 = flattenCableSegmentNumbers(selectedCblSectRowKeys.value);

  DownloadSelectedCblSectAccsNosinfo.value = {
    codes: flattenedArray1,
    city: city.value,
  };

  const result = await DownloadSelectedCblSectAccsNosinfoService.doDownload(
    '/api/accs_nbr_no/download_cbl_sect_effect',
  );
  const blob = new Blob([result], { type: 'application/vnd.ms-excel' });
  const fileName = '选中光缆段影响接入号清单' + '.xlsx';
  const a = document.createElement('a');
  a.download = fileName;
  a.href = window.URL.createObjectURL(blob);
  a.click();
  a.remove();

  downloading.value = false;
  selectedCblSectRowKeys.value = [];
};

const ExecutePonCheck = async (record, city) => {
  const poninfo: any = [];
  poninfo.push({ ip: record.olt_ip, pon_code: record.pon_code });
  checkPonInfo.value = { poninfo: poninfo, city: city };

  ponCheckinfoResult.value = await checkPonInfoService.doCreateNew('/api/accs_nbr_no/checkpon');

  const checkpon = ponCheckinfoResult.value.checkpon;
  // 检查数组中是否有对象的 runState 属性为 'online'
  function calculateOnlineRate(array) {
    let onlineCount = 0;
    let offlineCount = 0;
    let totalCount = 0;

    // 遍历数组中的每个元素
    array.forEach((item) => {
      // 检查 item 是否包含 result 属性，且 result.LOID 是一个数组
      if (item.result && Array.isArray(item.result.LOID)) {
        // 遍历 result.LOID 数组中的每个对象
        item.result.LOID.forEach((loidItem) => {
          // 检查是否有对象的 runState 属性为 'online'
          if (loidItem.runState === 'online') {
            onlineCount++;
          } else if (loidItem.runState === 'offline') {
            offlineCount++;
          }
          // 如果 runState 不是 'online' 也不是 'offline'，则不计数（可以根据需要调整）
          totalCount++; // 注意：这里应该在上面两个条件之一满足时增加，以避免重复计数
        });
      }
    });

    // 计算总数（应该是在线和离线数量的和，但上面的totalCount可能会重复计数，需要修正）
    totalCount = onlineCount + offlineCount;

    // 避免除数为0的情况
    if (totalCount === 0) {
      return 0; // 如果总数为0，则在线率为0
    }

    // 计算在线率
    const onlineRate = (onlineCount / totalCount) * 100;
    return onlineRate; // 返回在线率百分比
  }

  // 调用函数并打印结果
  const result = calculateOnlineRate(checkpon);

  if (result) {
    record.poncheck_result = '在线率：' + result;
  } else {
    record.poncheck_result = 'PON口异常';
  }
};

// 下载数据
const downloadOtherCblSectAccsNos = async (cbl_sect_no_list, city) => {
  downloading.value = true;

  DownloadSelectedCblSectAccsNosinfo.value = { codes: cbl_sect_no_list, city: city };

  const result = await DownloadSelectedCblSectAccsNosinfoService.doDownload(
    '/api/accs_nbr_no/download_cbl_sect_effect',
  );
  const blob = new Blob([result], { type: 'application/vnd.ms-excel' });
  const fileName = '其他光缆段影响的接入号清单' + '.xlsx';
  const a = document.createElement('a');
  a.download = fileName;
  a.href = window.URL.createObjectURL(blob);
  a.click();
  a.remove();

  downloading.value = false;
};

// 下载支撑段关联的其他的政企客户的数据
const DownloadEnterpriseCustomerByBseSectNos = async (bse_sect_nos, city) => {
  downloading.value = true;
  DownloadEnterpriseCustomerByBseSectNosInfo.value = { codes: bse_sect_nos, city: city };
  const result = await DownloadEnterpriseCustomerByBseSectNosInfoService.doDownload(
    '/api/accs_nbr_no/download_pipe_effect',
  );
  const blob = new Blob([result], { type: 'application/vnd.ms-excel' });
  const fileName = '支撑段关联的其他的客户的数据' + '.xlsx';
  const a = document.createElement('a');
  a.download = fileName;
  a.href = window.URL.createObjectURL(blob);
  a.click();
  a.remove();

  downloading.value = false;
};
const relateanalyse = ref();
// 查询支撑段关联的其他的政企客户的数据
function extractBseSectEffectCodes(jsonData) {
  const resultList: any[] = [];

  if (jsonData && jsonData.pipeSegmentList) {
    jsonData.pipeSegmentList.forEach((pipeSegment) => {
      if (pipeSegment.cable_segment_list) {
        pipeSegment.cable_segment_list.forEach((cableSegment) => {
          if (cableSegment.cfs_list) {
            cableSegment.cfs_list.forEach((cfs) => {
              const service_spec_name = cfs.service_spec_name;
              //如果不是宽带固话ITV三个类型，那就放进去
              if (
                service_spec_name !== '宽带' &&
                service_spec_name !== '普通电话' &&
                service_spec_name !== 'iTV' &&
                service_spec_name !== '智能公话'
              ) {
                const extractedData: any = {
                  pipeSegmentCode: pipeSegment.code,
                  cableSegmentCode: cableSegment.code,
                  optRoadCode: cfs.route_code,
                  lineNo: cfs.line_no,
                  cfsAccessCode: cfs.access_code,
                  cfsName: cfs.service_name,
                  cfsSpec: cfs.service_spec_name,
                  olt_ip: cfs.ip_addr,
                  pon_code: cfs.pon_code,
                  cust_name: cfs.cust_name,
                  khjl_name: cfs.khjl_name,
                  khjl_phone: cfs.khjl_phone,
                  customer_level_name: cfs.customer_level_name,
                  scene_name: cfs.scene_name,
                };
                if(!extractedData.cust_name.includes('电信')){
                  resultList.push(extractedData);
              
                }
              }
            });
          }
        });
      }
    });
  }
  if (resultList.length == 0) {

    notification.open({
      message: `该管道段未查到政企用户`,
      duration: 2,
      icon: () => h(SmileOutlined, { style: 'color: green' }),
    });
  }
  // if (relateanalyse.value) {
  //   relateanalyse.value.scrollIntoView({ behavior: 'smooth', block: 'center' });
  // }
  return resultList;
}
const ExecuteGetPipeEffect = async (input, city) => {
  // if (relateanalyse.value) {
  //   relateanalyse.value.scrollIntoView({ behavior: 'smooth', block: 'center' });
  // }
  getPipeEffectInfo.value = { codes: input, city: city };
  // DownloadSelectedCblSectAccsNosinfo.value = { segments: cbl_sect_nos.value, city: city.value };

  getPipeEffectInfo.value = await getPipeEffectInfoService.doCreateNew(
    '/api/accs_nbr_no/pipe_effect',
  );

  relatedEnterpriseData.value = extractBseSectEffectCodes(getPipeEffectInfo.value);


};

function extractCblSectEffectCodes(jsonData,cableSegment_code) {
  const resultList: any[] = [];

  if (jsonData && jsonData.cfs_influence) {
    jsonData.cfs_influence.forEach((cfs) => {

      // console.log('cableSegment', cableSegment);


      //6\7\9公众，别的都是政企
            if (
                cfs.cust_type_id !== '7' &&
                cfs.cust_type_id !== '6' &&
                cfs.cust_type_id !== '9' 
            ){

          
            const extractedData: any = {
              pipeSegmentCode: '',
              cableSegmentCode: cableSegment_code,
              optRoadCode: cfs.route_code,
              lineNo: cfs.line_no,
              cfsAccessCode: cfs.accs_nbr_no || cfs.access_code,
              cfsName: cfs.service_name,
              cfsSpec: cfs.service_spec_name,
              // olt_ip: cfs.ip_addr,
              // pon_code: cfs.pon_code,
              cust_name: cfs.cust_name,
              khjl_name: cfs.khjl_name,
              khjl_phone: cfs.khjl_phone,
              customer_level_name: cfs.customer_level_name,
              scene_name: cfs.scene_name,
            };

            resultList.push(extractedData);
              
            // 只有客户不是电信的时候才能插入
            // if(!extractedData.cust_name.includes('电信')){
          
            // }
          }
      
      

    });
  }

  if (resultList.length == 0) {

    notification.open({
      message: cableSegment_code+`该光缆段未查到政企用户`,
      duration: 2,
      icon: () => h(SmileOutlined, { style: 'color: green' }),
    });
  }
  console.log('resultList',resultList);
  
  // if (relateanalyse.value) {
  //   relateanalyse.value.scrollIntoView({ behavior: 'smooth', block: 'center' });
  // }
  return resultList;
}

const ExecuteGetCblSectEffect = async (input, city) => {
  // if (relateanalyse.value) {
  //   relateanalyse.value.scrollIntoView({ behavior: 'smooth', block: 'center' });
  // }
  relatedEnterpriseData.value =[];

  console.log('input',input);
  
  if(Array.isArray(input)){

    for (let index = 0; index < input.length; index++) {
      const element = input[index];

      getCblSectEffectInfo.value = {  devCode: element,
      devType: 'cbl_sect',
      city: city, };



      console.log('执行getCblSectEffectInfo.value查询',getCblSectEffectInfo.value );
      

      //TODO:如果是苏州的话，用一树一图接口


      let querylink =  '/api/influenceanalysis/getresourceinfluence'


      // if(city =='sz'){
      //   querylink = '/api/accs_nbr_no/cbl_sect_effect'
      //   getCblSectEffectInfo.value = { 
      //     codes: [element],
      //     city: city, 
      //   };
          
      // }


      getCblSectEffectInfo.value = await getCblSectEffectInfoService.doCreateNew(
        querylink,
      );

      console.log('getCblSectEffectInfo', getCblSectEffectInfo.value);

      let queryresult = getCblSectEffectInfo.value;

      // if(city =='sz'){
      //   queryresult.cfs_influence = getCblSectEffectInfo.value.cblSegmentList[0].cfs_list;

      // }




      relatedEnterpriseData.value = [...relatedEnterpriseData.value, ...extractCblSectEffectCodes(queryresult,element)]
      console.log('relatedEnterpriseData.value',relatedEnterpriseData.value);
      
    }

  }


};








//==================================================表格相关变量、方法========================================================================
//tab页的默认激活项目
const downloading = ref(false);
const hasSelectedCblSect = computed(() => {
  return selectedCblSectRowKeys.value.length > 0;
});
const hasSelectedbseSect = computed(() => {
  return selectedbseSectRows.value.length > 0;
});
const hasSelectedolt = computed(() => {
  return selectedoltRowKeys.value.length > 0;
});
const hasSelectedpon = computed(() => {
  return selectedponRowKeys.value.length > 0;
});
const hasSelectedfrstobd = computed(() => {
  return selectedfrstobdRowKeys.value.length > 0;
});
const hasSelectedscndobd = computed(() => {
  return selectedscndobdRowKeys.value.length > 0;
});

const selectedCblSectRowKeys = ref([]);
const selectedcblSectRows = ref([]);
const onCblSectSelectChange = (selectedRowKeys, selectedRows) => {
  selectedCblSectRowKeys.value = selectedRowKeys;
  selectedcblSectRows.value = selectedRows;
};

const selectedbseSectRowKeys = ref([]);
const selectedbseSectRows = ref([]);
const onbseSectSelectChange = (selectedRowKeys, selectedRows) => {
  selectedbseSectRowKeys.value = selectedRowKeys;
  selectedbseSectRows.value = selectedRows;
};

const selectedoltRowKeys = ref([]);
const selectedoltRows = ref([]);
const onoltSelectChange = (selectedRowKeys, selectedRows) => {
  selectedoltRowKeys.value = selectedRowKeys;
  selectedoltRows.value = selectedRows;
};
console.log(onoltSelectChange);

const selectedponRowKeys = ref([]);
const selectedponRows = ref([]);
const onponSelectChange = (selectedRowKeys, selectedRows) => {
  selectedponRowKeys.value = selectedRowKeys;
  selectedponRows.value = selectedRows;
};

const selectedfrstobdRowKeys = ref([]);
const selectedfrstobdRows = ref([]);
const onfrstobdSelectChange = (selectedRowKeys, selectedRows) => {
  selectedfrstobdRowKeys.value = selectedRowKeys;
  selectedfrstobdRows.value = selectedRows;
};

const selectedscndobdRowKeys = ref([]);
const selectedscndobdRows = ref([]);
const onscndobdSelectChange = (selectedRowKeys, selectedRows) => {
  selectedscndobdRowKeys.value = selectedRowKeys;
  selectedscndobdRows.value = selectedRows;
};

//==================================================树状图相关变量、方法========================================================================

//================================================== 表的列信息========================================================================

//关联政企用户的表的查询列
const relatedEnterpriseColumns = [
  // {
  //   title: '支撑段编码',
  //   dataIndex: 'pipeSegmentCode',
  //   key: 'pipeSegmentCode',
  // },
  {
    title: '光缆段编码',
    dataIndex: 'cableSegmentCode',
    key: 'cableSegmentCode',
  },
  {
    title: '光路编码',
    dataIndex: 'optRoadCode',
    key: 'optRoadCode',
  },
  {
    title: '纤芯号',
    dataIndex: 'lineNo',
    key: 'lineNo',
  },
  {
    title: '接入号',
    dataIndex: 'cfsAccessCode',
    key: 'cfsAccessCode',
  },
  {
    title: '影响业务名称',
    dataIndex: 'cfsName',
    key: 'cfsName',
  },
  {
    title: '政企客户名称',
    dataIndex: 'cust_name',
    key: 'cust_name',
  },
  {
    title: '客户等级',
    dataIndex: 'customer_level_name',
    key: 'customer_level_name',
  },
  {
    title: '影响业务类型',
    dataIndex: 'cfsSpec',
    key: 'cfsSpec',
  },
  // {
  //   title: 'ip地址',
  //   dataIndex: 'olt_ip',
  //   key: 'olt_ip',
  // },
  // {
  //   title: 'pon口编码',
  //   dataIndex: 'pon_code',
  //   key: 'pon_code',
  // },
  {
    title: '客户经理',
    dataIndex: 'khjl_name',
    key: 'khjl_name',
  },
  {
    title: '客户经理电话',
    dataIndex: 'khjl_phone',
    key: 'khjl_phone',
  },
  {
    title: '生命线客户名称',
    dataIndex: 'scene_name',
    key: 'scene_name',
    // customRender: (text) => {
    //   if (text != '') {
    //     // 如果 text.value 是数组，则将其元素用逗号连接
    //     return '否';
    //   } else {
    //     // 如果 text.value 不是数组，则直接返回其值
    //     return '是';
    //   }
    // },
  },

  // {
  //   title: '操作',
  //   dataIndex: 'actions',
  // },
  // {
  //   title: '检测结果',
  //   dataIndex: 'poncheck_result',
  // }
];

//接入号查出的光缆段表的列
const cbl_sectcolumns = [
  {
    title: '光缆段编码',
    dataIndex: 'code',
    key: 'code',
    customRender: (text) => {
      if (Array.isArray(text.value)) {
        // 如果 text.value 是数组，则将其元素用逗号连接
        return text.value.join(',');
      } else {
        // 如果 text.value 不是数组，则直接返回其值
        return String(text.value);
      }
    },
  },
  {
    title: '怀疑度',
    dataIndex: 'doubt_degree',
    key: 'doubt_degree',
    width: '10%',
  },
  {
    title: '光缆段名称',
    dataIndex: 'name',
    customRender: (text) => {
      if (Array.isArray(text.value)) {
        // 如果 text.value 是数组，则将其元素用逗号连接
        return text.value.join(', ');
      } else {
        // 如果 text.value 不是数组，则直接返回其值
        return String(text.value);
      }
    },
  },
  {
    title: '报障接入号',
    dataIndex: 'access_code',
    customRender: (text) => {
      if (Array.isArray(text.value)) {
        // 如果 text.value 是数组，则将其元素用逗号连接
        return text.value.join(', ');
      } else {
        // 如果 text.value 不是数组，则直接返回其值
        return String(text.value);
      }
    },
  },
  {
    title: '报障接入号数',
    dataIndex: 'access_code',
    customRender: (text) => {
      if (Array.isArray(text.value)) {
        // 如果 text.value 是数组，则将其元素用逗号连接
        return text.value.length;
      } else {
        // 如果 text.value 不是数组，则直接返回其值
        return 1;
      }
    },
    sorter: (a, b) => a.access_code.length - b.access_code.length,
    sortDirections: ['descend', 'ascend'],
  },

  // {
  //   title: '正常接入号',
  //   dataIndex: 'access_code_normal',
  //   customRender: (text) => {
  //     if (text === undefined) {
  //       return '无';
  //     }
  //     if (Array.isArray(text.value)) {
  //       // 如果 text.value 是数组，则将其元素用逗号连接
  //       return text.value.join(', ');
  //     } else {
  //       // 如果 text.value 不是数组，则直接返回其值
  //       return String(text.value);
  //     }
  //   },
  //   width: '10%',
  // },
  // {
  //   title: '正常接入号数',
  //   dataIndex: 'access_code_normal',
  //   customRender: (text) => {
  //     if (Array.isArray(text.value)) {
  //       // 如果 text.value 是数组，则将其元素用逗号连接
  //       return text.value.length;
  //     } else {
  //       // 如果 text.value 不是数组，则直接返回其值
  //       return 1;
  //     }
  //   },
  //   width: '8%',
  // }
];

const cbl_sectcolumns_simplify = [
  {
    title: '光缆段编码',
    dataIndex: 'code',
    key: 'code',
    customRender: (text) => {
      if (Array.isArray(text.value)) {
        // 如果 text.value 是数组，则将其元素用逗号连接
        return text.value.join(',');
      } else {
        // 如果 text.value 不是数组，则直接返回其值
        return String(text.value);
      }
    },
    ellipsis: true,
    width: 300,
  },
  {
    title: '怀疑度',
    dataIndex: 'doubt_degree',
    width: 100,
    key: 'doubt_degree',
  },
];

const popuptableColumns = [
  {
    title: '光缆段编码',
    dataIndex: 'cbl_sect_no',
    width: 300,
  },
  {
    title: '接入号数',
    dataIndex: 'accs_nos',
    customRender: (text) => text.value.length,
    width: 100,
  },
  {
    title: '接入号清单',
    dataIndex: 'accs_nos',
    customRender: (text) => text.value.join(','),
    width: 200,
  },
];

const olt_columns = [
  // {
  //   title: 'olt_id',
  //   dataIndex: 'olt_id',
  // },
  {
    title: 'ip地址',
    dataIndex: 'olt_ip',
  },
  {
    title: '报障接入号数量',
    dataIndex: 'count_olt_accs_nbr_no',
  },
  {
    title: 'olt名称',
    dataIndex: 'olt_name',
  },

  {
    title: '安装地址',
    dataIndex: 'olt_instl_addr_desc',
  },
  {
    title: '机房名称',
    dataIndex: 'room_name',
  },
  {
    title: '局站名称',
    dataIndex: 'station_name',
  },
  {
    title: '操作',
    dataIndex: 'actions',
  },
  // {
  //   title: '标签',
  //   dataIndex: 'tags',
  // }
];

const frstobd_columns = [
  // {
  //   title: 'obd_id',
  //   dataIndex: 'obd_id',
  // },
  {
    title: 'obd编码',
    dataIndex: 'frst_obd_no',
  },
  {
    title: '报障接入号数量',
    dataIndex: 'count_frstobd_accs_nbr_no',
  },
  {
    title: 'obd名称',
    dataIndex: 'frst_obd_name',
  },

  {
    title: 'obd端口',
    dataIndex: 'frst_obd_phy_port_no',
  },
  // {
  //   title: '标签',
  //   dataIndex: 'tags',
  // }
];

const scndobd_columns = [
  // {
  //   title: 'obd_id',
  //   dataIndex: 'obd_id',
  // },
  {
    title: 'obd编码',
    dataIndex: 'scnd_obd_no',
  },
  {
    title: '报障接入号数量',
    dataIndex: 'count_scndobd_accs_nbr_no',
  },
  {
    title: 'obd名称',
    dataIndex: 'scnd_obd_name',
  },

  {
    title: 'obd端口',
    dataIndex: 'scnd_obd_phy_port_no',
  },
  // {
  //   title: '标签',
  //   dataIndex: 'tags',
  // }
];




const subtable_coloums = [
  {
    title: 'pon口id',
    dataIndex: 'pon_id',
  },
  {
    title: 'pon口编码',
    dataIndex: 'pon_code',
  },
  {
    title: '接入号',
    dataIndex: 'accs_nbr_no',
  },
  {
    title: 'CRM产品ID',
    dataIndex: 'crm_prod_id',
  },
  {
    title: '板卡名称',
    dataIndex: 'card_name',
  },

  {
    title: '一级分光器编码',
    dataIndex: 'frst_obd_no',
  },
  {
    title: '一级分光器端口编码',
    dataIndex: 'frst_obd_phy_port_no',
  },
  {
    title: '二级分光器编码',
    dataIndex: 'scnd_obd_no',
  },
  {
    title: '二级分光器端口编码',
    dataIndex: 'scnd_obd_phy_port_no',
  },
  {
    title: '操作',
    dataIndex: 'actions',
  },
  {
    title: '检测结果',
    dataIndex: 'poncheck_result',
  },

];

const obd_subtable_coloums = [
  {
    title: '接入号',
    dataIndex: 'accs_nbr_no',
  },
  {
    title: 'CRM产品ID',
    dataIndex: 'crm_prod_id',
  },
  {
    title: '板卡名称',
    dataIndex: 'card_name',
  },
  {
    title: 'pon口编码',
    dataIndex: 'pon_code',
  },
  {
    title: '一级分光器编码',
    dataIndex: 'frst_obd_no',
  },
  {
    title: '一级分光器端口编码',
    dataIndex: 'frst_obd_phy_port_no',
  },
  {
    title: '二级分光器编码',
    dataIndex: 'scnd_obd_no',
  },
  {
    title: '二级分光器端口编码',
    dataIndex: 'scnd_obd_phy_port_no',
  },
];

const accs_nbr_nos_columns = [
  {
    title: '光缆段编码',
    dataIndex: 'cbl_sect_no',
  },

  {
    title: '接入号数量',
    dataIndex: 'accs_nbr_no_count',
  },
  {
    title: '接入号清单',
    dataIndex: 'accs_nbr_no',
    customRender: (text) => text.value.join(','),
    ellipsis: true,
  },

  {
    title: '光缆段名称',
    dataIndex: 'cbl_sect_name',
  },
];

const bseSectColumns = [
  {
    title: '管道段编码',
    dataIndex: 'bse_sect_no',
    key: 'bse_sect_no',
    fixed: 'left',
    customRender: (text) => {
      if (Array.isArray(text.value)) {
        // 如果 text.value 是数组，则将其元素用逗号连接
        return text.value.join(', ');
      } else {
        // 如果 text.value 不是数组，则直接返回其值
        return String(text.value);
      }
    },
  },
  {
    title: '管道段名称',
    dataIndex: 'bse_sect_name',
    customRender: (text) => {
      if (Array.isArray(text.value)) {
        // 如果 text.value 是数组，则将其元素用逗号连接
        return text.value.join(', ');
      } else {
        // 如果 text.value 不是数组，则直接返回其值
        return String(text.value);
      }
    },
  },
  {
    title: 'A端名称',
    dataIndex: 'a_bse_eqp_name',
    customRender: (text) => {
      if (Array.isArray(text.value)) {
        // 如果 text.value 是数组，则将其元素用逗号连接
        return text.value.join(', ');
      } else {
        // 如果 text.value 不是数组，则直接返回其值
        return String(text.value);
      }
    },
  },
  {
    title: 'A端编码',
    dataIndex: 'a_bse_eqp_no',
    customRender: (text) => {
      if (Array.isArray(text.value)) {
        // 如果 text.value 是数组，则将其元素用逗号连接
        return text.value.join(', ');
      } else {
        // 如果 text.value 不是数组，则直接返回其值
        return String(text.value);
      }
    },
  },
  {
    title: 'Z端名称',
    dataIndex: 'z_bse_eqp_name',
    customRender: (text) => {
      if (Array.isArray(text.value)) {
        // 如果 text.value 是数组，则将其元素用逗号连接
        return text.value.join(', ');
      } else {
        // 如果 text.value 不是数组，则直接返回其值
        return String(text.value);
      }
    },
  },
  {
    title: 'Z端编码',
    dataIndex: 'z_bse_eqp_no',
    customRender: (text) => {
      if (Array.isArray(text.value)) {
        // 如果 text.value 是数组，则将其元素用逗号连接
        return text.value.join(', ');
      } else {
        // 如果 text.value 不是数组，则直接返回其值
        return String(text.value);
      }
    },
  },
];

const bseSect_subtable_Columns = [
  {
    title: '光缆段编码',
    dataIndex: 'cbl_sect_no',
    key: 'cbl_sect_no',
    fixed: 'left',
  },
  {
    title: '接入号清单',
    dataIndex: 'accs_nos',
    customRender: (text) => {
      if (Array.isArray(text.value)) {
        // 如果 text.value 是数组，则将其元素用逗号连接
        return text.value.join(',');
      } else {
        // 如果 text.value 不是数组，则直接返回其值
        return 1;
      }
    },
  },
  {
    title: '接入号个数',
    dataIndex: 'accs_nos',
    customRender: (text) => {
      if (Array.isArray(text.value)) {
        // 如果 text.value 是数组，则将其元素用逗号连接
        return text.value.length;
      } else {
        // 如果 text.value 不是数组，则直接返回其值
        return 1;
      }
    },
  },
];

//================================================== Gis相关绘图方法========================================================================
//Gis清除绘图
const ClearDraw = () => {
  // MapContainer.layersGroup.clearLayers();
  MapContainer.overLayer.clearLayers();
};

//================================================== 逻辑图绘制相关方法========================================================================

const container = ref(null);
const { DrawCable, svgRef, drawGraph, treeData } = useCableFaultGraph(container);
console.log(DrawCable);


//=======================================================采用自定义的drawMap函数，应对菜单的特殊选项，例如标记位置和保存位置功能========================

const groupfaultposition = ref(<any>[]);
const pulseIcon = Ai.IconPulse({
  iconSize: [20, 20],
  color: '#FF0000',
});

const drawMap = async (city) => {


const ak = await getAk();

console.log('ak',ak);
  

const map = new Ai.Map('mapRef', {
  ak: ak,
  crs: '',
  mapType: '',
  maxZoom: 19,
  closePopupOnClick: true,
  contextmenu: true,
  contextmenuWidth: 140,
  contextmenuItems: [
    {
      text: '获取坐标',
      callback: showCoordinates,
    },
    {
      text: '测量距离',
      callback: distince,
    },
    {
      text: '标记位置',
      callback: addPulseIconGroupfault,
    },
    {
      text: '保存位置',
      callback: saveGroupfaultPosition,
    },
 
  ],
});

//江苏底图
const maplayer = Ai.TileLayer(
   '/gis-platform-new/elec/js_map/server/wmts',
);


printer = Ai.MapPrint({
    tileLayer: maplayer,
    exportOnly: true,
    hidden:true,
    export: "picture",
}).addTo(map);


map.addLayer(maplayer);

// const layersGroup = Ai.LayerGroup(); //定义图层数组
// map.addLayer(layersGroup);

const overLayer = new Ai.FeatureGroup();
map.addLayer(overLayer);




function addPulseIconGroupfault(e) {
  let pos =e.latlng;
  console.log('e',e);
  
  //插入记录
  groupfaultposition.value.push(e.latlng);

  const marker = new Ai.Point(pos, { icon: pulseIcon });
  marker.on('click', function () {

    console.log('marker._latlng',marker._latlng);
    
    map.removeLayer(marker);

    removeMarkerByLatLng(groupfaultposition.value,marker._latlng);



  });
  overLayer.addLayer(marker);
  console.log('overLayer',overLayer);

  console.log(  JSON.stringify(groupfaultposition.value) );
  



  return marker;
};


function saveGroupfaultPosition(){


  //将障碍点保存
  ExecuteUpdateGroupFaultLog(accs_nbr_nos.value);


}

// const grahicLayer = Ai.GeoJSON();
// map.addLayer(grahicLayer);

function distince() {
  Ai.MeasureTool(map, 'distince', function () {}); //测距
}

const MapContainer = { map: map,overLayer: overLayer, ak: ak }; //,grahicLayer:grahicLayer
// pantoCity(map, city);

pantoCity(MapContainer, city);

map.on('zoomend', () => {
  console.log('现在的缩放等级是', map.getZoom());
});

return MapContainer;
};




const removeMarkerByLatLng = (groupfaultposition,latlng) => {  
  // 找到匹配元素的索引  
  const index = groupfaultposition.findIndex(coord =>   
    coord.lat === latlng.lat && coord.lng === latlng.lng  
  );  
  
  // 如果找到了匹配的元素（index !== -1），则删除它  
  if (index !== -1) {  
    groupfaultposition.splice(index, 1);  
  }  
};  


//================================================== 挂载时执行========================================================================







onMounted(async () => {
  //画gis底图
  // const ak = await getAk();
  queryComplaint();
  console.log('MapContainer',MapContainer);

  if(MapContainer!=null ){
    ClearDraw();
    MapContainer= null;
  }
  MapContainer = await drawMap(city);

  map = MapContainer.map;



  //外力点图层默认加载
  const layername = '_externalforce';
  
  ExternalForceLayer = Ai.WMSLayer("/gis-platform-new/ctgis-engine/service/JS_YUANFENG_B/wms?clid=475BBFFB01D447539890C54F3237DCD3", {
    layers: 'JS_YUANFENG_B:' +city.value+ layername,
    format: 'image/png',
    transparent: true,
  });



  map.addLayer(ExternalForceLayer);


  //增加比例尺显示
  pantoCity(MapContainer, city.value);

  Ai.Scale({ position: 'bottomright' }).addTo(MapContainer.map);

  //mainFunction(accs_nbr_nos.value,'',city.value);

});

//================================================== 主函数========================================================================

async function mainFunction(accs_nbr_nos, accs_nbr_nos_normal, city) {

  groupfaultposition.value = [];
  // 将主逻辑封装在一个异步函数中
  if (!accs_nbr_nos || accs_nbr_nos.trim() === '') {
    showalert('请输入报障的接入号清单，用逗号或者空格分隔！');
    return;
  }

  const accs_nbr_nos_list = splitItemsBySeparators(accs_nbr_nos);
  mainLoading.value = true;
  try {
    ClearDraw();
    ExecuteGetCblinfo(accs_nbr_nos_list, city, true); 
    await ExecuteGetResourceinfo(accs_nbr_nos_list, city);
    ExecuteDrawBseSect(accs_nbr_nos_list, city);
    //取综调工单情况
    getNocFaultOrder();
    
    //这个方法记录日志

    ExecuteGroupFaultLog(accs_nbr_nos_list);
    ExecutegetGroupFaultLog(accs_nbr_nos_list);
    // await DrawCable(accs_nbr_nos_list, city);
    drawGraph();
    if (accs_nbr_nos_normal && accs_nbr_nos_normal.trim() !== '') {
      const accs_nbr_nos_normal_list = splitItemsBySeparators(accs_nbr_nos_normal);
      // 等待正常号码查询的异步调用完成
      await ExecuteGetCblinfo(accs_nbr_nos_normal_list, city, false);
    }
    mainLoading.value = false;
  } catch (e) {
    mainLoading.value = false;
  }
}

// // 调用主函数
// mainFunction().catch(error => {
//   // 处理任何在异步操作中抛出的错误
//   console.error('An error occurred:', error);
// });

//================================================== 相关工具方法========================================================================
function extractAndJoinUniqueValues(items, outerKey, innerKeys, separator = ',') {
  const uniqueValues = new Set();

  for (const item of items) {
    const innerArray = item[outerKey];
    if (!Array.isArray(innerArray)) {
      continue;
    }

    for (const innerObj of innerArray) {
      for (const key of innerKeys) {
        if (Object.prototype.hasOwnProperty.call(innerObj, key)) {
          uniqueValues.add(innerObj[key]);
        }
      }
    }
  }

  // 将Set转换为数组，并连接成一个字符串
  return Array.from(uniqueValues).join(separator);
}
console.log(extractAndJoinUniqueValues);

//页面激活的tabs的key预设值
const activeKey = ref('cbl_sect_judge');
const GraphAndMapActiveKey = ref('gis_map');
// const activeCollapseKey = ref(['tables','map']);
const GraphCollapseactiveKey = ref(['1']);
const TableCollapseactiveKey = ref(['1']);
// const  MakeCauseactiveKey = ref(['1']);

const justifyOptions = reactive<FlexProps['justify'][]>([
  'flex-start',
  'center',
  'flex-end',
  'space-between',
  'space-around',
  'space-evenly',
]);

const alignOptions = reactive<FlexProps['align'][]>(['flex-start', 'center', 'flex-end']);
const justify = ref(justifyOptions[0]);
const alignItems = ref(alignOptions[0]);
console.log(justify, alignItems);

//================================================== 抽屉相关方法========================================================================

const Drawervisible = ref<boolean>(false);
const afterDrawerVisibleChange = (bool: boolean) => { };
const showDrawer = () => {
  Drawervisible.value = true;
};

// const layout = {
//   labelCol: { span: 4 },
//   wrapperCol: { span: 8 },
// };

// const validateMessages = {
//   required: '${label} 是必填的!',
//   types: {
//     email: '${label} is not a valid email!',
//     number: '${label} is not a valid number!',
//   },
//   number: {
//     range: '${label} must be between ${min} and ${max}',
//   },
// };

// const formState = reactive({
//   cause: {
//     causeName: '元凤生成群障测试',
//     recordType: 'project',
//     faultType: '04',
//     causeType: 'yfptlmportData',
//     startTime: '',
//     endTime: '',
//     areaId: '',
//     deviceType: '701',
//     deviceCode: 'AQP.MYYEP-HW-OLT004',
//     deviceIp: '',
//     maintainTimes: '0',
//     dutyUserId: '',
//     createUser: '',
//     remark: '',
//     remark2: '',
//     numberList:{
//       relnumber: []
//     },
//     isValid: 'Y',

//   },
// });
// const onFinish = (values: any) => {
//   console.log('Success:', values);
// };

//================================================== 生成群障弹窗========================================================================
// const modalText = ref<string>('填写群障信息');
const visible = ref<boolean>(false);
const confirmLoading = ref<boolean>(false);
const userStore = useUserStoreWithOut();
console.log('userStore',userStore);
console.log('userStore.getUserInfo',userStore.getUserInfo);


const operator = userStore.getUserInfo.realName;
const causeName = ref('元凤平台群障');
const makecauseTime = ref(new Date());

//生成群障名称
function formatDateTime(dateObj) {
  const year = dateObj.getFullYear();
  const month = String(1 + dateObj.getMonth()).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  const seconds = String(dateObj.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day}日${hours}:${minutes}:${seconds}`;
}

function generateStringWithDateTime(cityName, username) {
  const now = new Date(); // 获取当前日期和时间对象
  const formattedDateTime = formatDateTime(now);

  makecauseTime.value = now;
  causeName.value = `${cityName}${username}于元凤平台${formattedDateTime}生成群障`;

  // return causeName;
}

const devType = ref('cbl');
const makecauseDesc = computed(() => {
  let count = 0;
  let description = '';

  if (devType.value === 'cbl') {
    count = selectedCblSectRowKeys.value.length;
    description = `您将拦截的是${count}条光缆段`;
  } else if (devType.value === 'pon') {
    count = selectedponRowKeys.value.length;
    description = `您将拦截的是${count}个PON口`;
  } else if (devType.value === 'frstobd') {
    count = selectedfrstobdRowKeys.value.length;
    description = `您将拦截的是${count}个一级分光器的接入号`;
  } else if (devType.value === 'scndobd') {
    count = selectedscndobdRowKeys.value.length;
    description = `您将拦截的是${count}个二级分光器的接入号`;
  }

  return description;
});

const showModal = (devtype) => {
  remark2.value = '';
  generateStringWithDateTime(city.value, userStore.getUserInfo.realName);
  // causeName.value = generateStringWithDateTime(city.value,userStore.getUserInfo.realName);
  visible.value = true;
  devType.value = devtype;
};

const remark = ref('');
const remark2 = ref('');

const handleOk = async (causeName, city, devType) => {
  confirmLoading.value = true;

  let devCodes;

  console.log('causeName', causeName);
  console.log('city', city);
  console.log('devType', devType);

  let devTypeDesc;
  switch (devType) {
    case 'cbl':
      console.log('Value is cbl');
      devCodes = flattenDevCodes(selectedCblSectRowKeys.value);
      devTypeDesc = '光缆段编码：';
      break;
    // case 'bse':
    //   console.log('Value is bse');
    //   devCodes = selectedbseSectRows.value;
    //   break;
    case 'pon':
      console.log('Value is pon');
      // console.log('selectedoltRows.value',selectedoltRows.value);
      devCodes = flattenDevCodes(selectedponRowKeys.value);
      devTypeDesc = 'PON口ID：';

      break;
    case 'frstobd':
      console.log('Value is frstobd');
      console.log('selectedfrstobdRowKeys.value', selectedfrstobdRowKeys.value);

      devTypeDesc = 'OBD分光器ID：';

      devCodes = flattenDevCodes(selectedfrstobdRowKeys.value);
      break;

    case 'scndobd':
      console.log('Value is scndobd');
      console.log('selectedscndobdRowKeys.value', selectedscndobdRowKeys.value);

      devTypeDesc = 'OBD分光器ID：';

      devCodes = flattenDevCodes(selectedscndobdRowKeys.value);
      break;
  }

  console.log('devCodes', devCodes);

  //把设备类型送回去，devType对应关系如下
  //如果是光缆段，cbl
  //bse
  //olt
  //obd
  //devCodes存储实际的设备的编码，如果是olt就是IP地址
  //在后台进行号码关联

  //       CauseId
  // :
  // "1548619410"
  // Message
  // :
  // "操作成功！"
  // ReturnCode
  // :
  // "1"

  // console.log('makeCauseInfoResult',makeCauseInfoResult.value);

  const causeStartTime = formatDateTime(makecauseTime.value);
  //拦截一天
  const endDate = new Date(makecauseTime.value.getTime() + 86400000);
  const causeEndTime = formatDateTime(endDate);

  // const causeName = ref('元凤平台群障');
  // const makecauseTime = ref(new Date());

  for (let i = 0; i < devCodes.length; i++) {
    const devCode = devCodes[i]; // 在控制台输出每个devCode

    makeCauseInfo.value = {
      causeName: causeName,
      city: city,
      devType: devType,
      devCode: devCode,
      startTime: makecauseTime.value,
      endTime: endDate,
      operator: userStore.getUserInfo.realName,
      remark: remark.value,
      remark2: remark2.value,
    };
    console.log('makeCauseInfo.value', makeCauseInfo.value);
    makeCauseInfoResult.value = await makeCauseInfoService.doCreateNew(
      '/api/accs_nbr_no/makecause',
    );

    const message = makeCauseInfoResult.value.Message;
    if (makeCauseInfoResult.value.ReturnCode == 1) {
      notification.open({
        message: message,
        description: `群障causeID为：${makeCauseInfoResult.value.CauseId} ,\n
            障碍名称为：${causeName}${devCode},
            设备类型： ${devType} , \n
            ${devTypeDesc} ${devCode} ,\n
            群障拦截时间： ${causeStartTime} 至  ${causeEndTime} `,
        duration: 0,
        icon: () => h(CheckCircleOutlined, { style: 'color: green' }),
      });
    } else {
      notification.open({
        message: message,
        duration: 0,
        icon: () => h(CloseCircleOutlined, { style: 'color: red' }),
      });
    }
  }

  visible.value = false;

  confirmLoading.value = false;

  //把选中行里面的编码拉平成一个数组的方法：
  function flattenDevCodes(devCodes) {
    return devCodes.reduce((acc, val) => {
      // 检查当前元素是否是数组
      if (Array.isArray(val)) {
        // 如果是数组，则使用concat连接数组
        return acc.concat(val);
      } else {
        // 如果不是数组，则直接推入元素
        acc.push(val);
        return acc;
      }
    }, []); // 初始值是一个空数组
  }
};
const opticalTabsClick = (val) => {
  opticalCable.value = val;
};
const gisTabsClick = (val) => {
  gisCable.value = val;
  if (val == 2) {
    nextTick(() => {
      drawGraph();
    });
  }
}




const ExternalForceLayerchecked = ref<boolean>(true);
let ExternalForceLayer = undefined;

function changeExternalForceLayerShow() {

  if (ExternalForceLayerchecked.value) {


    const layername = '_externalforce';


    const layers = 'JS_YUANFENG_B:' +city.value+ layername;

    console.log('layers',layers);
    
    ExternalForceLayer = Ai.WMSLayer("/gis-platform-new/ctgis-engine/service/JS_YUANFENG_B/wms?clid=475BBFFB01D447539890C54F3237DCD3", {
      layers: 'JS_YUANFENG_B:' +city.value+ layername,
      format: 'image/png',
      transparent: true,
    });




    map.addLayer(ExternalForceLayer);
  } else {

    map.removeLayer(ExternalForceLayer);

  }

}


//查询在途网络障碍单  


const queryComplaintInfoService = useInfo({
  rootPath: '/graph-rest-api',
});
const { info: queryComplaintInfo, loading: queryComplaintloading } = queryComplaintInfoService;
const complainTableData = ref(<any>[]);

const ComplaintListData = ref(<any>[]);

  
  const queryOltIpInfoService = useInfo({
  rootPath: '/graph-rest-api',
});
// query_oltip_by_accs_nbr_no
const { info: queryOltIpInfo, loading: queryOltIploading } = queryOltIpInfoService;


// 根据 mng_ip_addr 字段汇聚  
const mngIpAddrAggregated = ref(<any>[]); 
  
// 根据 room_name 字段汇聚  
const roomNameAggregated = ref(<any>[]); 



async function queryComplaint() {
  roomNameAggregated.value  = [];
  mngIpAddrAggregated.value = [];
  ComplaintListData.value = [];


  let [startTime,endTime]=[...complaintDateRanger.value];
  

  //实时模式下，要求不能查太长时间的，会报错
  if(QueryMode.value == 'realtime'){
    complaintDateRanger.value = adjustDates(startTime, endTime);  
    [startTime,endTime]=[...complaintDateRanger.value];
    console.log(complaintDateRanger.value); // { start: '2023-09-05', end: '2023-09-15' }
  }

  if(startTime==null || startTime==undefined){

   startTime=dayjs().add(-1, 'd')
   complaintDateRanger.value[0] = dayjs().add(-1, 'd')
  }

  if(endTime==null || endTime==undefined){

    endTime=dayjs()
    complaintDateRanger.value[1] = dayjs()

  }

  queryComplaintInfo.value = {
    params: {
      business: "kd",
      nat: cityidtocompanyname(city.value),
      startTime: startTime.format('YYYY-MM-DD HH:mm:ss'),  
      endTime: endTime.format('YYYY-MM-DD HH:mm:ss'),  
    }

  };

  console.log('queryComplaintInfo.value ', queryComplaintInfo.value);

  if(QueryMode.value == 'realtime'){

    queryComplaintInfo.value = await queryComplaintInfoService.doCreateNew(
      '/api/faulthologram/querycomplaint',
    );

    console.log('queryComplaintInfo.value ', queryComplaintInfo.value);
  }
  else if (QueryMode.value == 'cache'){
    queryComplaintInfo.value = await queryComplaintInfoService.doCreateNew(
      '/api/faulthologram/query_cache_tb_complaints',
    );

    console.log('queryComplaintInfo.value ', queryComplaintInfo.value);


  }
  
  //绘制折线图
  // drawLineChart( queryComplaintInfo.value.value);

  

  if (Array.isArray(queryComplaintInfo.value.value)) {
    // tableDataNow.value = queryfaultordernowInfo.value.value;

    complainTableData.value = queryComplaintInfo.value.value;
  } else {
    // tableDataNow.value = [queryfaultordernowInfo.value.value];
    complainTableData.value = [queryComplaintInfo.value.value];


  }

  // 过滤数组  
  complainTableData.value = complainTableData.value.filter(obj =>
    obj.nat.includes(cityidtocompanyname(city.value))
  );


  complainTableDataBak.value = complainTableData.value;





  // 使用reduce函数来统计每个地址前缀出现的次数  
  const addressPrefixes = complainTableData.value.reduce((acc, cur) => {
    // 使用正则表达式匹配第一个数字之前的字符串  
     // 如果address为undefined，则跳过此次循环  
     if (!cur.address) {  
      return acc;  
    }  
    const prefix = extractPrefix(cur.address);
    if (acc[prefix]) {
      acc[prefix]++;
    } else {
      acc[prefix] = 1;
    }
    return acc;
  }, {});

  // 将对象转换为数组，并基于计数进行排序  
  const sortedPrefixes = Object.entries(addressPrefixes).sort((a, b) => b[1] - a[1]);

  // 提取排名前50的地址  
  const top50Addresses = sortedPrefixes.slice(0, 50);

  console.log(top50Addresses);

  ComplaintListData.value = top50Addresses;
  ComplaintListData.value = ComplaintListData.value.filter(obj =>
    obj[1]>1
  );



  
  //把汇聚的申告，关联oLT信息后进一步汇聚：

  function getDetailNumsByPrefixes(complaintListData, complainTableDataBak) {  
    // 初始化一个空对象来存储每个前缀对应的detail_num数组  
    let detailNumsByPrefix = <any>[];  
  
    // 遍历 ComplaintListData.value 中的每个 item  
    complaintListData.value.forEach(item => {  
      const prefix = item[0];  
  
      // 使用 filter 和 map 从 complainTableDataBak.value 中提取匹配的 detail_num  
      const detailNums = complainTableDataBak.value  
        .filter(obj =>  ()=>{

          if( obj.address !== '' && obj.address !== null ){

            if(obj.address.startsWith(prefix)){
              return true;

            }
            
          }
          else{
            return false 
          }  

        })  
        .map(obj => obj.detail_num);  
  
      // 将结果存储到 detailNumsByPrefix 对象中  
      detailNumsByPrefix = [...detailNumsByPrefix,...detailNums];  
    });  
  
    // 返回包含所有前缀和对应detail_num数组的对象  
    return detailNumsByPrefix;  
  }  
  const detailNumsResult = getDetailNumsByPrefixes(ComplaintListData, complainTableDataBak);  
  console.log('detailNumsResult',detailNumsResult);

  queryOltIpInfo.value = {
    accs_nbr_nos:detailNumsResult,
    city: city.value
  }

// todo:准备恢复
  // queryOltIpInfo.value = await queryOltIpInfoService.doCreateNew(
  //   '/api/accs_nbr_no/query_oltip_by_accs_nbr_no',
  // );

  // console.log('queryOltIpInfo.value ',queryOltIpInfo.value );
  
  // /**  
  //  * 根据指定字段汇聚数组，并添加接入号清单  
  //  * @param {Array} data - 输入的数组  
  //  * @param {String} field - 要汇聚的字段名  
  //  * @returns {Array} - 汇聚后的数组，每个元素包含字段值、对应的记录数和接入号清单  
  //  */  
  //  function aggregateByField(data, field) {  
  //     // 初始化一个空对象用于存储计数和接入号清单  
  //     const countObject = {};  
  
  //     // 遍历数组进行计数和接入号收集  
  //     data.forEach(item => {  
  //         const fieldValue = item[field];  
  //         if (!countObject[fieldValue]) {  
  //             countObject[fieldValue] = { count: 0, accs_nbr_nos: [] };  
  //         }  
  //         countObject[fieldValue].count += 1;  
  //         countObject[fieldValue].accs_nbr_nos.push(item.accs_nbr_no);  
  //     });  
  
  //     // 将计数结果转化为数组形式，并根据count排序  
  //     return Object.entries(countObject).map(([key, value]) => ({  
  //         [field]: key,  
  //         count: value.count,  
  //         accs_nbr_nos: value.accs_nbr_nos  
  //     })).sort((a, b) => b.count - a.count); // 根据count降序排序  
  // }  
  
  // // 示例使用  
  // const data = queryOltIpInfo.value.data; // 假设这是你的输入数组  
  
  // // 根据 mng_ip_addr 字段汇聚  
  //  mngIpAddrAggregated.value = aggregateByField(data, 'mng_ip_addr');  
  // console.log('根据 mng_ip_addr 汇聚的结果:', mngIpAddrAggregated);  
  
  // // 根据 room_name 字段汇聚  
  //  roomNameAggregated.value = aggregateByField(data, 'room_name');  
  // console.log('根据 room_name 汇聚的结果:', roomNameAggregated);




}


function extractPrefix(address) { 
    
    address = address.replace(/FTTH|光纤到户/g, '');
      
      if(city.value== 'nj'){
        const regex = /(.+?)(?=\d+(?:[^\d\s]*\d+)*[^\d\s]*)/;  
        const match = address.match(regex);  
        if (match && match.length > 1) {  
            return match[1];  
        }  
        else{
          return address.match(/^[^\d]*/)[0];
  
        }
      }  
      else{
        return address.match(/^[^\d]*/)[0];
      }
    
  }  
    

// 限制时间不能超过7天，超过的话，修改开始时间到7天以内   
// 如果时间差不超过 7 天，则返回原日期  

function adjustDates(start, end) {  
    const diffInDays = end.diff(start, 'day');  
    if (diffInDays > 7) {  
        const newStartTime = end.subtract(7, 'day');  
        return  [newStartTime,end ] 
    }  
    return [start,end ] 
}  

const modalName = ref('宽带申告单详情');
const showmodalopen = ref(false);
const modalinfo = ref({});
function showmodal(record, name) {

  modalName.value = name;
  showmodalopen.value = true;
  modalinfo.value = record;

}
const searchText = ref('');
const searchedColumn = ref('');

const searchInput = ref();
const handleTableSearch = (selectedKeys, confirm, dataIndex) => {
  confirm();
  searchText.value = selectedKeys[0];
  searchedColumn.value = dataIndex;
};

const handleReset = (clearFilters) => {
  clearFilters({ confirm: true });
  searchText.value = '';
};


const complainColumns = [
  {
    title: '分公司',
    dataIndex: 'nat',
    key: 'nat',
  },
  // {
  //   title: '宽带账号',
  //   dataIndex: 'accounts',
  //   key: 'accounts',
  //   customFilterDropdown: true,

  //   onFilter: (value, record) => {
  //     if (record.accounts) {
  //       return record.accounts.toString().toLowerCase().includes(value.toLowerCase())
  //     }
  //   }
  //   ,
  //   onFilterDropdownOpenChange: (visible) => {
  //     if (visible) {
  //       setTimeout(() => {
  //         searchInput.value.focus();
  //       }, 100);
  //     }
  //   },
  // },
  {
    title: '宽带接入号',
    dataIndex: 'detail_num',
    key: 'detail_num',
    customFilterDropdown: true,
    onFilter: (value, record) => {
      if (record.detail_num) {
        return record.detail_num.toString().toLowerCase().includes(value.toLowerCase())
      }
    }
    ,
    onFilterDropdownOpenChange: (visible) => {
      if (visible) {
        setTimeout(() => {
          searchInput.value.focus();
        }, 100);
      }
    },
  },
  {
    title: '业务类型',
    dataIndex: 'bussiness',
    key: 'bussiness',
    customRender: (text) => {
      if (text.value == 'kd') {
        // 如果 text.value 是数组，则将其元素用逗号连接
        return '宽带';
      } else {
        // 如果 text.value 不是数组，则直接返回其值
        return text;
      }
    },
  },
  {
    title: '创建理由',
    dataIndex: 'create_cause',
    key: 'create_cause',
  },
  {
    title: '创建信息',
    dataIndex: 'create_info',
    key: 'create_info',

  },
  // {
  //   title: '业务ID',
  //   dataIndex: 'bill_id',
  //   key: 'bill_id',

  // },
  {
    title: '地址',
    dataIndex: 'address',
    key: 'address',
    customFilterDropdown: true,
    onFilter: (value, record) => {

      if (record.address) {

        return record.address.toString().toLowerCase().includes(value.toLowerCase());
      }
    }
    ,
    onFilterDropdownOpenChange: (visible) => {
      if (visible) {
        setTimeout(() => {
          searchInput.value.focus();
        }, 100);
      }
    },


    // onFilter: (value, record) =>
    //   record.cfsName.toString().toLowerCase().includes(value.toLowerCase()),
    // onFilterDropdownOpenChange: (visible) => {
    //   if (visible) {
    //     setTimeout(() => {
    //       searchInput.value.focus();
    //     }, 100);
    //   }
    // },
  },
  // {
  //   title: '创建人',
  //   dataIndex: 'create_oper',
  //   key: 'create_oper',
  // },

  // {
  //   title: '联系号码',
  //   dataIndex: 'user_link_phone',
  //   key: 'user_link_phone',
  // },
  // {
  //   title: '联系人',
  //   dataIndex: 'user_link_people',
  //   key: 'user_link_people',
  // },
  // {
  //   title: '申告来源',
  //   dataIndex: 'complaint_src',
  //   key: 'complaint_src',
  // },
  // {
  //   title: 'oltip地址',
  //   dataIndex: 'olt_ip',
  //   key: 'olt_ip',
  //   customFilterDropdown: true,
  //   onFilter: (value, record) => {
  //     if (record.olt_ip) {
  //       return record.olt_ip.toString().toLowerCase().includes(value.toLowerCase())
  //     }
  //   }
  //   ,
  //   onFilterDropdownOpenChange: (visible) => {
  //     if (visible) {
  //       setTimeout(() => {
  //         searchInput.value.focus();
  //       }, 100);
  //     }
  //   },
  // },
  {
    title: '创建时间',
    dataIndex: 'time',
    key: 'time',
  },
  {
    title: '详情', dataIndex: '详情', key: '详情',
  }
];

function cityidtocompanyname(text) {


  switch (text) {

    case 'js':
      return ['南京分公司', '无锡分公司', '徐州分公司', '常州分公司',
        '苏州分公司', '南通分公司', '连云港分公司', '淮安分公司',
        '盐城分公司', '扬州分公司', '镇江分公司', '泰州分公司',
        '宿迁分公司']

    case 'nj':
      return ['南京分公司'];

    case 'zj':
      return ['镇江分公司'];

    case 'wx':
      return ['无锡分公司'];

    case 'sz':
      return ['苏州分公司'];

    case 'nt':
      return ['南通分公司'];

    case 'yz':
      return ['扬州分公司'];

    case 'yc':
      return ['盐城分公司'];

    case 'xz':
      return ['徐州分公司'];

    case 'ha':
      return ['淮安分公司'];

    case 'lyg':
      return ['连云港分公司'];

    case 'cz':
      return ['常州分公司'];

    case 'tz':
      return ['泰州分公司'];

    case 'sq':
      return ['宿迁分公司'];

  }

}

const complainTableDataBak = ref();
const linkisActive = ref();
function filterComplaintTableData(item) {

  linkisActive.value  = item[0];
  complainTableData.value = complainTableDataBak.value;


  // 过滤数组  
  complainTableData.value = complainTableData.value.filter(obj =>
       obj.address !== null &&  extractPrefix(obj.address).startsWith(item[0])
  );

  

}

//筛选多个地址的申告数据
function filterMutipleComplaintData(items) {

  console.log('complainTableData',complainTableData.value);
  

  let newcomplainTableData= ref([]) ;
  complainTableData.value = complainTableDataBak.value;
  let addresses = <any>[];
  for (let index = 0; index < items.length; index++) {
    const element = items[index];

    if(element.checked){

      addresses.push(element[0]);


    }
    
  }


  console.log('addresses',addresses);
  

  if(addresses.length>0 ){

    for (let index = 0; index < addresses.length; index++) {
      const address = addresses[index];

      console.log('@@@address',address);

      // 过滤数组  
      const temp = complainTableData.value.filter(obj =>
           obj.address !== null &&  extractPrefix(obj.address).startsWith(address)
      );

      console.log('temp',temp);
      
      for (let index = 0; index < temp.length; index++) {
        const element = temp[index];
        console.log('element',element);

         newcomplainTableData.value.push(element);

      }


    }

    complainTableData.value = [];
    for (let index = 0; index < newcomplainTableData.value.length; index++) {
      const element = newcomplainTableData.value[index];
      
      complainTableData.value.push(element);
    }

    // complainTableData.value = newcomplainTableData;

  }

  console.log('complainTableData.value',complainTableData.value);
  



}


function filterComplaintyAggreatedAccsnbr(item) {

complainTableData.value = complainTableDataBak.value;


  // 对数组进行过滤  
  complainTableData.value = complainTableData.value.filter(obj =>  
    item.accs_nbr_nos.includes(obj.detail_num)  
  );  



}


function complatinToAccsnbrno() {
  const detailNumsString = complainTableData.value.map(obj => obj.detail_num).join(',');

  accs_nbr_nos.value = detailNumsString;
}




//页面自动刷新
   // 定义刷新间隔时间，5分钟 = 5 * 60 * 1000 毫秒，10分钟 = 10 * 60 * 1000 毫秒  
  const  refreshIntervalMinute = ref(5); // 这里设置为5分钟，你可以根据需要修改为10分钟  
  // 5 * 60 * 1000
  // 定时器ID，用于在组件卸载时清除定时器  
  let timerId ;  
  
  console.log('检查timerId',timerId);
  
  // 定时刷新页面的函数  
  const refreshComplaint= () => {  
    console.log('自动刷新申告清单和汇聚结果');
    
    queryComplaint();  
  };  
  
  // 在组件挂载时设置定时器  
  // onMounted(() => {  
  //   timerId = setInterval(refreshComplaint, refreshIntervalMinute.value*60*1000 );  
  //   console.log('mounted timerId',timerId);
    
  // });  
  
  // 在组件卸载时清除定时器  
  onUnmounted(() => {  
    clearInterval(timerId);  
    
  });  

  const autoRefreshSwitch = ref(false);

  //关闭自动刷新
  function closeAutoRefresh(){

    
    clearInterval(timerId);  

    console.log('closeAutoRefresh timerId',timerId);
    
  }

  closeAutoRefresh();


  //启动自动刷新
  function startAutoRefresh(){
    console.log('refreshIntervalMinute.value',refreshIntervalMinute.value);
    
    clearInterval(timerId);  

    timerId = setInterval(refreshComplaint, refreshIntervalMinute.value*60*1000 );  


  }




  const refreshCacheInfoService = useInfo({
      rootPath: '/graph-rest-api',
    });

    const { info: refreshCacheInfo, loading: refreshCacheloading } = refreshCacheInfoService;

  async function refreshCache(){

    notification.open({
      message: `正在刷新缓存，请勿重复点击`,
      duration: 2,
      icon: () => h(SmileOutlined, { style: 'color: green' }),
    });
    refreshCacheloading.value =true;
    // import requests
    // import json

    // headers = {'content-type': 'application/json',
    //            'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:22.0) Gecko/20100101 Firefox/22.0'}


    // # localhost:1084/  替换nrm.oss.telecomjs.com:39049/graph-rest-api/ 即可本地测试

    let [startTime,endTime]=[...complaintDateRanger.value];


    if(startTime==null || startTime==undefined){

     startTime=dayjs().add(-1, 'd')
     complaintDateRanger.value[0] = dayjs().add(-1, 'd')
    }

    if(endTime==null || endTime==undefined){

      endTime=dayjs()
      complaintDateRanger.value[1] = dayjs()

    }

    //刷缓存也不能查太长时间的，百川的接口会报错
      complaintDateRanger.value = adjustDates(startTime, endTime);  
      console.log(complaintDateRanger.value); // { start: '2023-09-05', end: '2023-09-15' }
    

    //todo:接口要修改为获取申告的接口
    refreshCacheInfo.value = {
      nat: cityidtocompanyname(city.value),
      startTime: startTime.format('YYYY-MM-DD HH:mm:ss'),  
      endTime: endTime.format('YYYY-MM-DD HH:mm:ss')

    }
    await refreshCacheInfoService.doCreateNew(    '/api/faulthologram/auto-savecomplaint',);


    refreshCacheloading.value =false;
    notification.open({
      message: `刷新成功`,
      duration: 2,
      icon: () => h(SmileOutlined, { style: 'color: green' }),
    });
}


//查询用户所属地市


// const localStoragecity = localStorage.getItem("AREA_CODE");
// console.log('localStoragecity',localStoragecity);
// const cityUnchangable = ref(true);
// async function getUserAreaCode (localStoragecity){
//   if(localStoragecity == '321122930000000000000014'){
//     city.value = 'yz'  
//     cityUnchangable.value = false
//   }
//   else{
//     UserAreainfo.value = {
//       id:localStoragecity
//     };
//     UserAreainfo.value = await UserAreainfoService.doCreateNew('/api/userinfo/query_rm_area');
//     console.log('UserAreainfo.value',UserAreainfo.value);
//     city.value = UserAreainfo.value.value[0].code.split('.')[0];  
    
//   }
// }

// getUserAreaCode(localStoragecity);



//根据AREA_CODE查到地市信息：

  //根据AREA_CODE查到地市信息：

  const cityUnchangable = ref(true);

  console.log('userStore.getAreaCode',userStore.getAreaCode);
  userStore.getAreaCode
  if(userStore.getAreaCode == 'js'){
        
        cityUnchangable.value = false  //js用户可以切换地市
      }
      else{
    
        city.value = userStore.getAreaCode;  
        cityUnchangable.value =  true //地市用户不可以切换地市

      }


const complaintlinechart = ref();
const activeKeyComplaint = ref();
      

var printer ;
const mapprintloading = ref(false);
function manualPrint() {
  mapprintloading.value = true;
  const mappng = printer.printMap('CurrentSize', 'aimap');
  mapprintloading.value = false;

  console.log('mappng',mappng);
  
  }



  const complaintAggreatedAddress = ref([]);

</script>

<style lang="less" scoped>
.ant-space {
  width: 100%;
}

.table-class :deep(.table-striped) td {
  background: #F0F6FF;
}

.card-container {
  display: flex;
  flex-wrap: wrap;
}

.left-card {
  display: flex;
  flex-direction: column;
  width: 70%;
}

.flex-card {
  box-sizing: border-box;
}

.right-card {
  width: 30%;
}

.container {
  position: relative;
  /* 设置为相对定位，以便子元素可以相对于它进行定位 */
}

.float-right-button {
  position: relative;
  // right: 2vw; /* 距离容器右侧的距离 */
  width: 90px;
  height: 32px;
  background: linear-gradient(270deg, #158ffe 0%, #2f6ced 100%);
  border-radius: 4px;
  margin-left: 2vw;
  color: #fff;
  // margin-right: 2vw;
}

.other-button {
  color: #2f6ced;
  min-width: 90px;
  height: 32px;
  background: #ecf4ff;
  border-radius: 4px;
  border: 1px solid #2f6ced;
}

.table-class {
  max-height: 100%;
  /* 或者其他合适的值 */
  max-width: 100%;
  overflow-y: auto;
  /* 当内容超出时显示滚动条 */
}

.tabs-class {
  max-height: 90vh;
  /* 或者其他合适的值 */
  overflow: auto;
  /* 当子元素超出时显示滚动条 */
  border: 2px;
}

.container {
  display: flex;
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */
  height: 100%;
  /* 为了演示垂直居中，设置容器高度为视口高度 */
}

.search-box {
  width: 100%;
  height: 92px;
  position: relative;

  img {
    position: absolute;
    width: 100%;
    height: 100%;
  }
}

.search-form {
  position: absolute;
  height: 100%;
  padding: 0 20px;
}

.title-header {
  width: 100%;
  height: 50px;
  background: linear-gradient(180deg, #f0f5ff 0%, #ffffff 100%);
  box-shadow: 0px 0px 4px 2px rgba(172, 172, 172, 0.22);
  line-height: 50px;
  font-size: 16px;
  color: #142540;
  font-weight: 600;
  padding-right: 14px;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background: linear-gradient(#158ffe 0%, #2f6ced 100%);
    border-radius: 2px;
    vertical-align: sub;
    margin: 0 11px 1px 16px;
  }
}

.optical-box {
  width: 100%;
  margin-top: 20px;
  background-color: #fff;
}

.tabs {
  display: flex;
  height: 40px;
  width: fit-content;
  background: #ecf4ff;
  border-radius: 6px;
  border: 1px solid #2f6ced;
  margin: 20px;
  margin-bottom: 0;
  align-items: center;

  .tabs-item {
    padding: 0 11px;
    color: #2f6ced;
    cursor: pointer;
  }

  .tabs-item-active {
    height: 32px;
    line-height: 32px;
    color: #fff;
    background: linear-gradient(270deg, #158ffe 0%, #2f6ced 100%);
    border-radius: 6px;
    margin: 0 4px;
  }
}

.fixed-order1 {
  position: absolute;
  width: 400px;
  max-height: 560px;
  z-index: 501;
  top: 20px;
  left: 20px;
  border-radius: 10px;
  overflow: hidden;
}

.fixed-order {
  position: absolute;
  width: 500px;
  max-height: 560px;
  z-index: 100;
  top: 100px;
  left: 20px;
  background: #FFFFFF;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.21);
  border-radius: 10px;
  overflow: hidden;
}

.fixed-header {
  width: 100%;
  height: 50px;
  // background: linear-gradient(180deg, #f0f5ff 0%, #ffffff 100%);
  box-shadow: 0px 0px 4px 2px rgba(172, 172, 172, 0.22);
  line-height: 50px;
  color: #2F6CED;
  font-weight: 600;
  padding: 0 16px;
}

.fixed-table {
  padding: 10px;
}


a:active {  
    color: red; /* 点击时的颜色 */  
  }  
  
  /* 如果你也想要改变访问过后的链接颜色，可以使用 :visited */  
  a:visited {  
    color: purple; /* 已访问链接的颜色 */  
  }  



  .gradient-transparency {
    /* 使用 :::after 伪元素来创建渐变背景 */
    position: relative;
    z-index: 1;
  }

  .gradient-transparency::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    background: linear-gradient(to left, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1) 50%, rgba(255, 255, 255, 0));
    /* 可以根据需要调整渐变的方向和颜色 */
  }

  .ant-table-striped :deep(.historyOrder) td {
    background-color: #333333;
    
    opacity: 70%; 
  }

  .ant-table-striped :deep(.table-striped) td {
    background-color: #fafafa;
  }
</style>
