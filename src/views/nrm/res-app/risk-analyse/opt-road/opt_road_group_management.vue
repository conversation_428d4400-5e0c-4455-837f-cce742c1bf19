<template>
  <page-wrapper>
    <!-- 重新设计的搜索区域 -->
    <div class="search-container">
      <!-- 筛选条件区域 -->
      <div class="filter-section">
        <a-row :gutter="[16, 16]">
          <!-- 第一行：基础筛选 -->
          <a-col :span="24">
            <div class="filter-row">
              <div class="filter-group">
                <span class="filter-label">区域筛选：</span>
                <a-space size="small">
                  <a-select
                    v-model:value="filter.ds"
                    :filterOption="
                      (input: string, option: any) => {
                        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                      }
                    "
                    style="width: 120px"
                    show-search
                    placeholder="地市"
                    class="filter-select"
                    @change="
                      (v) => {
                        etlAreaCode = v;
                        filter.area_name = null;
                        queryService.queryDictionary();
                        queryService.pageQuery();
                      }
                    "
                  >
                    <a-select-option
                      v-for="item in dictionary.cityList"
                      :key="item.name"
                      :value="item.name"
                      :label="item.name"
                      >{{ item.name }}
                    </a-select-option>
                  </a-select>
                  <a-select
                    v-model:value="filter.area_name"
                    :filterOption="
                      (input: string, option: any) => {
                        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                      }
                    "
                    style="width: 120px"
                    show-search
                    placeholder="区县"
                    class="filter-select"
                    :allowClear="true"
                    @change="queryService.pageQuery()"
                  >
                    <a-select-option
                      v-for="item in dictionary.leafRegionList"
                      :key="item.name"
                      :value="item.name"
                      :label="item.name"
                      >{{ item.name }}
                    </a-select-option>
                  </a-select>
                  <a-select
                    v-model:value="filter.speciality"
                    style="width: 120px"
                    show-search
                    placeholder="专业"
                    class="filter-select"
                    :allowClear="true"
                    @change="queryService.pageQuery()"
                  >
                    <a-select-option
                      v-for="item in dictionary.speciality"
                      :key="item.speciality"
                      :value="item.speciality"
                      :label="item.speciality"
                      >{{ item.speciality }}
                    </a-select-option>
                  </a-select>
                </a-space>
              </div>

              <div class="filter-group">
                <span class="filter-label">状态筛选：</span>
                <a-space size="small">
                  <a-select
                    v-model:value="filter.result"
                    style="width: 120px"
                    show-search
                    placeholder="检测结果"
                    class="filter-select"
                    allow-clear
                    @change="query()"
                  >
                    <a-select-option
                      v-for="item in dictionary.StateList"
                      :key="item"
                      :value="item"
                      :label="item"
                      >{{ item }}
                    </a-select-option>
                  </a-select>
                  <a-select
                    v-model:value="filter.deal_result"
                    style="width: 120px"
                    show-search
                    placeholder="处理结果"
                    class="filter-select"
                    allow-clear
                    @change="query()"
                  >
                    <a-select-option :key="'待整改'" :value="'待整改'" label="待整改"
                      >待整改</a-select-option
                    >
                    <a-select-option :key="'已整改'" :value="'已整改'" label="已整改"
                      >已整改</a-select-option
                    >
                    <a-select-option :key="'忽略异常'" :value="'忽略异常'" label="忽略异常"
                    >已忽略</a-select-option
                    >
                  </a-select>
                  <a-select
                    v-model:value="filter.source_type_id"
                    style="width: 140px"
                    show-search
                    placeholder="来源"
                    class="filter-select"
                    allow-clear
                    @change="query()"
                  >
                    <a-select-option :key="0" :value="0" label="人工整理">人工整理</a-select-option>
                    <a-select-option :key="1" :value="1" label="接入设备安全保护"
                      >接入设备安全保护</a-select-option
                    >
                    <a-select-option :key="2" :value="2" label="组网安全保护"
                      >组网安全保护</a-select-option
                    >
                    <a-select-option :key="10" :value="10" label="OLT上联">OLT上联</a-select-option>
                  </a-select>
                </a-space>
              </div>
            </div>
          </a-col>

          <!-- 第二行：详细筛选 -->
          <a-col :span="24">
            <div class="filter-row">
              <div class="filter-group">
                <span class="filter-label">详细筛选：</span>
                <a-space size="small">
                  <a-input
                    v-model:value="filter.opt_code"
                    @blur="filter.opt_code = filter.opt_code?.trim() || ''"
                    placeholder="光路编码"
                    style="width: 160px"
                    class="filter-input"
                  />
                  <a-input
                    v-model:value="filter.name"
                    @blur="filter.name = filter.name?.trim() || ''"
                    placeholder="光路组名称"
                    style="width: 160px"
                    class="filter-input"
                  />
                  <a-input
                    v-model:value="filter.code"
                    @blur="filter.code = filter.code?.trim() || ''"
                    placeholder="光路组编码"
                    style="width: 160px"
                    class="filter-input"
                  />
                  <a-input
                    v-model:value="filter.create_op"
                    @blur="filter.create_op = filter.create_op?.trim() || ''"
                    placeholder="创建人"
                    style="width: 120px"
                    class="filter-input"
                  />
                  <a-range-picker
                    v-model:value="create_date_range"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    placeholder="['开始日期', '结束日期']"
                    style="width: 240px"
                    class="filter-input"
                  />
                </a-space>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-section">
        <a-space size="middle">
          <a-button type="primary" @click="query()" class="action-btn primary-btn">
            <SearchOutlined />
            查询
          </a-button>
          <a-button @click="showNewDialog()" class="action-btn secondary-btn">
            <PlusOutlined />
            新增光路
          </a-button>
          <a-button @click="showUploadDialog()" class="action-btn secondary-btn">
            <UploadOutlined />
            导入光路
          </a-button>
          <a-button @click="check_analyze" :loading="etlLoading" class="action-btn warning-btn">
            <ExperimentOutlined />
            一键检测
            <span v-if="etlLoading" class="loading-text">请耐心等待</span>
          </a-button>
          <a-button @click="download()" class="action-btn secondary-btn">
            <DownloadOutlined />
            导出
          </a-button>
        </a-space>
      </div>
    </div>

      <!--
      <a-flex gap="middle">
        <a-button html-type="submit" @click="query()" class="btnSearch">查询</a-button>

        <a-button
          html-type="submit"
          @click="
            () => {
              showNewDialog();
            }
          "
          class="btnAdd"
          >新增光路</a-button
        >
        <a-button
          @click="
            () => {
              showUploadDialog();
            }
          "
          class="btnImport"
          >导入光路</a-button
        >
        <a-button @click="check_analyze" :loading="etlLoading" class="btnTest"
          >一键检测 <span v-if="etlLoading" style="color: red">请耐心等待</span></a-button
        >
        <a-button html-type="submit" @click="download()" class="btnSearch">导出</a-button>
      </a-flex>
      -->
      <uploadDialog
        :filter="filter"
        v-model:show="uploadDialogVisible"
        @on-finish="queryService.pageQuery"
      />
    </div>
    <a-card>
      <div
        class="staticBox"
        v-if="dictionary.group_static && dictionary.group_static.length > 0"
        @click="staticDetailShow = !staticDetailShow"
      >
        <InfoCircleFilled style="margin-right: 0; margin-left: 10px" />
        <span class="stcTitle">统计：</span>
        <span>总量 {{ dictionary.group_static[0].total_count || 0 }}</span>
        <span style="color: red">同管道 {{ dictionary.group_static[0].samepiple_count || 0 }}</span>
        <span style="color: red">同光缆 {{ dictionary.group_static[0].samecable_count || 0 }}</span>
        <span style="color: red"
          >单光路 {{ dictionary.group_static[0].singlefiber_count || 0 }}</span
        >
        <span style="color: red"
          >单路由 {{ dictionary.group_static[0].singleroute_count || 0 }}</span
        >
        <span style="color: red">无光路 {{ dictionary.group_static[0].nonefiber_count || 0 }}</span>
        <span style="color: red">缺管道 {{ dictionary.group_static[0].lackpiple_count || 0 }}</span>
        <span style="color: darkorange"
          >同局前井 {{ dictionary.group_static[0].samejqj_count || 0 }}</span
        >
        <span style="color: darkgreen"
          >局内光路 {{ dictionary.group_static[0].jnfiber_count || 0 }}</span
        >
        <span style="color: darkgreen">正常 {{ dictionary.group_static[0].normal }}</span>
      </div>
      <div class="staticBox" v-else @click="staticDetailShow = !staticDetailShow">
        <InfoCircleFilled style="margin-right: 0; margin-left: 10px" />
        <span class="stcTitle">统计：</span>
        <span>总量 0</span>
        <span>同管道 0</span>
        <span>同光缆 0</span>
        <span>单光路 0</span>
        <span>单路由 0</span>
        <span>无光路 0</span>
        <span>缺管道 0</span>
        <span>同局前井 0</span>
        <span>局内光路 0</span>
        <span>正常 0</span>
      </div>
      <a-table
        v-if="staticDetailShow"
        :dataSource="dictionary.group_static"
        :columns="columns_opt_static"
        style="background-color: #ecf4ff"
        :pagination="false"
      />
      <a-button v-if="staticDetailShow" @click="exportExcel" class="btnSearch">导出总览</a-button>
    </a-card>
    <a-card>
      <a-table
        class="tableBox"
        :dataSource="dataSource"
        :columns="columns"
        :pagination="queryService.pagination"
        @change="queryService.pageChange"
        :loading="loading"
        :expand-column-width="70"
        :row-key="(record) => record.id"
        :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
        bordered
        :scroll="{ y: 500 }"
      >
        <template #bodyCell="{ column, record }">
          <template
            v-if="
              column.key !== 'result' &&
              column.key !== 'action' &&
              column.key !== 'checkTime' &&
              column.key !== 'createDate'
            "
          >
            <a-tooltip :title="record[column.key]" placement="topLeft">
              {{ record[column.key] }}
            </a-tooltip>
          </template>
          <template v-if="column.key === 'actionStart'">
            <CaretRightOutlined
              :style="{ fontSize: '16px', color: '#2F6CED' }"
              @click="handleRowClick(record)"
            />
          </template>
          <template v-if="column.key === 'checkTime' && record.checkTime != null">
            {{ formatToDateTime(record.checkTime) }}
          </template>
          <template v-if="column.key === 'createDate' && record.createDate != null">
            {{ formatToDateTime(record.createDate) }}
          </template>
          <template v-if="column.key === 'result'">
            <a-tooltip :title="record.result" placement="topLeft">
              <a-typography-text
                v-if="
                  ['同管道', '同光缆', '单光路', '单路由', '无光路', '缺管道'].some((item) =>
                    record.result.includes(item),
                  )
                "
                style="color: #ff0000"
              >
                {{ record.result }}
              </a-typography-text>
              <a-typography-text
                v-else-if="['同局前井'].some((item) => record.result.includes(item))"
                style="color: #ff8c00"
              >
                {{ record.result }}
              </a-typography-text>
              <a-typography-text
                v-else-if="['正常', '局内光路'].some((item) => record.result.includes(item))"
                style="color: #006400"
              >
                {{ record.result }}
              </a-typography-text>
            </a-tooltip>
          </template>

          <template v-if="column.key === 'action'">
            <a-button
              v-if="record.dealResult != null && record.dealResult.includes('待整改')"
              type="link"
              class="btnSty"
              @click="ignoreRisk(record)"
              >忽略隐患</a-button
            >
            <a-button
              v-if="record.dealResult != null && record.dealResult.includes('忽略')"
              type="link"
              class="btnSty"
              @click="restorRisk(record)"
              >恢复隐患</a-button
            >
            <a-button type="link" class="btnSty" @click="showEditDialog(record)">修改信息</a-button>
            <a-button type="link" class="btnSty" @click="saveOpt(record)">增加光路</a-button>
            <a-button type="link" class="btnSty" @click="riskAnalyze(record)">检测</a-button>
            <a-button
              type="link"
              class="btnSty"
              @click="
                () => {
                  let ds = filter.ds;
                  go('./risk-show-opt-road/' + record.id + '_' + ds);
                }
              "
              >详情</a-button
            >
            <a-button
              type="link"
              class="btnSty"
              @click="
                () => {
                  showNoteDialog(record);
                }
              "
              >备注</a-button
            >
            <a-button
              class="btnSty"
              v-if="record.confirmStatus === '未确认' || !record.confirmStatus"
              type="link"
              @click="confirmResult(record)"
              >人工确认</a-button
            >
            <a-button
              type="link"
              class="btnSty"
              style="color: #f31818"
              danger
              @click="deleteRecord(record)"
              >删除</a-button
            >
          </template>
        </template>
      </a-table>
      <NoteDialog
        :info="current"
        :filter="filter"
        v-model:show="NoteDialogVisible"
        @on-finish="queryService.pageQuery"
      />
      <EditDialog
        :dictionary="dictionary"
        :info="current"
        :filter="filter"
        v-model:show="showEditVisible"
        @on-finish="queryService.pageQuery"
      />
      <ElementEditDialog
        :info="current"
        :filter="filter"
        v-model:show="showElementEditVisible"
        @on-finish="onFinish(current)"
      />
    </a-card>

    <!-- 表格行row详情弹窗 -->
    <opt_road_detail_modal
      ref="optRoadDetailModal"
      @delete-opt="deleteOpt"
      @update-opt="updateOpt"
    />
  </page-wrapper>
</template>
<script lang="ts" setup>
  import { onActivated, ref, onMounted, defineOptions, getCurrentInstance } from 'vue';
  import { PageWrapper } from '@/components/Page';
  import { useGo } from '@/hooks/web/usePage';
  import { useMessage } from '@/hooks/web/useMessage';
  import { formatToDateTime } from '@/utils/dateUtil';
  import { usePageQuery, useInfo } from '@/hooks/web/useRestAPI';
  import { default as uploadDialog } from './opt_road_group_management_upload.vue';
  import { useETLTask } from '@/hooks/web/useETLTask';
  import { default as NoteDialog } from './optRoadGroupNoteDialog.vue';
  import { default as EditDialog } from './opt_road_group_edit.vue';
  import { default as ElementEditDialog } from './opt_road_group_element_edit.vue';
  import { useUserStore } from '@/store/modules/user';
  import {
    InfoCircleFilled,
    CaretRightOutlined,
    SearchOutlined,
    PlusOutlined,
    UploadOutlined,
    DownloadOutlined,
    ExperimentOutlined
  } from '@ant-design/icons-vue';
  import * as XLSX from 'xlsx';
  import { saveAs } from 'file-saver';
  import { default as opt_road_detail_modal } from './opt_road_detail_modal.vue';

  const { proxy } = getCurrentInstance(); //光路保护组管理详情弹窗

  defineOptions({ name: 'OptRoadGroupManagement' });

  const go = useGo();
  const NoteDialogVisible = ref(false);
  const current = ref<any>({});
  const showNoteDialog = (record) => {
    current.value = record;
    NoteDialogVisible.value = true;
  };

  let staticDetailShow = ref(false); //统计详情是否展示

  const uploadDialogVisible = ref(false);
  const showUploadDialog = () => {
    // const ds = filter.value.ds;
    // current.value = ds;
    uploadDialogVisible.value = true;
  };
  const columns_opt_static = ref([
    {
      title: '区县',
      dataIndex: 'leaf_region',
      key: 'leaf_region',
    },
    {
      title: '专业',
      dataIndex: 'speciality',
      key: 'speciality',
    },
    {
      title: '总量',
      dataIndex: 'total_count',
      key: 'total_count',
    },
    {
      title: '同管道',
      dataIndex: 'samepiple_count',
      key: 'samepiple_count',
    },
    {
      title: '同光缆',
      dataIndex: 'samecable_count',
      key: 'samecable_count',
    },
    {
      title: '单光路',
      dataIndex: 'singlefiber_count',
      key: 'singlefiber_count',
    },
    {
      title: '单路由',
      dataIndex: 'singleroute_count',
      key: 'singleroute_count',
    },
    {
      title: '无光路',
      dataIndex: 'nonefiber_count',
      key: 'nonefiber_count',
    },
    {
      title: '缺管道',
      dataIndex: 'lackpiple_count ',
      key: 'lackpiple_count ',
    },
    {
      title: '同局前井',
      dataIndex: 'samejqj_count',
      key: 'samejqj_count',
    },
    {
      title: '局内光路',
      dataIndex: 'jnfiber_count',
      key: 'jnfiber_count',
    },
    {
      title: '正常',
      dataIndex: 'normal',
      key: 'normal',
    },
  ]);

  const columns = ref([
    {
      title: '更多',
      dataIndex: 'actionStart',
      key: 'actionStart',
      fixed: 'left',
      width: 40,
    },
    {
      title: '地市',
      dataIndex: 'areaName',
      key: 'areaName',
      width: 50,
      ellipsis: false,
      className: 'wrap-text',
    },
    {
      title: '区县',
      dataIndex: 'leafRegion',
      key: 'leafRegion',
      width: 50,
      ellipsis: false,
      className: 'wrap-text',
    },
    {
      title: '专业',
      dataIndex: 'speciality',
      key: 'speciality',
      width: 60,
      ellipsis: false,
      className: 'wrap-text',
    },
    {
      title: '光路组ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      ellipsis: false,
      className: 'wrap-text',
    },
    {
      title: '光路组名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      ellipsis: false,
      className: 'wrap-text',
    },
    {
      title: '光路组编码',
      dataIndex: 'code',
      key: 'code',
      width: 180,
      ellipsis: false,
      className: 'wrap-text',
    },
    {
      title: '检测结果',
      dataIndex: 'result',
      key: 'result',
      width: 150,
      ellipsis: false,
      className: 'wrap-text',
    },
    {
      title: '检测时间',
      key: 'checkTime',
      width: 120,
      ellipsis: true,
    },
    {
      title: '相关客户',
      dataIndex: 'customer',
      key: 'customer',
      width: 60,
      ellipsis: false,
      className: 'wrap-text',
    },
    {
      title: '处理结果',
      dataIndex: 'dealResult',
      key: 'dealResult',
      width: 80,
      ellipsis: false,
      className: 'wrap-text',
    },
    {
      title: '集约化派单号',
      dataIndex: 'workOrderCode',
      key: 'workOrderCode',
      width: 60,
      ellipsis: false,
      className: 'wrap-text',
    },
    {
      title: '人工确认',
      dataIndex: 'confirmOp',
      key: 'confirmOp',
      width: 60,
      ellipsis: false,
      className: 'wrap-text',
    },
    {
      title: '维护人员',
      dataIndex: 'maintainers',
      key: 'maintainers',
      width: 60,
      ellipsis: false,
      className: 'wrap-text',
    },
    {
      title: '创建时间',
      dataIndex: 'createDate',
      key: 'createDate',
      width: 120,
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      width: 180,
    },
  ]);

  const create_date_range = ref([])
  const queryService = usePageQuery({
    rootPath: '/graph-rest-api',
    queryUrl: '/api/opt-group-api',
    filter: {
      opt_code: '',
      ds: '南京',
      code: '',
      name: '',
      create_op: '',
      //create_date_range:undefined,
      create_start_date:'',
      create_end_date:'',
      source_type_id: null,
      result: undefined,
    },
    pagination: {
      pageSize: 100,
      hideOnSinglePage: false,
    },
  });

  const { filter, loading, dataSource, dictionary } = queryService;

  // const query = async () => {
  //   await queryService.queryDictionary();
  //   await queryService.pageQuery();
  //   // await statusStatic();
  // };

  const query = () => {
    console.log("查询时间段:",create_date_range,create_date_range.value[0])
    queryService.filter.value.create_start_date = create_date_range.value[0];
    queryService.filter.value.create_end_date = create_date_range.value[1];
    queryService.queryDictionary();
    queryService.pageQuery();
    // await statusStatic();
  };

  onMounted(async () => {
    //await query();
  });
  onActivated(async () => {
     // await query();
  });

  const infoService = useInfo({
    rootPath: '/graph-rest-api',
    info: {
      ds: '',
      id: '',
      group_name: '',
      speciality:'',
    },
    responseType: 'blob',
  });

  const { createMessage, createConfirm } = useMessage();
  const spinning = ref<boolean>(false);

  const check_analyze = async () => {
    const ds = filter.value.ds;
    infoService.info.value.ds = ds;
    createMessage.info('一键检测开始！请稍后……');
    etlStart();
  };

  const default_area_code: string = '无锡';
  const {
    areaCode: etlAreaCode,
    loading: etlLoading,
    start: etlStart,
  } = useETLTask({
    ruleCode: 'python_opt_pair_check',
    nodeGroup: 'node-group-k8s01',
    areaCode: default_area_code,
    onComplete: () => {
      query();
    },
  });
  const deleteRecord = async (record) => {
    createConfirm({
      iconType: 'warning',
      title: '请确认',
      content: '删除后将不可恢复',
      onOk: async () => {
        const ds = filter.value.ds;
        console.log('record.group_name', record);
        infoService.info.value.ds = ds;
        infoService.info.value.id = record.id;
        let result = await infoService.doCreateNew(`/api/opt-group-api/deleteOptGroupRecord`);
        if (result > 0) {
          createMessage.info('删除成功！');
        } else {
          createMessage.warn('删除失败！');
        }
        await queryService.pageQuery();
      },
    });
  };
  const riskAnalyze = async (record) => {
    infoService.info.value.ds = filter.value.ds;
    infoService.info.value.id = record.id;
    infoService.info.value = record
    createMessage.info('开始检测，稍等！');
    await infoService.doCreateNew('/api/opt-group-api/risk-analyze');
    createMessage.info('检测完成');
    await queryService.pageQuery();
  };

  const showEditVisible = ref(false);
  const showEditDialog = async (record: any) => {
    record.area_name = record.areaName;
    current.value = record;
    showEditVisible.value = true;
  };

  const showNewDialog = async () => {
    current.value = {};
    showEditVisible.value = true;
  };

  const showElementEditVisible = ref(false);
  const saveOpt = async (record) => {
    console.log('record', record);
    current.value = { group_id: record.id, ds: filter.value.ds, code: record.code };
    showElementEditVisible.value = true;
  };

  //光路详情列表修改
  const updateOpt = async (record) => {
    current.value = record;
    current.value.areaName = filter.value.ds;
    current.value.opt_code = record.code;
    current.value.opt_name = record.name;
    showElementEditVisible.value = true;
  };
  const userStore = useUserStore();

  const confirmResult = async (record) => {
    createConfirm({
      iconType: 'warning',
      title: '请确认',
      content: '是否确认该检测结果',
      onOk: async () => {
        infoService.info.value.userName =
          userStore.getUserInfo.realName + '(' + userStore.getUserInfo.username + ')';
        infoService.info.value.ds = filter.value.ds;
        infoService.info.value.id = record.id;
        await infoService.doCreateNew(`/api/opt-group-api/confirm_result`);
        await query();
      },
    });
  };

  const cancelConfirmResult = async (id) => {
    infoService.info.value.ds = filter.value.ds;
    infoService.info.value.id = id;
    await infoService.doCreateNew(`/api/opt-group-api/cancel_confirm_result`);
    await query();
  };

  //光路列表删除
  const deleteOpt = async (record) => {
    createConfirm({
      iconType: 'warning',
      title: '请确认',
      content: '删除后将不可恢复',
      onOk: async () => {
        infoService.info.value.id = record.id;
        infoService.info.value.ds = filter.value.ds;
        await infoService.doDownload(`/api/opt-group-api/deleteOpt`);
        await cancelConfirmResult(record.group_id);
        await query();
        proxy.$refs.optRoadDetailModal.openShow = false;
      },
    });
  };

  const onFinish = async (current) => {
    let current_id_cancel_confirm = '';
    if (current.group_id) current_id_cancel_confirm = current.group_id;
    else current_id_cancel_confirm = current.id;
    await cancelConfirmResult(current_id_cancel_confirm);
    await queryService.pageQuery;
    proxy.$refs.optRoadDetailModal.openShow = false;
  };

  //表格行点击
  const handleRowClick = (record) => {
    console.log('当前row数据', record);
    proxy.$refs.optRoadDetailModal.openShow = true;
    proxy.$refs.optRoadDetailModal.record = record;
  };

  //导出功能
  const downloadinfoService = useInfo({
    rootPath: '/graph-rest-api',
    info: {
      ds: '',
      area_name:'',
      speciality:'',
      result:'',
      deal_result:'',
      opt_code:'',
      name:'',
      code:'',
    },
    responseType: 'blob',
  });

  const download = async () => {
    downloadinfoService.info.value = filter.value
    const result = await downloadinfoService.doDownload(`/api/opt-group-api/opt_group_download`);
    const blob = new Blob([result], { type: 'application/vnd.ms-excel' });
    let fileName = filter.value.ds + '光路组隐患分析' + Date.parse(new Date().toString()) + '.xlsx';
    let a = document.createElement('a');
    a.download = fileName;
    a.href = window.URL.createObjectURL(blob);
    a.click();
    a.remove();
  };

  const ignorRisk_infoService = useInfo({
    rootPath: '/graph-rest-api',
    info: {
      ds: '',
    },
    responseType: 'blob',
  });
  const ignoreRisk = async (record) => {
    record.userCode = userStore.getUserInfo.realName + userStore.getUserInfo.username;
    ignorRisk_infoService.info.value = record;
    const result = await ignorRisk_infoService.doDownload(
      `/api/opt-group-api/deal_result_update_ignore`,
    );
    query();
  };

  const restorRisk = async (record) => {
    record.userCode = userStore.getUserInfo.realName + userStore.getUserInfo.username;
    ignorRisk_infoService.info.value = record;
    const result = await ignorRisk_infoService.doDownload(`/api/opt-group-api/deal_result_update`);
    query();
  };
  const exportExcel = () => {
    const data = dictionary.value.group_static;
    const exportData = data.map((item) => {
      const newItem = {};
      columns_opt_static.value.forEach((col) => {
        newItem[col.title] = item[col.dataIndex];
      });
      return newItem;
    });
    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });
    saveAs(blob, '光路数据总览.xlsx');
  };
</script>

<style scoped lang="less">
  a-tag {
    display: block;
    /* 或者 inline-block */
    margin-bottom: 5px;
    /* 控制元素间距 */
  }

  .blue-line {
    display: inline-block;
    margin-right: 15px;
    /* 可以根据需要调整间距 */
    border-left: 5px solid #1890ff;
    /* 蓝色竖线的颜色 */
    padding-left: 5px;
    /* 左侧填充，确保文字不会紧贴竖线 */
  }

  /* 新的搜索容器样式 */
  .search-container {
    margin-bottom: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* 筛选条件区域 */
  .filter-section {
    margin-bottom: 16px;
  }

  .filter-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .filter-group {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
  }

  .filter-label {
    font-weight: 500;
    color: #2f6ced;
    margin-right: 8px;
    white-space: nowrap;
    font-size: 14px;
  }

  .filter-select {
    border-radius: 6px;

    :deep(.ant-select-selector) {
      border-radius: 6px;
      border-color: #d9d9d9;
      transition: all 0.3s ease;

      &:hover {
        border-color: #2f6ced;
      }
    }

    :deep(.ant-select-focused .ant-select-selector) {
      border-color: #2f6ced;
      box-shadow: 0 0 0 2px rgba(47, 108, 237, 0.2);
    }
  }

  .filter-input {
    border-radius: 6px;

    &:hover {
      border-color: #2f6ced;
    }

    &:focus {
      border-color: #2f6ced;
      box-shadow: 0 0 0 2px rgba(47, 108, 237, 0.2);
    }
  }

  /* 操作按钮区域 */
  .action-section {
    padding-top: 16px;
    border-top: 1px solid #e8e8e8;
    display: flex;
    justify-content: center;
  }

  .action-btn {
    height: 36px;
    border-radius: 6px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;

    &.primary-btn {
      background: linear-gradient(135deg, #2f6ced 0%, #158ffe 100%);
      border: none;
      color: white;

      &:hover {
        background: linear-gradient(135deg, #1e5bb8 0%, #0d7ce6 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 108, 237, 0.3);
      }
    }

    &.secondary-btn {
      background: #ffffff;
      border: 1px solid #2f6ced;
      color: #2f6ced;

      &:hover {
        background: #f0f5ff;
        border-color: #1e5bb8;
        color: #1e5bb8;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(47, 108, 237, 0.2);
      }
    }

    &.warning-btn {
      background: #fff7e6;
      border: 1px solid #ffa940;
      color: #fa8c16;

      &:hover {
        background: #fff1b8;
        border-color: #fa8c16;
        color: #d46b08;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(250, 140, 22, 0.2);
      }
    }
  }

  .loading-text {
    color: #ff4d4f;
    font-size: 12px;
    margin-left: 4px;
  }

  /* 保留旧按钮样式以兼容其他地方使用 */
  .btnSearch {
    width: 90px;
    height: 32px;
    background-image: linear-gradient(270deg, #158ffe 0%, #2f6ced 100%);
    border-radius: 4px;
    font-size: 14px;
    color: #ffffff;
    border: 0 none;
    letter-spacing: 1px;
    &:hover {
      color: #fff;
    }
  }

  :deep(.table-striped) td {
    background-color: #fff;
  }
  :deep(.table-striped) td {
    background-color: #f0f6ff;
  }

  .btnSty {
    color: #1e85f2;
    border-radius: 0;
    border-right: 1px solid #d9d9d9;
    padding: 0 8px;
    height: 20px;
    line-height: 20px;
    &:first-child {
      padding-left: 0;
    }
    &:last-child {
      border-right: 0 none;
      padding-right: 3px;
    }
  }

  // 统计
  .staticBox {
    width: 100%;
    height: 36px;
    line-height: 36px;
    background: #ecf4ff;
    border: 1px solid #2f6ced;
    border-radius: 4px;
    cursor: pointer;
    // margin-top: 16px;
    span {
      &.stcTitle {
        margin-right: 2px;
        margin-left: 12px;
      }
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #246ad9;
      margin-right: 30px;
    }
  }

  /* 移除旧的btnBox样式，使用新的action-section布局 */

  // a-card 卡片样式自定义
  :deep(.ant-card .ant-card-body) {
    padding: 15px;
  }

  //表格高度修改
  .tableBox {
    :deep(.ant-table-thead tr th) {
      padding: 14px 16px;
    }
    :deep(.ant-table-body .ant-table-row td) {
      // height:40px;
      padding: 12px 16px;
    }
  }

  .tableBox .ant-table-cell {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    overflow: hidden;
  }

  .btnSty {
    margin: 2px 0;
    text-align: left;
    white-space: normal;
  }

  .wrap-text {
    white-space: normal;
    overflow: hidden;
    text-overflow: clip;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .filter-row {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .filter-group {
      width: 100%;
      justify-content: flex-start;
    }

    .action-section {
      :deep(.ant-space) {
        flex-wrap: wrap;
        justify-content: center;
      }
    }
  }

  @media (max-width: 768px) {
    .search-container {
      padding: 16px;
    }

    .filter-group {
      flex-direction: column;
      align-items: flex-start;
      width: 100%;
    }

    .filter-label {
      margin-bottom: 4px;
    }

    .action-btn {
      width: 100%;
      justify-content: center;
    }
  }
</style>
