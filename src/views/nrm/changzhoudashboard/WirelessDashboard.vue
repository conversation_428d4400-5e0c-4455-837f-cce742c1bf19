<template>
  <div class="dashboard-container">
    <!-- 背景装饰元素 -->
    <div class="tech-background"></div>
    <div class="tech-overlay"></div>
    <div class="tech-particles"></div>
    <div class="tech-circuit"></div>
    <div class="tech-grid"></div>
    <!-- 主内容区 -->
    <main class="dashboard-content">
      <!-- 物理资源 -->
      <div class="dashboard-card physics-resources">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-cyan-400 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <path d="M2 20h.01"></path>
              <path d="M7 20v-4"></path>
              <path d="M12 20v-8"></path>
              <path d="M17 20V8"></path>
              <path d="M22 4v16"></path>
            </svg>
            <span>物理资源</span>
          </h2>
          <div class="header-decoration"></div>
        </div>
        <div class="dashboard-card-content">
          <div class="chart-container">
            <physics-resources-chart />
          </div>
        </div>
      </div>
      <!-- 无线覆盖 -->
      <div class="dashboard-card wireless-coverage">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-cyan-400 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
            <span>无线覆盖</span>
          </h2>
          <div class="flex items-center gap-2">
            <button class="menu-button" :class="{ active: networkTypeForWirelessCoverage === '4G' }"
              @click="networkTypeForWirelessCoverage = '4G'">4G</button>
            <button class="menu-button" :class="{ active: networkTypeForWirelessCoverage === '5G' }"
              @click="networkTypeForWirelessCoverage = '5G'">5G</button>
          </div>
        </div>
        <div class="dashboard-card-content">
          <div class="relative h-full w-full rounded-lg overflow-hidden">
            <wireless-coverage-map :network-type="networkTypeForWirelessCoverage" />
          </div>
        </div>
      </div>
      <!-- 4G电联一张网 -->
      <div class="dashboard-card telecom-unicom">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-cyan-400 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <rect x="4" y="4" width="16" height="16" rx="2" ry="2"></rect>
              <rect x="9" y="9" width="6" height="6"></rect>
              <path d="M15 2v2"></path>
              <path d="M15 20v2"></path>
              <path d="M2 15h2"></path>
              <path d="M20 15h2"></path>
            </svg>
            <span>4G电联一张网</span>
          </h2>
        </div>
        <div style="padding: 10px">
          <div class="chart-container">
            <telecom-unicom-chart />
          </div>
        </div>
      </div>
      <!-- 业务负荷 -->
      <div class="dashboard-card business-load">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-cyan-400 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <rect width="18" height="18" x="3" y="3" rx="2"></rect>
              <path d="M8 14.5v-5" />
              <path d="M12 16V8" />
              <path d="M16 13.5v-3" />
            </svg>
            <span>业务负荷</span>
          </h2>
        </div>
        <div style="padding: 15px 10px">
          <div class="chart-container">
            <business-load-chart />
          </div>
        </div>
      </div>
      <!-- 无人机 + 5G-A -->
      <div class="dashboard-card drone-and-5ga">
        <div class="card-decoration top-left"></div>
        <div class="card-decoration top-right"></div>
        <div class="card-decoration bottom-left"></div>
        <div class="card-decoration bottom-right"></div>
        <div class="card-glow"></div>
        <div class="dashboard-card-header">
          <h2 class="text-xs font-medium text-cyan-400 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
              <path
                d="m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z">
              </path>
              <path d="m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65"></path>
              <path d="m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65"></path>
            </svg>
            <span>无人机 + 5G-A</span>
          </h2>
        </div>
        <div class="dashboard-card-content">
          <div class="chart-container">
            <drone-and5-g-a-chart />
          </div>
        </div>
      </div>
    </main>
  </div>

</template>

<script setup>
import { ref } from 'vue';
import PhysicsResourcesChart from './wireless-charts/PhysicsResourcesChart.vue';
import WirelessCoverageMap from './wireless-charts/WirelessCoverageMap.vue';
import BusinessLoadChart from './wireless-charts/BusinessLoadChart.vue';
import TelecomUnicomChart from './wireless-charts/TelecomUnicomChart.vue';
import DroneAnd5GAChart from './wireless-charts/DroneAnd5GAChart.vue';

// 添加网络类型状态，默认为4G
const networkTypeForWirelessCoverage = ref('4G')
</script>
<style scoped>
/* 主内容区 */
.dashboard-content {
  display: grid;
  grid-template-columns: repeat(15, 1fr);
  grid-template-rows: repeat(8, 1fr);
  height: calc(100vh - 85px);
  width: 100%;
  padding: 4px 14px 8px 4px;
  gap: 6px;
  position: relative;
  z-index: 10;
}

/* 图表位置和大小 */
/* 物理资源 */
.physics-resources {
  grid-column: 1 / 4;
  grid-row: 1 / 5;
}

/* 无线覆盖 */
.wireless-coverage {
  grid-column: 4 / 10;
  grid-row: 1 / 9;
}

/* 4G电联一张网 */
.telecom-unicom {
  grid-column: 10 / 13;
  grid-row: 1 / 5;
}

/* 业务负荷 */
.business-load {
  grid-column: 1 / 4;
  grid-row: 5 / 9;
}

/* 无人机 + 5G-A */
.drone-and-5ga {
  grid-column: 10 / 13;
  grid-row: 5 / 9;
}
</style>
