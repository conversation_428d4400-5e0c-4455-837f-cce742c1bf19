<template>
    <div class="trunk-container">
        <div class="trunk-section">
            <div class="trunk-title">一级干线</div>
            <div class="trunk-item"><span>光缆条数</span><span>{{ primaryTrunkData.cblcnt || '-' }}</span></div>
            <div class="trunk-item"><span>光缆段</span><span>{{ primaryTrunkData.cblsectcnt || '-' }}</span></div>
            <div class="trunk-item"><span>皮长公里</span><span>{{ primaryTrunkData.len || '-' }}</span></div>
        </div>
        <div class="trunk-section secondary">
            <div class="trunk-title">二级干线</div>
            <div class="trunk-item"><span>光缆条数</span><span>{{ secondaryTrunkData.cblcnt || '-' }}</span></div>
            <div class="trunk-item"><span>光缆段</span><span>{{ secondaryTrunkData.cblsectcnt || '-' }}</span></div>
            <div class="trunk-item"><span>皮长公里</span><span>{{ secondaryTrunkData.len || '-' }}</span></div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { callChangzhouAPI } from '@/store/changzhou/util.ts';

// 初始化数据
const primaryTrunkData = ref({
    cblcnt: 0,
    cblsectcnt: 0,
    len: '0'
});

const secondaryTrunkData = ref({
    cblcnt: 0,
    cblsectcnt: 0,
    len: '0'
});

// 获取干线数据
const fetchTrunkLineData = async () => {
    try {
        const result = await callChangzhouAPI('cbldataseach', 'V20250418155940576');
        if (result && result.count && result.count.length > 0) {
            // 处理一干数据
            const primaryData = result.count.find(item => item.cbltype === '一干');
            if (primaryData) {
                primaryTrunkData.value = primaryData;
            }

            // 处理二干数据
            const secondaryData = result.count.find(item => item.cbltype === '二干');
            if (secondaryData) {
                secondaryTrunkData.value = secondaryData;
            }
        }
    } catch (error) {
        console.error('获取干线数据失败:', error);
    }
};

// 组件挂载时获取数据
onMounted(() => {
    fetchTrunkLineData();
});
</script>

<style scoped>
.trunk-container {
    display: flex;
    width: 100%;
    height: 100%;
    gap: 10px;
    /* padding: 5px; */
}

.trunk-section {
    flex: 1;
    background: rgba(0, 40, 80, 0.4);
    border: 1px solid rgba(0, 210, 255, 0.3);
    border-radius: 4px;
    /* padding: 8px; */
}

.secondary {
    border-color: rgba(0, 210, 128, 0.3);
}

.trunk-title {
    color: rgba(0, 210, 255, 0.9);
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 8px;
    border-bottom: 1px solid rgba(0, 210, 255, 0.3);
    padding-bottom: 4px;
}

.secondary .trunk-title {
    color: rgba(0, 210, 128, 0.9);
    border-color: rgba(0, 210, 128, 0.3);
}

.trunk-item {
    display: flex;
    justify-content: space-between;
    background: rgba(0, 0, 0, 0.2);
    padding: 6px 8px;
    margin-bottom: 6px;
    border-radius: 4px;
    border-left: 2px solid rgba(0, 210, 255, 0.6);
}

.secondary .trunk-item {
    border-left-color: rgba(0, 210, 128, 0.6);
}

.trunk-item span:first-child {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.trunk-item span:last-child {
    color: rgba(255, 255, 255, 0.9);
    font-weight: bold;
}
</style>