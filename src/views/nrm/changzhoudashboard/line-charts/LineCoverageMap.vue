<template>
  <div class="absolute inset-0 flex items-center justify-center">
    <div ref="chartDom" class="w-[95%] h-[95%] relative"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive } from 'vue'
import * as echarts from 'echarts'
import { callChangzhouAPI } from '@/store/changzhou/util'
import { lineCoordinateData, lineLinkSolidData, lineLinkDashedData } from '@/store/changzhou/lineStore'
import { message } from 'ant-design-vue'

const chartDom = ref(null)
let chart = null
const chartData = reactive({
  big: [],   // 大型红色发光点
  medi: [],  // 中型蓝色发光点
})

// 重设图表大小
const resizeChart = () => {
  chart && chart.resize()
}

// 获取局站坐标数据
const fetchLineData = () => {
  try {
    // 发送网络请求获取数据
    // const result = await callChangzhouAPI('hexinbianyuanjzpos', 'V20250508171751895')
    // if (result && result.count) {

    // 暂时用写死的数据
    if (lineCoordinateData && lineCoordinateData.count) {
      // 清空现有数据
      chartData.big = []
      chartData.medi = []

      // 处理返回的数据，根据showtype分类
      lineCoordinateData.count.forEach(item => {
        const point = {
          value: [parseFloat(item.jzxpos), parseFloat(item.jzypos)],
          name: item.jzname,
          region: item.region,
          jztype: item.jztype,
          message: item.message
        }

        if (item.showtype === 'big') {
          chartData.big.push(point)
        } else if (item.showtype === 'medi') {
          chartData.medi.push(point)
        }
      })

      // 更新图表
      updateChart()
    }
  } catch (error) {
    console.error('获取局站坐标数据失败:', error)
  }
}

// 更新图表配置
const updateChart = () => {
  if (!chart) return

  const option = {
    series: [
      // 大型红色发光点
      {
        type: 'effectScatter',
        coordinateSystem: 'geo',
        symbolSize: 6,
        itemStyle: { color: '#FF4500' },
        rippleEffect: {
          brushType: 'stroke',
          scale: 6
        },
        showEffectOn: 'render',
        // showEffectOn: 'emphasis',
        effectType: 'ripple',
        data: chartData.big,
        tooltip: {
          show: true,
          formatter: params => `名称: ${params.name}<br/>类型: ${params.data.jztype}<br/>区域: ${params.data.region}<br/>光缆情况: <br/>${params.data.message}`
        },
        zlevel: 3
      },
      // 中型蓝色发光点
      {
        type: 'effectScatter',
        coordinateSystem: 'geo',
        symbolSize: 4,
        itemStyle: { color: '#00BFFF' },
        rippleEffect: {
          brushType: 'stroke',
          scale: 4
        },
        showEffectOn: 'render',
        // showEffectOn: 'emphasis',
        effectType: 'ripple',
        data: chartData.medi,
        tooltip: {
          show: true,
          formatter: params => `名称: ${params.name}<br/>类型: ${params.data.jztype}<br/>区域: ${params.data.region}<br/>光缆情况: <br/>${params.data.message}`
        },
        zlevel: 3
      },
      // 添加连接线(实线)
      {
        type: 'lines',
        coordinateSystem: 'geo',
        // effect: {
        //   show: true,
        //   period: 6,
        //   trailLength: 0.1,
        //   symbol: 'arrow',
        //   symbolSize: 5
        // },
        lineStyle: {
          width: 1,
          color: '#00FFFF',
          curveness: 0.2,
          type: 'solid'
        },
        data: lineLinkSolidData,
        tooltip: {
          show: true,
          formatter: params => `${params.data.fromName} - ${params.data.toName}`
        },
        zlevel: 2
      },
      // 添加连接线(虚线)
      {
        type: 'lines',
        coordinateSystem: 'geo',
        // effect: {
        //   show: true,
        //   period: 6,
        //   trailLength: 0.1,
        //   symbol: 'arrow',
        //   symbolSize: 5
        // },
        lineStyle: {
          width: 1,
          color: '#00FFFF',
          curveness: 0.2,
          type: 'dashed'
        },
        data: lineLinkDashedData,
        tooltip: {
          show: true,
          formatter: params => `${params.data.fromName} - ${params.data.toName}`
        },
        zlevel: 2
      },
    ]
  }

  chart.setOption(option)
}

// 初始化图表
const initChart = async () => {
  try {
    // 加载常州市地图数据
    const mapData = await fetch('/changzhou/changzhou.json').then(res => res.json())

    // 注册地图数据
    echarts.registerMap('changzhou', mapData)

    // 创建图表实例
    chart = echarts.init(chartDom.value, null, { renderer: 'svg' })

    // 显示加载状态
    chart.showLoading()

    // 应用初始配置
    const option = {
      backgroundColor: '#001529',
      title: {
        text: '常州市线路覆盖',
        left: '2%',
        top: '2%',
        textStyle: {
          fontSize: 14,
          color: '#fff'
        }
      },
      // 禁用全局tooltip
      tooltip: {
        show: false
      },
      geo: {
        map: 'changzhou',
        roam: true,
        zoom: 1.2,
        // 禁用geo地图tooltip
        tooltip: {
          show: false
        },
        itemStyle: {
          areaColor: 'rgba(0, 40, 80, 0.5)',
          borderColor: 'rgba(0, 210, 255, 0.2)',
          borderWidth: 1
        },
        // 添加 label 配置，禁用标签显示
        label: {
          show: false
        },
        // 添加 emphasis 配置，禁用鼠标悬停效果
        emphasis: {
          itemStyle: {
            // 使用与普通状态相同的颜色
            areaColor: 'rgba(0, 40, 80, 0.5)',
            borderColor: 'rgba(0, 210, 255, 0.2)',
            borderWidth: 1
          },
          // 添加 label 配置，禁用鼠标悬停时的标签显示
          label: {
            show: false
          }
        }
      },
      series: []
    }

    // 设置图表配置项
    chart.setOption(option, true)

    // 获取数据
    fetchLineData()

    // 隐藏加载状态
    chart.hideLoading()

    // 响应窗口大小变化
    window.addEventListener('resize', resizeChart)
  } catch (error) {
    console.error('加载常州市地图数据失败:', error)
    if (chart) {
      chart.hideLoading()
    }
  }
}

// 在组件挂载后初始化图表
onMounted(initChart)

// 在组件卸载前清理资源
onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('resize', resizeChart)
  // 销毁图表实例
  if (chart) {
    chart.dispose()
    chart = null
  }
})
</script>

<style scoped>
/* 添加网格背景效果 */
.chart-container {
  background-image:
    linear-gradient(rgba(0, 210, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 210, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}
</style>
