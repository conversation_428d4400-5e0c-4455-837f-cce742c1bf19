<template>
  <div class="w-full h-full flex flex-col">
    <!-- 标题和区域名称 -->
    <div class="text-center mb-1">
      <div class="text-xs text-blue-300">
        {{ selectedDistrict ? `${selectedDistrict} - 4G电联一张网` : '常州市 - 4G电联一张网' }}
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="flex-1 bg-gray-800/60 rounded-md">
      <table class="w-full text-xs text-left text-gray-100">
        <tbody>
          <!-- 电信共享给联通部分 -->
          <tr class="border-b border-gray-700 bg-gray-800/40">
            <td class="py-1.5 px-3 font-medium">电信共享给联通4G小区数量</td>
            <td class="py-1.5 px-3 text-right">{{ telecomUnicomChartData.dxtoltCells }}</td>
          </tr>
          <tr class="border-b border-gray-700">
            <td class="py-1.5 px-3 font-medium">电信4G共享比例</td>
            <td class="py-1.5 px-3 text-right">{{ telecomUnicomChartData.dxsharingRatio }}%</td>
          </tr>
          <tr class="border-b border-gray-700 bg-gray-800/40">
            <td class="py-1.5 px-3 font-medium">电信共享小区电信流量占比</td>
            <td class="py-1.5 px-3 text-right">{{ telecomUnicomChartData.dxliuliangratiodx }}%</td>
          </tr>
          <tr class="border-b border-gray-700">
            <td class="py-1.5 px-3 font-medium">电信共享小区联通流量占比</td>
            <td class="py-1.5 px-3 text-right">{{ telecomUnicomChartData.ltliuliangratiodx }}%</td>
          </tr>

          <!-- 联通共享给电信部分 -->
          <tr class="border-b border-gray-700 bg-gray-800/40">
            <td class="py-1.5 px-3 font-medium">联通共享给电信4G小区数量</td>
            <td class="py-1.5 px-3 text-right">{{ telecomUnicomChartData.lttodxCells }}</td>
          </tr>
          <tr class="border-b border-gray-700">
            <td class="py-1.5 px-3 font-medium">联通4G共享比例</td>
            <td class="py-1.5 px-3 text-right">{{ telecomUnicomChartData.ltsharingRatio }}%</td>
          </tr>
          <tr class="border-b border-gray-700 bg-gray-800/40">
            <td class="py-1.5 px-3 font-medium">联通共享小区电信流量占比</td>
            <td class="py-1.5 px-3 text-right">{{ telecomUnicomChartData.dxliuliangratiolt }}%</td>
          </tr>
          <tr>
            <td class="py-1.5 px-3 font-medium">联通共享小区联通流量占比</td>
            <td class="py-1.5 px-3 text-right">{{ telecomUnicomChartData.ltliuliangratiolt }}%</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { selectedDistrict, getTelecomUnicomData, telecomUnicomData } from '@/store/changzhou/wirelessStore'

// 计算当前需要显示的数据
const telecomUnicomChartData = computed(() => getTelecomUnicomData())
</script>

<style scoped>
/* 添加渐变背景 */
.bg-gray-800\/60 {
  background-color: rgba(31, 41, 55, 0.6);
}

/* 隔行变色效果 */
.bg-gray-800\/40 {
  background-color: rgba(31, 41, 55, 0.4);
}

/* 表格边框和间距 */
table {
  border-collapse: collapse;
  width: 100%;
}

/* 表格行悬停效果 */
tr:hover {
  background-color: rgba(55, 65, 81, 0.5);
}
</style>
