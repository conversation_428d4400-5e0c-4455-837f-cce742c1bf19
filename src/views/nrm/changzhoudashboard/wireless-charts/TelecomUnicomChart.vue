<template>
  <div ref="chartDom" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';
import { selectedDistrict, getScenarioData, scenarioNetworkData, loadScenarioData } from '@/store/changzhou/wirelessStore';

const chartDom = ref(null);
let chart = null;

// 更新图表数据
const updateChartData = () => {
  if (!chart) return;

  const data = getScenarioData();

  // 获取当前区域名称
  const title = selectedDistrict.value ?
    `${selectedDistrict.value}概况` :
    '全市概况';

  chart.setOption({
    title: {
      text: title,
      top: 5,
      left: 'center',
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12,
        fontWeight: 'normal'
      }
    },
    series: [
      {
        name: '数量',
        data: data
      }
    ]
  });
};

onMounted(() => {
  // 确保数据已加载
  if (!scenarioNetworkData.isLoaded) {
    loadScenarioData();
  }

  chart = echarts.init(chartDom.value, null, { renderer: 'svg' });
  const option = {
    title: {
      text: '全市概况',
      // top: 5,
      left: 'center',
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12,
        fontWeight: 'normal'
      }
    },
    grid: {
      top: 60,
      bottom: 30,
      left: 45,
      right: 10,
      // containLabel: true
    },
    legend: {
      top: 25,
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 10
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(0, 24, 48, 0.8)',
      borderColor: 'rgba(0, 210, 255, 0.5)',
      borderWidth: 1,
      textStyle: {
        fontSize: 12,
        color: 'rgba(255, 255, 255, 0.9)'
      }
    },
    xAxis: {
      type: 'category',
      data: ['载波聚合2CC', '载波聚合3CC'],
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.7)'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 210, 255, 0.1)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.7)'
      }
    },
    series: [
      {
        name: '数量',
        type: 'bar',
        data: getScenarioData(), // 初始化时使用全市数据
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(0, 255, 255, 0.8)' },
            { offset: 1, color: 'rgba(0, 255, 255, 0.3)' }
          ])
        },
        barWidth: '30%',
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(0, 255, 255, 1)' },
              { offset: 1, color: 'rgba(0, 255, 255, 0.5)' }
            ])
          }
        }
      }
    ]
  };

  chart.setOption(option);

  // 添加窗口大小变化监听
  window.addEventListener('resize', () => {
    chart.resize();
  });
});

// 监听选中区域变化
watch(() => selectedDistrict.value, () => {
  updateChartData();
});

// 监听载波聚合数据加载状态
watch(() => scenarioNetworkData.isLoaded, (isLoaded) => {
  if (isLoaded) {
    updateChartData();
  }
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
  }
  window.removeEventListener('resize', () => {
    chart.resize();
  });
});
</script>
