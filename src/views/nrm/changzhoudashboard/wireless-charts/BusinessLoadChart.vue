<template>
  <div class="w-full h-full flex flex-col">
    <!-- 标题和区域名称 -->
    <div class="text-center mb-5.5">
      <div class="text-xs text-blue-300">
        {{ selectedDistrict ? `${selectedDistrict} - 业务负荷` : '常州市 - 业务负荷' }}
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="flex-1 bg-gray-800/60 rounded-md">
      <table class="w-full text-xs text-left text-gray-100">
        <tbody>
          <tr class="border-b border-gray-700 bg-gray-800/40">
            <td class="py-1.5 px-3 font-medium">5G超忙小区数</td>
            <td class="py-1.5 px-3 text-right">{{ businessLoadChartData.busyCells5g }}</td>
          </tr>
          <tr class="border-b border-gray-700">
            <td class="py-1.5 px-3 font-medium">5G超忙小区占比</td>
            <td class="py-1.5 px-3 text-right">{{ businessLoadChartData.busyCellsRatio5g }}%</td>
          </tr>
          <tr class="border-b border-gray-700 bg-gray-800/40">
            <td class="py-1.5 px-3 font-medium">5G忙时PRB利用率</td>
            <td class="py-1.5 px-3 text-right">{{ businessLoadChartData.prbUtil5g }}%</td>
          </tr>
          <tr class="border-b border-gray-700">
            <td class="py-1.5 px-3 font-medium">4G超忙小区数</td>
            <td class="py-1.5 px-3 text-right">{{ businessLoadChartData.busyCells4g }}</td>
          </tr>
          <tr class="border-b border-gray-700 bg-gray-800/40">
            <td class="py-1.5 px-3 font-medium">4G超忙小区占比</td>
            <td class="py-1.5 px-3 text-right">{{ businessLoadChartData.busyCellsRatio4g }}%</td>
          </tr>
          <tr class="border-b border-gray-700">
            <td class="py-1.5 px-3 font-medium">4G忙时PRB利用率</td>
            <td class="py-1.5 px-3 text-right">{{ businessLoadChartData.prbUtil4g }}%</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { selectedDistrict, getBusinessLoadData, businessLoadData } from '@/store/changzhou/wirelessStore'

// 计算当前需要显示的数据
const businessLoadChartData = computed(() => getBusinessLoadData())
</script>

<style scoped>
/* 添加渐变背景 */
.bg-gray-800\/60 {
  background-color: rgba(31, 41, 55, 0.6);
}

/* 隔行变色效果 */
.bg-gray-800\/40 {
  background-color: rgba(31, 41, 55, 0.4);
}

/* 表格边框和间距 */
table {
  border-collapse: collapse;
  width: 100%;
}

/* 表格行悬停效果 */
tr:hover {
  background-color: rgba(55, 65, 81, 0.5);
}
</style>
