<template>
  <div ref="chartDom" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, reactive } from 'vue';
import * as echarts from 'echarts';
import { callChangzhouAPI } from '@/store/changzhou/util';

// 接收网络类型props
const props = defineProps({
  networkType: {
    type: String,
    default: '4G'
  }
});

const chartDom = ref(null);
let chart = null;

// 定义区域顺序
const districts = ['武进区', '新北区', '天宁区', '钟楼区', '经开区', '金坛区', '溧阳市'];
const districtLabels = ['武进', '新北', '天宁', '钟楼', '经开', '金坛', '溧阳'];

// 定义响应式数据对象
const chartData = reactive({
  isLoaded: false,
  '4G': {
    busyRruCount: [],
    lowTrafficRruCount: []
  },
  '5G': {
    busyRruCount: [],
    lowTrafficRruCount: []
  }
});

// 从API获取4G RRU数据
const fetch4GRruData = async () => {
  try {
    const response = await callChangzhouAPI('cz_rru4G_statistics', 'V20250409093121004');

    if (response && response.count && response.count.length > 0) {
      // 过滤出七个行政区的数据（排除"常州总体"）
      const districtData = response.count.filter(item => item.district !== '常州总体');

      // 按照指定的区域顺序排序数据
      const busyRruCount = [];
      const lowTrafficRruCount = [];

      // 遍历区域顺序，确保数据按照正确的顺序排列
      districts.forEach(district => {
        const item = districtData.find(data => data.district === district);
        if (item) {
          busyRruCount.push(item.busyRruCount);
          lowTrafficRruCount.push(item.lowTrafficRruCount);
        } else {
          // 如果没有找到对应区域的数据，填充0
          busyRruCount.push(0);
          lowTrafficRruCount.push(0);
        }
      });

      // 更新响应式数据
      chartData['4G'].busyRruCount = busyRruCount;
      chartData['4G'].lowTrafficRruCount = lowTrafficRruCount;
    }
  } catch (error) {
    console.error('获取4G RRU数据失败:', error);
  }
};

// 从API获取5G RRU数据
const fetch5GRruData = async () => {
  try {
    const response = await callChangzhouAPI('cz_rru5G_statistics', 'V20250409094920736');

    if (response && response.count && response.count.length > 0) {
      // 过滤出七个行政区的数据（排除"常州总体"）
      const districtData = response.count.filter(item => item.district !== '常州总体');

      // 按照指定的区域顺序排序数据
      const busyRruCount = [];
      const lowTrafficRruCount = [];

      // 遍历区域顺序，确保数据按照正确的顺序排列
      districts.forEach(district => {
        const item = districtData.find(data => data.district === district);
        if (item) {
          busyRruCount.push(item.busyRruCount);
          lowTrafficRruCount.push(item.lowTrafficRruCount);
        } else {
          // 如果没有找到对应区域的数据，填充0
          busyRruCount.push(0);
          lowTrafficRruCount.push(0);
        }
      });

      // 更新响应式数据
      chartData['5G'].busyRruCount = busyRruCount;
      chartData['5G'].lowTrafficRruCount = lowTrafficRruCount;

      // 标记数据已加载
      chartData.isLoaded = true;
    }
  } catch (error) {
    console.error('获取5G RRU数据失败:', error);
  }
};

// 加载所有数据
const loadAllData = async () => {
  await Promise.all([fetch4GRruData(), fetch5GRruData()]);
};

// 更新图表数据的函数
const updateChart = () => {
  if (!chart || !chartData.isLoaded) return;

  const data = chartData[props.networkType];

  chart.setOption({
    series: [
      {
        name: '过忙RRU数',
        data: data.busyRruCount
      },
      {
        name: '低流量RRU数',
        data: data.lowTrafficRruCount
      }
    ]
  });
};

onMounted(async () => {
  // 加载数据
  await loadAllData();

  chart = echarts.init(chartDom.value, null, { renderer: 'svg' });
  const option = {
    grid: {
      top: 60,
      bottom: 30,
      left: 45,
      right: 10,
      // containLabel: true
    },
    legend: {
      top: 10,
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 10
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(0, 24, 48, 0.8)',
      borderColor: 'rgba(0, 210, 255, 0.5)',
      borderWidth: 1,
      textStyle: {
        fontSize: 12,
        color: 'rgba(255, 255, 255, 0.9)'
      }
    },
    xAxis: {
      type: 'category',
      data: districtLabels,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.7)'
      }
    },
    yAxis: [
      {
        type: 'value',
        position: 'left',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(0, 210, 255, 0.1)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.7)'
        }
      },
    ],
    series: [
      {
        name: '过忙RRU数',
        type: 'bar',
        data: chartData[props.networkType].busyRruCount,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(0, 255, 255, 0.8)' },
            { offset: 1, color: 'rgba(0, 255, 255, 0.3)' }
          ])
        },
        barWidth: '30%',
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(0, 255, 255, 1)' },
              { offset: 1, color: 'rgba(0, 255, 255, 0.5)' }
            ])
          }
        }
      },
      // {
      //   name: '低流量RRU数',
      //   type: 'bar',
      //   data: chartData[props.networkType].lowTrafficRruCount,
      //   itemStyle: {
      //     color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      //       { offset: 0, color: 'rgba(255, 255, 0, 0.8)' },
      //       { offset: 1, color: 'rgba(255, 255, 0, 0.3)' }
      //     ])
      //   },
      //   barWidth: '30%',
      //   emphasis: {
      //     itemStyle: {
      //       color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      //         { offset: 0, color: 'rgba(255, 255, 0, 1)' },
      //         { offset: 1, color: 'rgba(255, 255, 0, 0.5)' }
      //       ])
      //     }
      //   }
      // }
    ]
  };

  chart.setOption(option);

  window.addEventListener('resize', () => {
    chart.resize();
  });
});

// 监听网络类型变化，更新图表
watch(() => props.networkType, () => {
  updateChart();
});

// 监听数据加载状态
watch(() => chartData.isLoaded, (isLoaded) => {
  if (isLoaded && chart) {
    updateChart();
  }
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
  }
  window.removeEventListener('resize', () => {
    chart.resize();
  });
});
</script>
