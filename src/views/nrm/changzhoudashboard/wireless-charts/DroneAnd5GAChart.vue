<template>
  <div ref="chartDom" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import * as echarts from 'echarts';
import {
  selectedDistrict,
  getDroneData,
  getScenarioData,
  droneData,
  scenarioNetworkData,
  loadDroneData,
  loadScenarioData
} from '@/store/changzhou/wirelessStore';

const chartDom = ref(null);
let chart = null;

// 获取当前区域的无人机资源数据
const droneResourceData = computed(() => {
  return getDroneData();
});

// 获取当前区域的5G-A资源数据
const fiveGAData = computed(() => {
  return getScenarioData();
});

// 获取当前区域名称
const currentTitle = computed(() => {
  return selectedDistrict.value ? `${selectedDistrict.value} - 无人机与5G-A资源` : '全市 - 无人机与5G-A资源';
});

// 更新图表数据
const updateChartData = () => {
  if (!chart) return;

  // 设置图表标题和更新数据
  chart.setOption({
    title: {
      text: currentTitle.value,
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12,
        fontWeight: 'normal'
      }
    },
    series: [
      {
        name: '机场',
        data: [droneResourceData.value[0], null, null, null]
      },
      {
        name: '基站',
        data: [null, droneResourceData.value[1], null, null]
      },
      {
        name: '载波聚合2CC',
        data: [null, null, fiveGAData.value[0], null],
      },
      {
        name: '载波聚合3CC',
        data: [null, null, null, fiveGAData.value[1]],
      }
    ]
  });
};

onMounted(async () => {
  // 确保数据已加载
  if (!droneData.isLoaded) {
    await loadDroneData();
  }
  if (!scenarioNetworkData.isLoaded) {
    await loadScenarioData();
  }

  // 初始化图表
  chart = echarts.init(chartDom.value, null, { renderer: 'svg' });

  const option = {
    title: {
      text: '全市 - 无人机与5G-A资源',
      left: 'center',
      top: -5,
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12,
        fontWeight: 'normal'
      }
    },
    grid: {
      top: 60,
      bottom: 20,
      left: 45,
      right: 45,
    },
    legend: {
      top: 20,
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 10
      }
    },
    tooltip: {
      trigger: 'item',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(0, 24, 48, 0.8)',
      borderColor: 'rgba(0, 210, 255, 0.5)',
      borderWidth: 1,
      textStyle: {
        fontSize: 12,
        color: 'rgba(255, 255, 255, 0.9)'
      },
      formatter: function (params) {
        // 根据系列索引确定显示的内容
        if (params.seriesIndex === 0 || params.seriesIndex === 1) {
          // 无人机资源（机场、基站）
          return `<div>
            <div style="font-weight: bold; margin-bottom: 5px;">无人机资源</div>
            <div>${params.seriesName}: ${params.value}</div>
          </div>`;
        } else {
          // 5G-A资源（载波聚合2CC、载波聚合3CC）
          return `<div>
            <div style="font-weight: bold; margin-bottom: 5px;">5G-A资源</div>
            <div>${params.seriesName}: ${params.value}</div>
          </div>`;
        }
      }
    },
    xAxis: {
      type: 'category',
      data: ['机场', '基站', '2CC', '3CC'],
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.7)',
      },
    },
    yAxis: [
      {
        type: 'value',
        nameTextStyle: {
          color: 'rgba(0, 210, 255, 0.8)'
        },
        position: 'left',
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0, 210, 255, 0.8)'
          }
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: 'rgba(0, 210, 255, 0.8)'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(0, 210, 255, 0.1)'
          }
        },
        axisLabel: {
          color: 'rgba(0, 210, 255, 0.8)'
        }
      },
      {
        type: 'value',
        nameTextStyle: {
          color: 'rgba(255, 210, 0, 0.8)'
        },
        position: 'right',
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 210, 0, 0.8)'
          }
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 210, 0, 0.8)'
          }
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          color: 'rgba(255, 210, 0, 0.8)'
        }
      }
    ],
    series: [
      {
        name: '机场',
        type: 'bar',
        yAxisIndex: 0,
        data: [droneResourceData.value[0], null, null, null],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(0, 210, 255, 0.8)' },
            { offset: 1, color: 'rgba(0, 210, 255, 0.3)' }
          ])
        },
        barWidth: '50%',  // 设置柱子宽度
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(0, 210, 255, 1)' },
              { offset: 1, color: 'rgba(0, 210, 255, 0.5)' }
            ])
          }
        }
      },
      {
        name: '基站',
        type: 'bar',
        yAxisIndex: 0,
        data: [null, droneResourceData.value[1], null, null],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(0, 150, 255, 0.8)' },
            { offset: 1, color: 'rgba(0, 150, 255, 0.3)' }
          ])
        },
        barWidth: '50%',  // 设置柱子宽度
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(0, 150, 255, 1)' },
              { offset: 1, color: 'rgba(0, 150, 255, 0.5)' }
            ])
          }
        }
      },
      {
        name: '载波聚合2CC',
        type: 'bar',
        yAxisIndex: 1,
        data: [null, null, fiveGAData.value[0], null],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 210, 0, 0.8)' },
            { offset: 1, color: 'rgba(255, 210, 0, 0.3)' }
          ])
        },
        barWidth: '50%',  // 设置柱子宽度
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(255, 210, 0, 1)' },
              { offset: 1, color: 'rgba(255, 210, 0, 0.5)' }
            ])
          }
        }
      },
      {
        name: '载波聚合3CC',
        type: 'bar',
        yAxisIndex: 1,
        data: [null, null, null, fiveGAData.value[1]],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 150, 0, 0.8)' },
            { offset: 1, color: 'rgba(255, 150, 0, 0.3)' }
          ])
        },
        barWidth: '50%',  // 设置柱子宽度
        barGap: '-100%',  // 此属性应设置于此坐标系中最后一个 'bar' 系列上才会生效，并且是对此坐标系中所有 'bar' 系列生效。
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(255, 150, 0, 1)' },
              { offset: 1, color: 'rgba(255, 150, 0, 0.5)' }
            ])
          }
        }
      }
    ]
  };

  chart.setOption(option);

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    chart.resize();
  });
});

// 监听选中区域变化
watch(() => selectedDistrict.value, () => {
  updateChartData();
});

// 监听数据加载状态
watch(() => droneData.isLoaded, (isLoaded) => {
  if (isLoaded && chart) {
    updateChartData();
  }
});

watch(() => scenarioNetworkData.isLoaded, (isLoaded) => {
  if (isLoaded && chart) {
    updateChartData();
  }
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
  }
  window.removeEventListener('resize', () => {
    chart.resize();
  });
});
</script>
