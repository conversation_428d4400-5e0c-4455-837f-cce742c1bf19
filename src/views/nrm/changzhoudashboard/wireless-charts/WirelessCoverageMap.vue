<template>
  <div class="absolute inset-0 flex items-center justify-center">
    <div ref="chartDom" class="w-full h-full relative"></div>

    <!-- 添加全选按钮，增加mouseenter和mouseleave事件 -->
    <button @click="resetHighlight" @mouseenter="showCityTooltip" @mouseleave="hideCityTooltip"
      class="bg-blue-500/50 hover:bg-blue-600/50 text-white px-2 py-1 rounded backdrop-blur-sm text-xs absolute top-10 right-15">
      全选
    </button>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, reactive } from 'vue'
import * as echarts from 'echarts'
import { selectedDistrict, setSelectedDistrict } from '@/store/changzhou/wirelessStore'
import { callChangzhouAPI } from '@/store/changzhou/util'

// 接收网络类型props
const props = defineProps({
  networkType: {
    type: String,
    default: '4G'
  }
})

const chartDom = ref(null)
let chart = null

// 定义响应式数据对象
const coverageData = reactive({
  isLoaded: false,
  '4G': [],
  '5G': [],
  // 增加分流比数据结构
  diversion: {
    '4G': [],
    '5G': []
  }
})

// 从API获取覆盖率数据和分流比数据
const fetchCoverageData = async () => {
  try {
    const response = await callChangzhouAPI('wxfugaifenliucount', 'V20250506192020402')

    if (response && response.count && response.count.length > 0) {
      // 处理4G和5G数据
      const data4G = []
      const data5G = []
      // 分流比数据
      const diversion4G = []
      const diversion5G = []

      response.count.forEach(item => {
        // 添加4G数据
        data4G.push({
          name: item.district,
          value: parseFloat(item.mrCoverage4g) // 使用API返回的字段名
        })

        // 添加5G数据
        data5G.push({
          name: item.district,
          value: parseFloat(item.mrCoverage5g) // 使用API返回的字段名
        })

        // 添加4G分流比数据
        diversion4G.push({
          name: item.district,
          value: parseFloat(item.分流比4g)
        })

        // 添加5G分流比数据
        diversion5G.push({
          name: item.district,
          value: parseFloat(item.分流比5g)
        })
      })

      // 更新响应式数据
      coverageData['4G'] = data4G
      coverageData['5G'] = data5G
      coverageData.diversion['4G'] = diversion4G
      coverageData.diversion['5G'] = diversion5G
      coverageData.isLoaded = true
    }
  } catch (error) {
    console.error('获取覆盖率和分流比数据失败:', error)
  }
}

// 获取当前网络类型的数据
const getChartData = () => {
  return coverageData.isLoaded ? coverageData[props.networkType] : []
}

// 获取当前网络类型的分流比数据
const getDiversionData = (districtName) => {
  if (!coverageData.isLoaded) return null;

  const diversionData = coverageData.diversion[props.networkType].find(
    item => item.name === districtName
  );

  return diversionData ? diversionData.value : null;
}

// 初始化图表
const initChart = async () => {
  try {
    // 获取覆盖率数据
    await fetchCoverageData()

    // 加载常州市地图数据
    const mapData = await fetch('/changzhou/changzhou.json').then(res => res.json())

    // 注册地图数据
    echarts.registerMap('changzhou', mapData)

    // 创建图表实例
    chart = echarts.init(chartDom.value, null, { renderer: 'svg' })

    // 应用初始配置
    updateChartOption()

    // 添加点击事件监听
    chart.on('click', handleMapClick)

    // 响应窗口大小变化
    window.addEventListener('resize', resizeChart)
  } catch (error) {
    console.error('初始化图表失败:', error)
  }
}

// 更新图表配置
const updateChartOption = () => {
  // 根据选中区域创建高亮配置
  const regions = getRegionsConfig()

  // 创建包含标签大小差异的数据
  const seriesData = getChartData().map(area => {
    // 如果选中了某个区域，并且当前区域就是选中的区域
    if (selectedDistrict.value !== null && area.name === selectedDistrict.value) {
      return {
        ...area,
        // 为选中区域应用特殊的标签样式
        label: {
          show: true,
          color: '#fff',
          fontSize: 14,
          fontWeight: 'bold'
        }
      };
    }
    // 非选中区域保持原样
    return area;
  });

  const option = {
    backgroundColor: '#001529',
    title: {
      text: '无线网络覆盖',
      left: '2%',
      top: '2%',
      textStyle: {
        fontSize: 14,
        color: '#fff'
      }
    },
    tooltip: {
      show: false
    },
    visualMap: {
      type: 'piecewise',
      left: 'left',
      top: 'bottom',
      splitNumber: 3,
      pieces: [
        { min: 95, max: 100, label: '覆盖优良', color: 'rgba(0, 255, 85, 0.5)' },    // 鲜绿色
        { min: 85, max: 94, label: '覆盖良好', color: 'rgba(255, 210, 0, 0.5)' },    // 明黄色
        { min: 0, max: 84, label: '覆盖一般', color: 'rgba(255, 60, 60, 0.5)' }      // 鲜红色
      ],
      textStyle: {
        fontSize: 10,
        color: '#fff'
      }
    },
    geo: {
      map: 'changzhou',
      roam: true,
      zoom: 1.2,
      itemStyle: {
        areaColor: 'rgba(0, 40, 80, 0.5)',
        borderColor: 'rgba(0, 210, 255, 0.2)',
        borderWidth: 1
      },
      // 添加 label 配置，禁用标签显示
      label: {
        show: false
      },
      // 添加 emphasis 配置，禁用鼠标悬停效果
      emphasis: {
        itemStyle: {
          // 使用与普通状态相同的颜色
          areaColor: 'rgba(0, 40, 80, 0.5)',
          borderColor: 'rgba(0, 210, 255, 0.2)',
          borderWidth: 1
        },
        // 添加 label 配置，禁用鼠标悬停时的标签显示
        label: {
          show: false
        }
      },
      // 应用动态区域配置
      regions: regions,
      // 禁用geo地图tooltip
      tooltip: {
        show: false
      }
    },
    series: [
      {
        name: '网络覆盖率',
        type: 'map',
        map: 'changzhou',
        roam: true,
        zoom: 1.2,
        label: {
          show: true,
          color: '#fff',
          fontSize: 12 // 默认字体大小
        },
        emphasis: {
          label: {
            color: '#fff',
            fontSize: 14,
            fontWeight: 'bold'
          },
          itemStyle: {
            areaColor: 'inherit'
          }
        },
        // 使用修改后的数据
        data: seriesData,
        tooltip: {
          show: true,
          formatter: params => {
            // 根据区域名称获取对应的图片URL和分流比数据
            const imageUrl = getDistrictImageUrl(params.name);
            const diversionValue = getDiversionData(params.name);

            // 返回包含图片、覆盖率和分流比的HTML内容
            return `
              <div style="width: 200px;">
                <div style="font-weight: bold; margin-bottom: 5px;">${params.name}</div>
                <div>覆盖率: ${params.value}%</div>
                <div>分流比: ${diversionValue !== null ? diversionValue + '%' : '暂无数据'}</div>
                <div style="margin-top: 8px;">
                  <img src="${imageUrl}" style="width: 100%; border-radius: 4px;" />
                </div>
              </div>
            `;
          },
          extraCssText: 'max-width: 220px; padding: 10px;'
        }
      },
      // 隐藏的scatter series，仅用于"常州市"tooltip触发
      {
        name: '常州市隐藏点',
        type: 'scatter',
        coordinateSystem: 'geo',
        data: [
          {
            name: '常州市',
            value: [119.7937, 31.8612]
          }
        ],
        symbol: 'circle',
        symbolSize: 0, // 不可见
        itemStyle: {
          color: 'rgba(0,0,0,0)',
          borderColor: 'rgba(0,0,0,0)'
        },
        tooltip: {
          show: true,
          formatter: params => {
            const imageUrl = getDistrictImageUrl('常州市');
            return `
              <div style="width: 340px;">
                <div style="font-weight: bold; margin-bottom: 5px">常州市热力图</div>
                <div style="margin-top: 8px;">
                  <img src="${imageUrl}" style="width: 100%; border-radius: 4px;" />
                </div>
              </div>
            `;
          },
          extraCssText: 'max-width: 360px; padding: 10px;'
        },
      }
    ]
  }

  // 设置图表配置项
  chart.setOption(option, true)
}

// 根据区域名称获取对应的图片URL
const getDistrictImageUrl = (districtName) => {
  // 根据当前网络类型和区域名称返回不同的图片URL
  const imageMap4G = {
    '武进区': '/changzhou/heatmap4G/wujin4G.jpg',
    '新北区': '/changzhou/heatmap4G/xinbei4G.jpg',
    '天宁区': '/changzhou/heatmap4G/tianning4G.jpg',
    '钟楼区': '/changzhou/heatmap4G/zhonglou4G.jpg',
    '经开区': '/changzhou/heatmap4G/jingkai4G.jpg',
    '溧阳市': '/changzhou/heatmap4G/liyang4G.jpg',
    '金坛区': '/changzhou/heatmap4G/jintan4G.jpg'
  };

  const imageMap5G = {
    '武进区': '/changzhou/heatmap5G/wujin5G.jpg',
    '新北区': '/changzhou/heatmap5G/xinbei5G.jpg',
    '天宁区': '/changzhou/heatmap5G/tianning5G.jpg',
    '钟楼区': '/changzhou/heatmap5G/zhonglou5G.jpg',
    '经开区': '/changzhou/heatmap5G/jingkai5G.jpg',
    '溧阳市': '/changzhou/heatmap5G/liyang5G.jpg',
    '金坛区': '/changzhou/heatmap5G/jintan5G.jpg'
  };

  // 根据当前网络类型选择对应的图片映射
  const imageMap = props.networkType === '4G' ? imageMap4G : imageMap5G;

  // 返回对应区域的图片，如果没有找到则返回常州市图片
  const defaultImage = props.networkType === '4G'
    ? '/changzhou/heatmap4G/changzhou4G.jpg'
    : '/changzhou/heatmap5G/changzhou5G.jpg';

  return imageMap[districtName] || defaultImage;
}

// 获取区域高亮配置
const getRegionsConfig = () => {
  // 获取区域对应的数据
  const getAreaData = (areaName) => {
    return getChartData().find(item => item.name === areaName);
  }

  // 根据覆盖率获取合适的高亮颜色
  const getHighlightColor = (value) => {
    if (value >= 95) {
      return 'rgba(0, 255, 85, 0.7)';    // 高覆盖率：更亮的绿色
    } else if (value >= 85) {
      return 'rgba(255, 210, 0, 0.7)';   // 中覆盖率：更亮的黄色
    } else {
      return 'rgba(255, 60, 60, 0.7)';   // 低覆盖率：更亮的红色
    }
  }

  if (selectedDistrict.value === null) {
    // 初始状态：所有区域边缘都高亮
    return getChartData().map(area => ({
      name: area.name,
      itemStyle: {
        borderColor: 'rgba(0, 255, 255, 1)',
        borderWidth: 4,
        shadowColor: 'rgba(50, 255, 255, 1)',
        shadowBlur: 20,
        shadowOffsetX: 0,
        shadowOffsetY: 0
      }
    }))
  } else {
    // 选中某个区域：只有选中的区域边缘高亮并增强填充颜色
    return getChartData().map(area => {
      if (area.name === selectedDistrict.value) {
        const data = getAreaData(area.name);
        return {
          name: area.name,
          itemStyle: {
            borderColor: 'rgba(0, 255, 255, 1)',
            borderWidth: 4,
            shadowColor: 'rgba(50, 255, 255, 1)',
            shadowBlur: 20,
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            areaColor: getHighlightColor(data.value)
          }
        };
      } else {
        return {
          name: area.name,
          itemStyle: {
            borderColor: 'rgba(0, 210, 255, 0.2)',
            borderWidth: 1,
          }
        };
      }
    });
  }
}

// 处理地图点击事件
const handleMapClick = (params) => {
  if (params.componentType === 'series' && params.seriesType === 'map') {
    // 更新选中的区域到共享状态
    setSelectedDistrict(params.name)
    // 更新图表配置
    updateChartOption()
  }
}

// 重置高亮状态
const resetHighlight = () => {
  setSelectedDistrict(null)
  updateChartOption()
}


// 显示常州市tooltip
const showCityTooltip = () => {
  if (chart) {
    // seriesIndex: 1（假设隐藏点series在series数组第2个，需与option保持一致）
    chart.dispatchAction({
      type: 'showTip',
      seriesIndex: 1,
      dataIndex: 0
    });
  }
}

// 隐藏常州市tooltip
const hideCityTooltip = () => {
  if (chart) {
    chart.dispatchAction({
      type: 'hideTip',
      seriesIndex: 1,
      dataIndex: 0
    });
  }
}

// 重设图表大小
const resizeChart = () => {
  chart && chart.resize()
}

// 组件挂载后初始化图表
onMounted(() => {
  initChart()
})

// 监听网络类型变化，更新图表
watch(() => props.networkType, () => {
  if (chart) {
    updateChartOption()
  }
})

// 监听覆盖率数据加载状态
watch(() => coverageData.isLoaded, (isLoaded) => {
  if (isLoaded && chart) {
    updateChartOption()
  }
})

// 组件卸载前清理资源
onUnmounted(() => {
  if (chart) {
    window.removeEventListener('resize', resizeChart)
    chart.off('click', handleMapClick)
    chart.dispose()
    chart = null
  }
})
</script>

<style scoped>
/* 添加网格背景效果 */
.chart-container {
  background-image:
    linear-gradient(rgba(0, 210, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 210, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}
</style>
