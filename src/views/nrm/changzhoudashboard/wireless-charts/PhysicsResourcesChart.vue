<template>
  <div ref="chartDom" class="w-full h-full"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';
import {
  selectedDistrict,
  getPhysicalResourceData,
  physicalResourceData,
  loadPhysicalResourceData
} from '@/store/changzhou/wirelessStore';

const chartDom = ref(null);
let chart = null;

// 更新图表数据
const updateChartData = () => {
  if (!chart) return;

  const data = getPhysicalResourceData();

  // 获取当前区域名称
  const title = selectedDistrict.value ?
    `${selectedDistrict.value} - 物理资源` :
    '全市 - 物理资源';

  chart.setOption({
    title: {
      text: title,
      top: 5,
      left: 'center',
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12,
        fontWeight: 'normal'
      }
    },
    series: [
      {
        name: '数量',
        data: data
      }
    ]
  });
};

onMounted(() => {
  // 确保数据已加载
  if (!physicalResourceData.isLoaded) {
    loadPhysicalResourceData();
  }

  chart = echarts.init(chartDom.value, null, { renderer: 'svg' });
  const option = {
    title: {
      text: '全市 - 物理资源',
      left: 'center',
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12,
        fontWeight: 'normal'
      }
    },
    grid: {
      top: 60, // 为标题留出空间
      bottom: -5,
      left: 5,
      right: 5,
      containLabel: true
    },
    legend: {
      top: 25,
      textStyle: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 10
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(0, 24, 48, 0.8)',
      borderColor: 'rgba(0, 210, 255, 0.5)',
      borderWidth: 1,
      textStyle: {
        fontSize: 12,
        color: 'rgba(255, 255, 255, 0.9)'
      }
    },
    xAxis: {
      type: 'category',
      data: ['BBU5g', 'BBU4g', 'RRU5g', 'RRU4g', '基站', '直放站近端', '直放站远端'],
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.7)',
        rotate: 30,
        fontSize: 10
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 210, 255, 0.1)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.7)'
      }
    },
    series: [
      {
        name: '数量',
        type: 'bar',
        data: getPhysicalResourceData(), // 初始化时使用全市数据
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(0, 255, 255, 0.8)' },
            { offset: 1, color: 'rgba(0, 255, 255, 0.3)' }
          ])
        },
        barWidth: '40%',
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(0, 255, 255, 1)' },
              { offset: 1, color: 'rgba(0, 255, 255, 0.5)' }
            ])
          }
        }
      }
    ]
  };

  chart.setOption(option);

  // 添加窗口大小变化监听
  window.addEventListener('resize', () => {
    chart.resize();
  });
});

// 监听选中区域变化
watch(() => selectedDistrict.value, () => {
  updateChartData();
});

// 监听物理资源数据加载状态
watch(() => physicalResourceData.isLoaded, (isLoaded) => {
  if (isLoaded) {
    updateChartData();
  }
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
  }
  window.removeEventListener('resize', () => {
    chart.resize();
  });
});
</script>
