<template>
  <div class="absolute inset-0 flex items-center justify-center">
    <div ref="chartDom" class="w-[95%] h-[95%] relative"></div>

    <!-- 添加全选按钮 -->
    <button @click="resetHighlight"
      class="bg-blue-500/50 hover:bg-blue-600/50 text-white px-2 py-1 rounded backdrop-blur-sm text-xs absolute top-10 right-15">
      全选
    </button>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import { selectedDistrict, setSelectedDistrict } from '@/store/changzhou/wiredStore'

// 常州市行政区列表
const districtList = ['武进区', '新北区', '天宁区', '钟楼区', '经开区', '溧阳市', '金坛区']

const chartDom = ref(null)
let chart = null

// 初始化图表
const initChart = async () => {
  try {
    // 加载常州市地图数据
    const mapData = await fetch('/changzhou/changzhou.json').then(res => res.json());

    // 注册地图数据
    echarts.registerMap('changzhou', mapData);

    // 创建图表实例
    chart = echarts.init(chartDom.value, null, { renderer: 'svg' });

    // 应用初始配置
    updateChartOption();

    // 添加点击事件监听
    chart.on('click', handleMapClick);

    // 响应窗口大小变化
    window.addEventListener('resize', resizeChart);
  } catch (error) {
    console.error('加载常州市地图数据失败:', error);
  }
}

// 更新图表配置
const updateChartOption = () => {
  // 根据选中区域创建高亮配置
  const regions = getRegionsConfig();

  // 创建包含标签大小差异的数据
  const seriesData = districtList.map(area => {
    // 如果选中了某个区域，并且当前区域就是选中的区域
    if (selectedDistrict.value !== null && area === selectedDistrict.value) {
      return {
        name: area,
        // 为选中区域应用特殊的标签样式
        label: {
          show: true,
          color: '#fff',
          fontSize: 14,
          fontWeight: 'bold'
        }
      };
    }
    // 非选中区域保持原样
    return { name: area };
  });

  const option = {
    backgroundColor: '#001529',
    title: {
      text: '城域网及传输覆盖',
      left: '2%',
      top: '2%',
      textStyle: {
        fontSize: 14,
        color: '#fff'
      }
    },
    tooltip: {
      show: false
    },
    geo: {
      map: 'changzhou',
      roam: true,
      zoom: 1.2,
      itemStyle: {
        areaColor: 'rgba(255, 204, 0, 0.4)', // 黄色基础色调
        borderColor: 'rgba(255, 215, 0, 0.5)',
        borderWidth: 1
      },
      label: {
        show: false
      },
      emphasis: {
        itemStyle: {
          areaColor: 'rgba(255, 204, 0, 0.4)', // 保持与普通状态相同的黄色
          borderColor: 'rgba(255, 215, 0, 0.5)',
          borderWidth: 1
        },
        label: {
          show: false
        }
      },
      regions: regions,
      tooltip: {
        show: false
      }
    },
    series: [
      {
        name: '有线覆盖',
        type: 'map',
        map: 'changzhou',
        roam: true,
        zoom: 1.2,
        itemStyle: {
          areaColor: 'rgba(255, 204, 0, 0.4)', // 黄色基础色调
          borderColor: 'rgba(255, 215, 0, 0.5)',
          borderWidth: 1
        },
        label: {
          show: true,
          color: '#fff',
          fontSize: 12
        },
        emphasis: {
          label: {
            color: '#fff',
            fontSize: 14,
            fontWeight: 'bold'
          },
          itemStyle: {
            areaColor: 'rgba(255, 215, 0, 0.6)' // 鼠标悬停时显示更亮的黄色
          }
        },
        data: seriesData,
        tooltip: {
          show: true,
          formatter: params => {
            return params.name
          }
        }
      }
    ]
  };

  chart.setOption(option, true);
};

// 获取区域高亮配置
const getRegionsConfig = () => {
  if (selectedDistrict.value === null) {
    // 初始状态：所有区域边缘都高亮
    return districtList.map(area => ({
      name: area,
      itemStyle: {
        borderColor: 'rgba(255, 165, 0, 1)', // 橙色边框
        borderWidth: 4,
        shadowColor: 'rgba(255, 140, 0, 1)', // 深橙色阴影
        shadowBlur: 20,
        shadowOffsetX: 0,
        shadowOffsetY: 0
      }
    }));
  } else {
    // 选中某个区域：只有选中的区域边缘高亮
    return districtList.map(area => {
      if (area === selectedDistrict.value) {
        return {
          name: area,
          itemStyle: {
            borderColor: 'rgba(255, 165, 0, 1)', // 橙色边框
            borderWidth: 4,
            shadowColor: 'rgba(255, 140, 0, 1)', // 深橙色阴影
            shadowBlur: 20,
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            areaColor: 'rgba(255, 215, 0, 0.8)' // 更亮的黄色
          }
        };
      } else {
        return {
          name: area,
          itemStyle: {
            borderColor: 'rgba(255, 215, 0, 0.3)', // 淡黄色边框
            borderWidth: 1,
            areaColor: 'rgba(255, 204, 0, 0.4)' // 保持与基础黄色一致
          }
        };
      }
    });
  }
};

// 处理地图点击事件
const handleMapClick = (params) => {
  if (params.componentType === 'series' && params.seriesType === 'map') {
    // 更新选中的区域到共享状态
    setSelectedDistrict(params.name);
    // 更新图表配置
    updateChartOption();
  }
};

// 重置高亮状态
const resetHighlight = () => {
  setSelectedDistrict(null);
  updateChartOption();
};

// 重设图表大小
const resizeChart = () => {
  chart && chart.resize()
}

// 在组件挂载后初始化图表
onMounted(initChart)

// 监听选中区域变化
watch(() => selectedDistrict.value, () => {
  if (chart) {
    updateChartOption();
  }
});

// 在组件卸载前清理资源
onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('resize', resizeChart)
  // 移除点击事件监听
  if (chart) {
    chart.off('click', handleMapClick)
    // 销毁图表实例
    chart.dispose()
    chart = null
  }
})
</script>

<style scoped>
/* 添加网格背景效果 */
.chart-container {
  background-image:
    linear-gradient(rgba(0, 210, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 210, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}
</style>
