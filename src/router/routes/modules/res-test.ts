import type { AppRouteModule } from '@/router/types';
// import ProtectionGroups from '@/views/nrm/res-test/dual-routing/ProtectionGroups.vue';
import ProtectionScenarios from '@/views/nrm/res-test/dual-routing/ProtectionScenarios.vue';
import RiskList from '@/views/nrm/res-test/dual-routing/RiskList.vue';
import RiskDetail from '@/views/nrm/res-test/dual-routing/RiskDetail.vue';
import Workspace from '@/views/nrm/res-test/dual-routing/Workspace.vue';
import { LAYOUT } from '@/router/constant';

const develop: AppRouteModule = {
  path: '/nrm/dualrouting',
  name: 'dualrouting',
  component: LAYOUT,
  meta: {
    orderNo: 20,
    icon: 'ion:extension-puzzle-outline',
    title: '双路由管理平台',
    ignoreKeepAlive: false,
    keepalive: true,
    // hideMenu: true
  },
  children: [
    {
      path: 'workspace',
      name: 'DualRoutingWorkspace',
      component: Workspace,
      meta: { title: '工作台',

        roles: ['测试菜单'],

       },

    },

    {
      path: 'protection-scenarios',
      name: 'DualRoutingProtectionScenarios',
      component: ProtectionScenarios,
      meta: { title: '保护组管理' },
    },
    {
      path: 'protection-scenario/single-device-dual-uplink',
      name: 'SingleDeviceDualUplink',
      component: () => import('@/views/nrm/res-test/dual-routing/protection-scenarios/SingleDeviceDualUplink.vue'),
      meta: {
        title: '单设备双上联',
        hideMenu: true,
        currentActiveMenu: '/nrm/dualrouting/protection-scenarios',
      },
    },
    {
      path: 'protection-scenario/dual-device-dual-route',
      name: 'DualDeviceDualRoute',
      component: () => import('@/views/nrm/res-test/dual-routing/protection-scenarios/DualDeviceDualRoute.vue'),
      meta: {
        title: '设备对双路由',
        hideMenu: true,
        currentActiveMenu: '/nrm/dualrouting/protection-scenarios',
      },
    },
    {
      path: 'protection-scenario/dual-device-triple-route',
      name: 'DualDeviceTripleRoute',
      component: () => import('@/views/nrm/res-test/dual-routing/protection-scenarios/DualDeviceTripleRoute.vue'),
      meta: {
        title: '设备对三路由',
        hideMenu: true,
        currentActiveMenu: '/nrm/dualrouting/protection-scenarios',

      },
    },
    {
      path: 'protection-scenario/ring-network',
      name: 'RingNetwork',
      component: () => import('@/views/nrm/res-test/dual-routing/protection-scenarios/RingNetwork.vue'),
      meta: {
        title: '环形组网',
        hideMenu: true,
        currentActiveMenu: '/nrm/dualrouting/protection-scenarios',

      },
    },



    // {
    //   path: 'treepic',
    //   name: 'treepic',
    //   component: () => import('@/views/nrm/res-test/dual-routing/relation-graph-topology/component/treePic.vue'),
    //   meta: {
    //     title: '树状网络 (Relation Graph)',
    //     icon: 'ion:git-network-outline'
    //   },
    // },


    {
      path: 'risks',
      name: 'DualRoutingRisks',
      component: RiskList,
      meta: { title: '风险管理' ,
        roles: ['测试菜单'],

      },
    },
    {
      path: 'risk-detail/:id',
      name: 'DualRoutingRiskDetail',
      component: RiskDetail,
      meta: {
        title: '风险详情',
        hideMenu: true,
        currentActiveMenu: '/nrm/dualrouting/risks',

        roles: ['测试菜单'],
      },
    },
    {
      path: 'hazards',
      name: 'DualRoutingHazards',
      component: () => import('@/views/nrm/res-test/dual-routing/HazardList.vue'),
      meta: {
        title: '隐患管理',
        // icon: 'warning-outlined',

        roles: ['测试菜单'],
      },
    },
    {
      path: 'hazard-detail/:id',
      name: 'DualRoutingHazardDetail',
      component: () => import('@/views/nrm/res-test/dual-routing/HazardDetail.vue'),
      meta: {
        title: '隐患详情',
        hideMenu: true,
        currentActiveMenu: '/nrm/dualrouting/hazards',

      },
    },
    {
      path: 'governance-plans',
      name: 'DualRoutingGovernancePlans',
      component: () => import('@/views/nrm/res-test/dual-routing/GovernancePlanList.vue'),
      meta: {
        title: '治理方案',
        roles: ['测试菜单'],
      },
    },
    {
      path: 'governance-plan/:id',
      name: 'DualRoutingGovernancePlanDetail',
      component: () => import('@/views/nrm/res-test/dual-routing/GovernancePlanDetail.vue'),
      meta: {
        title: '方案详情',
        hideMenu: true,
        currentActiveMenu: '/nrm/dualrouting/governance-plans',
      },
    },
    {
      path: 'governance-plan/create',
      name: 'DualRoutingGovernancePlanCreate',
      component: () => import('@/views/nrm/res-test/dual-routing/GovernancePlanDetail.vue'),
      meta: {
        title: '新建方案',
        hideMenu: true,
        currentActiveMenu: '/nrm/dualrouting/governance-plans',
      },
    },
    {
      path: 'governance-plan/:id/edit',
      name: 'DualRoutingGovernancePlanEdit',
      component: () => import('@/views/nrm/res-test/dual-routing/GovernancePlanDetail.vue'),
      meta: {
        title: '编辑方案',
        hideMenu: true,
        currentActiveMenu: '/nrm/dualrouting/governance-plans',
      },
    },
    {
      path: 'tasks',
      name: 'DualRoutingTasks',
      component: () => import('@/views/nrm/res-test/dual-routing/TaskList.vue'),
      meta: {
        title: '任务管理',
        currentActiveMenu: '/nrm/dualrouting/tasks',
        roles: ['测试菜单'],
      }
    },

    {
      path: 'tasks/create',
      name: 'DualRoutingTasksCreate',
      component: () => import('@/views/nrm/res-test/dual-routing/TaskDetail.vue'),
      meta: {
        title: '新建任务',
        hideMenu: true,
        currentActiveMenu: '/nrm/dualrouting/tasks',
      },
    },
    {
      path: 'tasks/:id/edit',
      name: 'DualRoutingTasksEdit',
      component: () => import('@/views/nrm/res-test/dual-routing/TaskDetail.vue'),
      meta: {
        title: '编辑任务',
        hideMenu: true,
        currentActiveMenu: '/nrm/dualrouting/tasks',
      },
    },
    {
      path: 'tasks/:id',
      name: 'DualRoutingTasksDetail',
      component: () => import('@/views/nrm/res-test/dual-routing/TaskDetail.vue'),
      meta: {
        title: '任务详情',
        hideMenu: true,
        currentActiveMenu: '/nrm/dualrouting/tasks',
      },
    },
    {
      path: 'operation-logs',
      name: 'OperationLogs',
      component: () => import('@/views/nrm/res-test/dual-routing/OperationLogs.vue'),
      meta: {
        title: '操作日志',
        currentActiveMenu: '/nrm/dualrouting/operation-logs',
        roles: ['测试菜单'],
      },
    },
    {
      path: 'statistics',
      name: 'DualRoutingStatistics',
      component: () => import('@/views/nrm/res-test/dual-routing/Statistics.vue'),
      meta: {
        title: '统计分析',
        currentActiveMenu: '/nrm/dualrouting/statistics',
        roles: ['测试菜单'],
      },
    },

    {
      path: 'device-err-detail',
      name: 'DeviceErrDetail',
      component: () => import('@/views/nrm/res-test/dual-routing/components/DeviceErrDetail.vue'),
      meta: {
        title: '设备问题详情',
        hideMenu: true,
        currentActiveMenu: '/nrm/dualrouting/protection-scenarios',
      },
    },
    {
      path: 'device-pair-err-detail',
      name: 'DevicePairErrDetail',
      component: () => import('@/views/nrm/res-test/dual-routing/components/DevicePairErrDetail.vue'),
      meta: {
        title: '设备对双路由问题详情',
        hideMenu: true,
        currentActiveMenu: '/nrm/dualrouting/protection-scenarios',
      },
    },
    {
      path: 'device-pair-triple-route-err-detail',
      name: 'DevicePairTripleRouteErrDetail',
      component: () => import('@/views/nrm/res-test/dual-routing/components/DevicePairTripleRouteErrDetail.vue'),
      meta: {
        title: '设备对三路由问题详情',
        hideMenu: true,
        currentActiveMenu: '/nrm/dualrouting/protection-scenarios',
      },
    },
    {
      path: 'a-ring-device-detail',
      name: 'ARingDeviceDetail',
      component: () => import('@/views/nrm/res-test/dual-routing/components/ARingDeviceDetail.vue'),
      meta: {
        title: 'A环设备详情',
        hideMenu: true,
        currentActiveMenu: '/nrm/dualrouting/protection-scenarios',
      },
    },
    {
      path: 'history-version-list',
      name: 'HistoryVersionList',
      component: () => import('@/views/nrm/res-test/dual-routing/components/HistoryVersionList.vue'),
      meta: {
        title: '历史版本清单',
      },
    },
    {
      path: 'history-version-view',
      name: 'HistoryVersionView',
      component: () => import('@/views/nrm/res-test/dual-routing/components/HistoryVersionView.vue'),
      meta: {
        title: '历史版本查看',
        hideMenu: true,
        currentActiveMenu: '/nrm/dualrouting/history-version-list',
      },
    },
    // {
    //   path: 'optical-path-map-demo',
    //   name: 'OpticalPathMapDemo',
    //   component: () => import('@/views/nrm/res-test/dual-routing/components/OpticalPathMapDemo.vue'),
    //   meta: {
    //     title: '光路地图组件示例',
    //     // hideMenu: true, // 设置为false可以在菜单中显示
    //     // currentActiveMenu: '/nrm/dualrouting/protection-scenarios',
    //   },
    // },


    // {
    //   path: 'statistics',
    //   name: 'DualRoutingStatistics',
    //   component: Statistics,
    //   meta: { title: '统计分析' ,
    //     hideMenu: true,}
    // },
  ]
};

export default develop;
