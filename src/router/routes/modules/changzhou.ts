import type { AppRouteModule } from '@/router/types';

import { LAYOUT } from '@/router/constant';

const develop: AppRouteModule = {
  path: '/nrm/changzhou',
  name: 'nrm-changzhou',
  component: LAYOUT,
  redirect: '/nrm/changzhou/wireless',
  meta: {
    orderNo: 30,
    title: '常州资源视图',
    ignoreKeepAlive: false,
    keepalive: true,
    // 只有常州的角色才能看到"常州大屏"
    roles: ['常州开发角色'],
    // hideMenu: true
  },
  children: [
    {
      path: 'idc',
      name: 'idc-dashboard',
      component: () => import('@/views/nrm/changzhoudashboard/IDCDashboard.vue'),

      meta: {
        title: '局站及IDC资源',
        ignoreKeepAlive: false,
        ignoreAuth: true,
        keepalive: true,
        // hideMenu: true,
      },
    },
    {
      path: 'cloud',
      name: 'cloud-dashboard',
      component: () => import('@/views/nrm/changzhoudashboard/CloudDashboard.vue'),

      meta: {
        title: '云资源',
        ignoreKeepAlive: false,
        ignoreAuth: true,
        keepalive: true,
        // hideMenu: true,
      },
    },
    {
      path: 'wireless',
      name: 'wireless-dashboard',
      component: () => import('@/views/nrm/changzhoudashboard/WirelessDashboard.vue'),

      meta: {
        title: '无线资源',
        ignoreKeepAlive: false,
        ignoreAuth: true,
        keepalive: true,
        // hideMenu: true,
      },
    },
    {
      path: 'wired',
      name: 'wired-dashboard',
      component: () => import('@/views/nrm/changzhoudashboard/WiredDashboard.vue'),

      meta: {
        title: '城域网及传输资源',
        ignoreKeepAlive: false,
        ignoreAuth: true,
        keepalive: true,
        // hideMenu: true,
      },
    },
    {
      path: 'line',
      name: 'line-dashboard',
      component: () => import('@/views/nrm/changzhoudashboard/LineDashboard.vue'),

      meta: {
        title: '线路资源',
        ignoreKeepAlive: false,
        ignoreAuth: true,
        keepalive: true,
        // hideMenu: true,
      },
    },
  ],
};

export default develop;
