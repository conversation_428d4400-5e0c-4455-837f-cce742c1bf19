import { defHttp } from '@/utils/http/axios';
import { ref } from 'vue';
import { buildShardingCode } from '@/api/unified-api';
import * as dualRoutingApi from '@/api/dual-routing';

/**
 * 统一API调用钩子
 * 参考useInfo实现，进一步封装callUnifiedApi方法
 */

interface UnifiedApiParams {
  apiCode: string;
  version: string;
  shardingCode: string;
  param: Record<string, any>;
}

interface UseUnifiedApiOption {
  rootPath?: string;
  defaultCityCode?: string;
}

export const useUnifiedApi = (options: UseUnifiedApiOption = {}) => {
  const loading = ref(false);
  const rootPath = options.rootPath == null ? '' : options.rootPath;
  const defaultCityCode = options.defaultCityCode || 'NJ'; // 默认南京
  const result = ref<any>(null);
  const error = ref<Error | null>(null);

  /**
   * 调用统一API
   * @param apiCode API代码
   * @param version API版本
   * @param params 请求参数
   * @param cityCode 城市代码，用于构建shardingCode
   * @returns
   */
  const callApi = async (
    apiCode: string,
    version: string,
    params: Record<string, any> = {},
    cityCode: string = defaultCityCode
  ) => {
    loading.value = true;
    error.value = null;

    try {
      // 构建请求参数
      const requestParams: UnifiedApiParams = {
        apiCode,
        version,
        shardingCode: buildShardingCode(cityCode),
        param: params
      };

      // 发送请求
      const response = await defHttp.post(
        {
          url: rootPath + '/api/apiconverge/query',
          data: requestParams
        },
        { isTransformResponse: false, errorMessageMode: 'modal' }
      );

      result.value = response;
      return response;
    } catch (e) {
      error.value = e as Error;
      console.error('统一API调用出错:', e);
      throw e;
    } finally {
      loading.value = false;
    }
  };

  // 这些API已经移动到 src/api/dual-routing 目录下的相应文件中

  /**
   * 获取端口和光路数据
   * @param cityCode 城市代码
   * @param params 查询参数
   * @returns
   */
  const getPortAndOpticalData = async (cityCode: string, params: Record<string, any> = {}) => {
    // 模拟数据，实际应调用相应的API
    return {
      data: {
        ports: [],
        opticalPaths: []
      }
    };
  };

  /**
   * 通用API请求方法
   * @param options API请求选项
   * @returns
   */
  const request = async (options: {
    apiCode: string;
    apiVersion: string;
    schemaCode: string;
    apiParams: Record<string, any>;
  }) => {
    return callApi(
      options.apiCode,
      options.apiVersion,
      options.apiParams,
      options.schemaCode
    );
  };

  // 这些API已经移动到 src/api/dual-routing/dual-device-dual-route.ts 文件中

  // 这些API已经移动到 src/api/dual-routing/triple-route.ts 文件中

  // 使用新的API结构
  return {
    loading,
    result,
    error,
    callApi,
    request,
    getPortAndOpticalData,

    // 单设备双上联相关API
    querySlyDevices: dualRoutingApi.querySlyDevices,
    queryImportantOltDevices: dualRoutingApi.queryImportantOltDevices, // 添加大客户OLT查询API
    getDeviceBasicInfo: dualRoutingApi.getDeviceBasicInfo,
    getRouteErrors: dualRoutingApi.getRouteErrors,
    getDeviceBackupErrors: dualRoutingApi.getDeviceBackupErrors,
    getNetsourceErrors: dualRoutingApi.getNetsourceErrors,
    getDeviceGlInfo: dualRoutingApi.getDeviceGlInfo,

    // 保护组相关API
    queryProtectionGroupDevices: dualRoutingApi.queryProtectionGroupDevices,
    queryProtectionGroupDetails: dualRoutingApi.queryProtectionGroupDetails,

    // 设备对双路由相关API
    queryDevicePairs: dualRoutingApi.queryDevicePairs,
    getDevicePairInfo: dualRoutingApi.getDevicePairInfo,
    getPairRouteErrors: dualRoutingApi.getPairRouteErrors,
    getPairDeviceErrors: dualRoutingApi.getPairDeviceErrors,
    getPairNetsourceErrors: dualRoutingApi.getPairNetsourceErrors,

    // 设备对三路由相关API
    getTripleRoutePairInfo: dualRoutingApi.getTripleRoutePairInfo,
    getTripleRoutePairErrors: dualRoutingApi.getTripleRoutePairErrors,
    getTripleRoutePairDeviceErrors: dualRoutingApi.getTripleRoutePairDeviceErrors,
    getTripleRoutePairNetsourceErrors: dualRoutingApi.getTripleRoutePairNetsourceErrors,
    queryTripleRoutePairs: dualRoutingApi.queryTripleRoutePairs,

    // 历史版本相关API
    queryHistoryVersions: dualRoutingApi.queryHistoryVersions,
    queryProtectionGroupVersions: dualRoutingApi.queryProtectionGroupVersions,
    getProtectionGroupVersionDetail: dualRoutingApi.getProtectionGroupVersionDetail,
    getProtectionGroupVersionProblems: dualRoutingApi.getProtectionGroupVersionProblems,
    getProtectionGroupVersionMapData: dualRoutingApi.getProtectionGroupVersionMapData,
    updateHistoryVersionDescription: dualRoutingApi.updateHistoryVersionDescription,
    deleteHistoryVersion: dualRoutingApi.deleteHistoryVersion
  };
};
