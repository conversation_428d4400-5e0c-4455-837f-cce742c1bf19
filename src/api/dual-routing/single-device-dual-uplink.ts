import { callUnifiedApi, buildShardingCode } from '../unified-api';

/**
 * 查询双路由设备列表
 * @param cityCode 城市代码
 * @param params 查询参数
 * @returns
 */
export const querySlyDevices = async (cityCode: string, params: any) => {
  return callUnifiedApi({
    apiCode: 'SLY_DEVICE_BASE_INFO_QUERY',
    version: 'V20250419121012578',
    shardingCode: buildShardingCode(cityCode),
    param: params
  }, '/graph-rest-api');
};

/**
 * 查询大客户OLT设备列表
 * @param cityCode 城市代码
 * @param params 查询参数
 * @returns
 */
export const queryImportantOltDevices = async (cityCode: string, params: any) => {
  return callUnifiedApi({
    apiCode: 'SLY_OLT_IMPORTANT_QUERY',
    version: 'V20250601000000000',
    shardingCode: buildShardingCode(cityCode),
    param: params
  }, '/graph-rest-api');
};

/**
 * 获取设备光路信息
 * @param cityCode 城市代码
 * @param params 查询参数
 * @returns
 */
export const getDeviceGlInfo = async (cityCode: string, params: any) => {
  return callUnifiedApi({
    apiCode: 'SLY_DEVICE_GL_INFO_QUERY',
    version: 'V20250520020000000',
    shardingCode: buildShardingCode(cityCode),
    param: params
  }, '/graph-rest-api');
};

/**
 * 获取设备基本信息
 * @param cityCode 城市代码
 * @param params 查询参数
 * @returns
 */
export const getDeviceBasicInfo = async (cityCode: string, params: any) => {
  return callUnifiedApi({
    apiCode: 'SLY_DEVICE_BASE_INFO_QUERY',
    version: 'V20250419121012578',
    shardingCode: buildShardingCode(cityCode),
    param: params
  }, '/graph-rest-api');
};

/**
 * 获取路由错误数据
 * @param cityCode 城市代码
 * @param params 查询参数
 * @returns
 */
export const getRouteErrors = async (cityCode: string, params: any) => {
  return callUnifiedApi({
    apiCode: 'SLY_DWDM_GL_ROUTE_ERR_QUERY',
    version: 'V20250420161149826',
    shardingCode: buildShardingCode(cityCode),
    param: params
  }, '/graph-rest-api');
};

/**
 * 获取设备备份错误数据
 * @param cityCode 城市代码
 * @param params 查询参数
 * @returns
 */
export const getDeviceBackupErrors = async (cityCode: string, params: any) => {
  return callUnifiedApi({
    apiCode: 'SLY_DEVICE_GL_BK_ERR_QUERY',
    version: 'V20250420161247456',
    shardingCode: buildShardingCode(cityCode),
    param: params
  }, '/graph-rest-api');
};

/**
 * 获取网络源错误数据
 * @param cityCode 城市代码
 * @param params 查询参数
 * @returns
 */
export const getNetsourceErrors = async (cityCode: string, params: any) => {
  return callUnifiedApi({
    apiCode: 'SLY_DEVICE_NETSOURCE_QUERY',
    version: 'V20250420161402784',
    shardingCode: buildShardingCode(cityCode),
    param: params
  }, '/graph-rest-api');
};
