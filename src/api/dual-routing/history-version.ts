import { callUnifiedApi, buildShardingCode } from '../unified-api';

/**
 * 查询历史版本清单
 * @param cityCode 城市代码
 * @param params 查询参数
 * @returns
 */
export const queryHistoryVersions = async (cityCode: string, params: any) => {
  return callUnifiedApi({
    apiCode: 'SLY_MAP_HISTORY_QUERY',
    version: 'V20250516110111700',
    shardingCode: buildShardingCode(cityCode),
    param: params
  }, '/graph-rest-api');
};

/**
 * 查询保护组历史版本列表
 * @param cityCode 城市代码
 * @param params 查询参数
 * @returns
 */
export const queryProtectionGroupVersions = async (cityCode: string, params: any) => {
  return callUnifiedApi({
    apiCode: 'SLY_PROTECTION_GROUP_VERSIONS_QUERY',
    version: 'V20250510000000001', // 需要注册后更新为实际版本号
    shardingCode: buildShardingCode(cityCode),
    param: params
  }, '/graph-rest-api');
};

/**
 * 获取保护组历史版本详情
 * @param cityCode 城市代码
 * @param params 查询参数
 * @returns
 */
export const getProtectionGroupVersionDetail = async (cityCode: string, params: any) => {
  return callUnifiedApi({
    apiCode: 'SLY_PROTECTION_GROUP_VERSION_DETAIL_QUERY',
    version: 'V20250510000000002', // 需要注册后更新为实际版本号
    shardingCode: buildShardingCode(cityCode),
    param: params
  }, '/graph-rest-api');
};

/**
 * 获取保护组历史版本问题列表
 * @param cityCode 城市代码
 * @param params 查询参数
 * @returns
 */
export const getProtectionGroupVersionProblems = async (cityCode: string, params: any) => {
  return callUnifiedApi({
    apiCode: 'SLY_PROTECTION_GROUP_VERSION_PROBLEMS_QUERY',
    version: 'V20250510000000003', // 需要注册后更新为实际版本号
    shardingCode: buildShardingCode(cityCode),
    param: params
  }, '/graph-rest-api');
};

/**
 * 获取保护组历史版本地图数据
 * @param cityCode 城市代码
 * @param params 查询参数
 * @returns
 */
export const getProtectionGroupVersionMapData = async (cityCode: string, params: any) => {
  return callUnifiedApi({
    apiCode: 'SLY_PROTECTION_GROUP_VERSION_MAP_DATA_QUERY',
    version: 'V20250510000000004', // 需要注册后更新为实际版本号
    shardingCode: buildShardingCode(cityCode),
    param: params
  }, '/graph-rest-api');
};
